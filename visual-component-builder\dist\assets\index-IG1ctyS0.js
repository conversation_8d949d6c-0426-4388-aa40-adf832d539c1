(function(){const l=document.createElement("link").relList;if(l&&l.supports&&l.supports("modulepreload"))return;for(const c of document.querySelectorAll('link[rel="modulepreload"]'))r(c);new MutationObserver(c=>{for(const d of c)if(d.type==="childList")for(const f of d.addedNodes)f.tagName==="LINK"&&f.rel==="modulepreload"&&r(f)}).observe(document,{childList:!0,subtree:!0});function u(c){const d={};return c.integrity&&(d.integrity=c.integrity),c.referrerPolicy&&(d.referrerPolicy=c.referrerPolicy),c.crossOrigin==="use-credentials"?d.credentials="include":c.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function r(c){if(c.ep)return;c.ep=!0;const d=u(c);fetch(c.href,d)}})();var Vm=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function og(a){return a&&a.__esModule&&Object.prototype.hasOwnProperty.call(a,"default")?a.default:a}var yo={exports:{}},ul={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var _m;function rb(){if(_m)return ul;_m=1;var a=Symbol.for("react.transitional.element"),l=Symbol.for("react.fragment");function u(r,c,d){var f=null;if(d!==void 0&&(f=""+d),c.key!==void 0&&(f=""+c.key),"key"in c){d={};for(var m in c)m!=="key"&&(d[m]=c[m])}else d=c;return c=d.ref,{$$typeof:a,type:r,key:f,ref:c!==void 0?c:null,props:d}}return ul.Fragment=l,ul.jsx=u,ul.jsxs=u,ul}var Bm;function ob(){return Bm||(Bm=1,yo.exports=rb()),yo.exports}var y=ob(),vo={exports:{}},ot={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Um;function cb(){if(Um)return ot;Um=1;var a=Symbol.for("react.transitional.element"),l=Symbol.for("react.portal"),u=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),d=Symbol.for("react.consumer"),f=Symbol.for("react.context"),m=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),g=Symbol.for("react.memo"),v=Symbol.for("react.lazy"),x=Symbol.iterator;function S(A){return A===null||typeof A!="object"?null:(A=x&&A[x]||A["@@iterator"],typeof A=="function"?A:null)}var V={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},j=Object.assign,Y={};function X(A,G,P){this.props=A,this.context=G,this.refs=Y,this.updater=P||V}X.prototype.isReactComponent={},X.prototype.setState=function(A,G){if(typeof A!="object"&&typeof A!="function"&&A!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,A,G,"setState")},X.prototype.forceUpdate=function(A){this.updater.enqueueForceUpdate(this,A,"forceUpdate")};function Q(){}Q.prototype=X.prototype;function C(A,G,P){this.props=A,this.context=G,this.refs=Y,this.updater=P||V}var M=C.prototype=new Q;M.constructor=C,j(M,X.prototype),M.isPureReactComponent=!0;var _=Array.isArray,O={H:null,A:null,T:null,S:null,V:null},B=Object.prototype.hasOwnProperty;function Z(A,G,P,$,tt,ft){return P=ft.ref,{$$typeof:a,type:A,key:G,ref:P!==void 0?P:null,props:ft}}function K(A,G){return Z(A.type,G,void 0,void 0,void 0,A.props)}function F(A){return typeof A=="object"&&A!==null&&A.$$typeof===a}function ut(A){var G={"=":"=0",":":"=2"};return"$"+A.replace(/[=:]/g,function(P){return G[P]})}var pt=/\/+/g;function St(A,G){return typeof A=="object"&&A!==null&&A.key!=null?ut(""+A.key):G.toString(36)}function Se(){}function fe(A){switch(A.status){case"fulfilled":return A.value;case"rejected":throw A.reason;default:switch(typeof A.status=="string"?A.then(Se,Se):(A.status="pending",A.then(function(G){A.status==="pending"&&(A.status="fulfilled",A.value=G)},function(G){A.status==="pending"&&(A.status="rejected",A.reason=G)})),A.status){case"fulfilled":return A.value;case"rejected":throw A.reason}}throw A}function Yt(A,G,P,$,tt){var ft=typeof A;(ft==="undefined"||ft==="boolean")&&(A=null);var it=!1;if(A===null)it=!0;else switch(ft){case"bigint":case"string":case"number":it=!0;break;case"object":switch(A.$$typeof){case a:case l:it=!0;break;case v:return it=A._init,Yt(it(A._payload),G,P,$,tt)}}if(it)return tt=tt(A),it=$===""?"."+St(A,0):$,_(tt)?(P="",it!=null&&(P=it.replace(pt,"$&/")+"/"),Yt(tt,G,P,"",function(Ve){return Ve})):tt!=null&&(F(tt)&&(tt=K(tt,P+(tt.key==null||A&&A.key===tt.key?"":(""+tt.key).replace(pt,"$&/")+"/")+it)),G.push(tt)),1;it=0;var Zt=$===""?".":$+":";if(_(A))for(var Tt=0;Tt<A.length;Tt++)$=A[Tt],ft=Zt+St($,Tt),it+=Yt($,G,P,ft,tt);else if(Tt=S(A),typeof Tt=="function")for(A=Tt.call(A),Tt=0;!($=A.next()).done;)$=$.value,ft=Zt+St($,Tt++),it+=Yt($,G,P,ft,tt);else if(ft==="object"){if(typeof A.then=="function")return Yt(fe(A),G,P,$,tt);throw G=String(A),Error("Objects are not valid as a React child (found: "+(G==="[object Object]"?"object with keys {"+Object.keys(A).join(", ")+"}":G)+"). If you meant to render a collection of children, use an array instead.")}return it}function U(A,G,P){if(A==null)return A;var $=[],tt=0;return Yt(A,$,"","",function(ft){return G.call(P,ft,tt++)}),$}function k(A){if(A._status===-1){var G=A._result;G=G(),G.then(function(P){(A._status===0||A._status===-1)&&(A._status=1,A._result=P)},function(P){(A._status===0||A._status===-1)&&(A._status=2,A._result=P)}),A._status===-1&&(A._status=0,A._result=G)}if(A._status===1)return A._result.default;throw A._result}var J=typeof reportError=="function"?reportError:function(A){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var G=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof A=="object"&&A!==null&&typeof A.message=="string"?String(A.message):String(A),error:A});if(!window.dispatchEvent(G))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",A);return}console.error(A)};function rt(){}return ot.Children={map:U,forEach:function(A,G,P){U(A,function(){G.apply(this,arguments)},P)},count:function(A){var G=0;return U(A,function(){G++}),G},toArray:function(A){return U(A,function(G){return G})||[]},only:function(A){if(!F(A))throw Error("React.Children.only expected to receive a single React element child.");return A}},ot.Component=X,ot.Fragment=u,ot.Profiler=c,ot.PureComponent=C,ot.StrictMode=r,ot.Suspense=p,ot.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=O,ot.__COMPILER_RUNTIME={__proto__:null,c:function(A){return O.H.useMemoCache(A)}},ot.cache=function(A){return function(){return A.apply(null,arguments)}},ot.cloneElement=function(A,G,P){if(A==null)throw Error("The argument must be a React element, but you passed "+A+".");var $=j({},A.props),tt=A.key,ft=void 0;if(G!=null)for(it in G.ref!==void 0&&(ft=void 0),G.key!==void 0&&(tt=""+G.key),G)!B.call(G,it)||it==="key"||it==="__self"||it==="__source"||it==="ref"&&G.ref===void 0||($[it]=G[it]);var it=arguments.length-2;if(it===1)$.children=P;else if(1<it){for(var Zt=Array(it),Tt=0;Tt<it;Tt++)Zt[Tt]=arguments[Tt+2];$.children=Zt}return Z(A.type,tt,void 0,void 0,ft,$)},ot.createContext=function(A){return A={$$typeof:f,_currentValue:A,_currentValue2:A,_threadCount:0,Provider:null,Consumer:null},A.Provider=A,A.Consumer={$$typeof:d,_context:A},A},ot.createElement=function(A,G,P){var $,tt={},ft=null;if(G!=null)for($ in G.key!==void 0&&(ft=""+G.key),G)B.call(G,$)&&$!=="key"&&$!=="__self"&&$!=="__source"&&(tt[$]=G[$]);var it=arguments.length-2;if(it===1)tt.children=P;else if(1<it){for(var Zt=Array(it),Tt=0;Tt<it;Tt++)Zt[Tt]=arguments[Tt+2];tt.children=Zt}if(A&&A.defaultProps)for($ in it=A.defaultProps,it)tt[$]===void 0&&(tt[$]=it[$]);return Z(A,ft,void 0,void 0,null,tt)},ot.createRef=function(){return{current:null}},ot.forwardRef=function(A){return{$$typeof:m,render:A}},ot.isValidElement=F,ot.lazy=function(A){return{$$typeof:v,_payload:{_status:-1,_result:A},_init:k}},ot.memo=function(A,G){return{$$typeof:g,type:A,compare:G===void 0?null:G}},ot.startTransition=function(A){var G=O.T,P={};O.T=P;try{var $=A(),tt=O.S;tt!==null&&tt(P,$),typeof $=="object"&&$!==null&&typeof $.then=="function"&&$.then(rt,J)}catch(ft){J(ft)}finally{O.T=G}},ot.unstable_useCacheRefresh=function(){return O.H.useCacheRefresh()},ot.use=function(A){return O.H.use(A)},ot.useActionState=function(A,G,P){return O.H.useActionState(A,G,P)},ot.useCallback=function(A,G){return O.H.useCallback(A,G)},ot.useContext=function(A){return O.H.useContext(A)},ot.useDebugValue=function(){},ot.useDeferredValue=function(A,G){return O.H.useDeferredValue(A,G)},ot.useEffect=function(A,G,P){var $=O.H;if(typeof P=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return $.useEffect(A,G)},ot.useId=function(){return O.H.useId()},ot.useImperativeHandle=function(A,G,P){return O.H.useImperativeHandle(A,G,P)},ot.useInsertionEffect=function(A,G){return O.H.useInsertionEffect(A,G)},ot.useLayoutEffect=function(A,G){return O.H.useLayoutEffect(A,G)},ot.useMemo=function(A,G){return O.H.useMemo(A,G)},ot.useOptimistic=function(A,G){return O.H.useOptimistic(A,G)},ot.useReducer=function(A,G,P){return O.H.useReducer(A,G,P)},ot.useRef=function(A){return O.H.useRef(A)},ot.useState=function(A){return O.H.useState(A)},ot.useSyncExternalStore=function(A,G,P){return O.H.useSyncExternalStore(A,G,P)},ot.useTransition=function(){return O.H.useTransition()},ot.version="19.1.0",ot}var Lm;function ac(){return Lm||(Lm=1,vo.exports=cb()),vo.exports}var W=ac();const Hm=og(W);var bo={exports:{}},rl={},xo={exports:{}},So={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qm;function fb(){return qm||(qm=1,function(a){function l(U,k){var J=U.length;U.push(k);t:for(;0<J;){var rt=J-1>>>1,A=U[rt];if(0<c(A,k))U[rt]=k,U[J]=A,J=rt;else break t}}function u(U){return U.length===0?null:U[0]}function r(U){if(U.length===0)return null;var k=U[0],J=U.pop();if(J!==k){U[0]=J;t:for(var rt=0,A=U.length,G=A>>>1;rt<G;){var P=2*(rt+1)-1,$=U[P],tt=P+1,ft=U[tt];if(0>c($,J))tt<A&&0>c(ft,$)?(U[rt]=ft,U[tt]=J,rt=tt):(U[rt]=$,U[P]=J,rt=P);else if(tt<A&&0>c(ft,J))U[rt]=ft,U[tt]=J,rt=tt;else break t}}return k}function c(U,k){var J=U.sortIndex-k.sortIndex;return J!==0?J:U.id-k.id}if(a.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var d=performance;a.unstable_now=function(){return d.now()}}else{var f=Date,m=f.now();a.unstable_now=function(){return f.now()-m}}var p=[],g=[],v=1,x=null,S=3,V=!1,j=!1,Y=!1,X=!1,Q=typeof setTimeout=="function"?setTimeout:null,C=typeof clearTimeout=="function"?clearTimeout:null,M=typeof setImmediate<"u"?setImmediate:null;function _(U){for(var k=u(g);k!==null;){if(k.callback===null)r(g);else if(k.startTime<=U)r(g),k.sortIndex=k.expirationTime,l(p,k);else break;k=u(g)}}function O(U){if(Y=!1,_(U),!j)if(u(p)!==null)j=!0,B||(B=!0,St());else{var k=u(g);k!==null&&Yt(O,k.startTime-U)}}var B=!1,Z=-1,K=5,F=-1;function ut(){return X?!0:!(a.unstable_now()-F<K)}function pt(){if(X=!1,B){var U=a.unstable_now();F=U;var k=!0;try{t:{j=!1,Y&&(Y=!1,C(Z),Z=-1),V=!0;var J=S;try{e:{for(_(U),x=u(p);x!==null&&!(x.expirationTime>U&&ut());){var rt=x.callback;if(typeof rt=="function"){x.callback=null,S=x.priorityLevel;var A=rt(x.expirationTime<=U);if(U=a.unstable_now(),typeof A=="function"){x.callback=A,_(U),k=!0;break e}x===u(p)&&r(p),_(U)}else r(p);x=u(p)}if(x!==null)k=!0;else{var G=u(g);G!==null&&Yt(O,G.startTime-U),k=!1}}break t}finally{x=null,S=J,V=!1}k=void 0}}finally{k?St():B=!1}}}var St;if(typeof M=="function")St=function(){M(pt)};else if(typeof MessageChannel<"u"){var Se=new MessageChannel,fe=Se.port2;Se.port1.onmessage=pt,St=function(){fe.postMessage(null)}}else St=function(){Q(pt,0)};function Yt(U,k){Z=Q(function(){U(a.unstable_now())},k)}a.unstable_IdlePriority=5,a.unstable_ImmediatePriority=1,a.unstable_LowPriority=4,a.unstable_NormalPriority=3,a.unstable_Profiling=null,a.unstable_UserBlockingPriority=2,a.unstable_cancelCallback=function(U){U.callback=null},a.unstable_forceFrameRate=function(U){0>U||125<U?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):K=0<U?Math.floor(1e3/U):5},a.unstable_getCurrentPriorityLevel=function(){return S},a.unstable_next=function(U){switch(S){case 1:case 2:case 3:var k=3;break;default:k=S}var J=S;S=k;try{return U()}finally{S=J}},a.unstable_requestPaint=function(){X=!0},a.unstable_runWithPriority=function(U,k){switch(U){case 1:case 2:case 3:case 4:case 5:break;default:U=3}var J=S;S=U;try{return k()}finally{S=J}},a.unstable_scheduleCallback=function(U,k,J){var rt=a.unstable_now();switch(typeof J=="object"&&J!==null?(J=J.delay,J=typeof J=="number"&&0<J?rt+J:rt):J=rt,U){case 1:var A=-1;break;case 2:A=250;break;case 5:A=1073741823;break;case 4:A=1e4;break;default:A=5e3}return A=J+A,U={id:v++,callback:k,priorityLevel:U,startTime:J,expirationTime:A,sortIndex:-1},J>rt?(U.sortIndex=J,l(g,U),u(p)===null&&U===u(g)&&(Y?(C(Z),Z=-1):Y=!0,Yt(O,J-rt))):(U.sortIndex=A,l(p,U),j||V||(j=!0,B||(B=!0,St()))),U},a.unstable_shouldYield=ut,a.unstable_wrapCallback=function(U){var k=S;return function(){var J=S;S=k;try{return U.apply(this,arguments)}finally{S=J}}}}(So)),So}var Ym;function db(){return Ym||(Ym=1,xo.exports=fb()),xo.exports}var To={exports:{}},ee={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Gm;function hb(){if(Gm)return ee;Gm=1;var a=ac();function l(p){var g="https://react.dev/errors/"+p;if(1<arguments.length){g+="?args[]="+encodeURIComponent(arguments[1]);for(var v=2;v<arguments.length;v++)g+="&args[]="+encodeURIComponent(arguments[v])}return"Minified React error #"+p+"; visit "+g+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(){}var r={d:{f:u,r:function(){throw Error(l(522))},D:u,C:u,L:u,m:u,X:u,S:u,M:u},p:0,findDOMNode:null},c=Symbol.for("react.portal");function d(p,g,v){var x=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:c,key:x==null?null:""+x,children:p,containerInfo:g,implementation:v}}var f=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function m(p,g){if(p==="font")return"";if(typeof g=="string")return g==="use-credentials"?g:""}return ee.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,ee.createPortal=function(p,g){var v=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!g||g.nodeType!==1&&g.nodeType!==9&&g.nodeType!==11)throw Error(l(299));return d(p,g,null,v)},ee.flushSync=function(p){var g=f.T,v=r.p;try{if(f.T=null,r.p=2,p)return p()}finally{f.T=g,r.p=v,r.d.f()}},ee.preconnect=function(p,g){typeof p=="string"&&(g?(g=g.crossOrigin,g=typeof g=="string"?g==="use-credentials"?g:"":void 0):g=null,r.d.C(p,g))},ee.prefetchDNS=function(p){typeof p=="string"&&r.d.D(p)},ee.preinit=function(p,g){if(typeof p=="string"&&g&&typeof g.as=="string"){var v=g.as,x=m(v,g.crossOrigin),S=typeof g.integrity=="string"?g.integrity:void 0,V=typeof g.fetchPriority=="string"?g.fetchPriority:void 0;v==="style"?r.d.S(p,typeof g.precedence=="string"?g.precedence:void 0,{crossOrigin:x,integrity:S,fetchPriority:V}):v==="script"&&r.d.X(p,{crossOrigin:x,integrity:S,fetchPriority:V,nonce:typeof g.nonce=="string"?g.nonce:void 0})}},ee.preinitModule=function(p,g){if(typeof p=="string")if(typeof g=="object"&&g!==null){if(g.as==null||g.as==="script"){var v=m(g.as,g.crossOrigin);r.d.M(p,{crossOrigin:v,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0})}}else g==null&&r.d.M(p)},ee.preload=function(p,g){if(typeof p=="string"&&typeof g=="object"&&g!==null&&typeof g.as=="string"){var v=g.as,x=m(v,g.crossOrigin);r.d.L(p,v,{crossOrigin:x,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0,type:typeof g.type=="string"?g.type:void 0,fetchPriority:typeof g.fetchPriority=="string"?g.fetchPriority:void 0,referrerPolicy:typeof g.referrerPolicy=="string"?g.referrerPolicy:void 0,imageSrcSet:typeof g.imageSrcSet=="string"?g.imageSrcSet:void 0,imageSizes:typeof g.imageSizes=="string"?g.imageSizes:void 0,media:typeof g.media=="string"?g.media:void 0})}},ee.preloadModule=function(p,g){if(typeof p=="string")if(g){var v=m(g.as,g.crossOrigin);r.d.m(p,{as:typeof g.as=="string"&&g.as!=="script"?g.as:void 0,crossOrigin:v,integrity:typeof g.integrity=="string"?g.integrity:void 0})}else r.d.m(p)},ee.requestFormReset=function(p){r.d.r(p)},ee.unstable_batchedUpdates=function(p,g){return p(g)},ee.useFormState=function(p,g,v){return f.H.useFormState(p,g,v)},ee.useFormStatus=function(){return f.H.useHostTransitionStatus()},ee.version="19.1.0",ee}var Xm;function mb(){if(Xm)return To.exports;Xm=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(l){console.error(l)}}return a(),To.exports=hb(),To.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Zm;function pb(){if(Zm)return rl;Zm=1;var a=db(),l=ac(),u=mb();function r(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function c(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function d(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function f(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function m(t){if(d(t)!==t)throw Error(r(188))}function p(t){var e=t.alternate;if(!e){if(e=d(t),e===null)throw Error(r(188));return e!==t?null:t}for(var n=t,i=e;;){var s=n.return;if(s===null)break;var o=s.alternate;if(o===null){if(i=s.return,i!==null){n=i;continue}break}if(s.child===o.child){for(o=s.child;o;){if(o===n)return m(s),t;if(o===i)return m(s),e;o=o.sibling}throw Error(r(188))}if(n.return!==i.return)n=s,i=o;else{for(var h=!1,b=s.child;b;){if(b===n){h=!0,n=s,i=o;break}if(b===i){h=!0,i=s,n=o;break}b=b.sibling}if(!h){for(b=o.child;b;){if(b===n){h=!0,n=o,i=s;break}if(b===i){h=!0,i=o,n=s;break}b=b.sibling}if(!h)throw Error(r(189))}}if(n.alternate!==i)throw Error(r(190))}if(n.tag!==3)throw Error(r(188));return n.stateNode.current===n?t:e}function g(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=g(t),e!==null)return e;t=t.sibling}return null}var v=Object.assign,x=Symbol.for("react.element"),S=Symbol.for("react.transitional.element"),V=Symbol.for("react.portal"),j=Symbol.for("react.fragment"),Y=Symbol.for("react.strict_mode"),X=Symbol.for("react.profiler"),Q=Symbol.for("react.provider"),C=Symbol.for("react.consumer"),M=Symbol.for("react.context"),_=Symbol.for("react.forward_ref"),O=Symbol.for("react.suspense"),B=Symbol.for("react.suspense_list"),Z=Symbol.for("react.memo"),K=Symbol.for("react.lazy"),F=Symbol.for("react.activity"),ut=Symbol.for("react.memo_cache_sentinel"),pt=Symbol.iterator;function St(t){return t===null||typeof t!="object"?null:(t=pt&&t[pt]||t["@@iterator"],typeof t=="function"?t:null)}var Se=Symbol.for("react.client.reference");function fe(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===Se?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case j:return"Fragment";case X:return"Profiler";case Y:return"StrictMode";case O:return"Suspense";case B:return"SuspenseList";case F:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case V:return"Portal";case M:return(t.displayName||"Context")+".Provider";case C:return(t._context.displayName||"Context")+".Consumer";case _:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case Z:return e=t.displayName||null,e!==null?e:fe(t.type)||"Memo";case K:e=t._payload,t=t._init;try{return fe(t(e))}catch{}}return null}var Yt=Array.isArray,U=l.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,k=u.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,J={pending:!1,data:null,method:null,action:null},rt=[],A=-1;function G(t){return{current:t}}function P(t){0>A||(t.current=rt[A],rt[A]=null,A--)}function $(t,e){A++,rt[A]=t.current,t.current=e}var tt=G(null),ft=G(null),it=G(null),Zt=G(null);function Tt(t,e){switch($(it,e),$(ft,t),$(tt,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?rm(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=rm(e),t=om(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}P(tt),$(tt,t)}function Ve(){P(tt),P(ft),P(it)}function Yn(t){t.memoizedState!==null&&$(Zt,t);var e=tt.current,n=om(e,t.type);e!==n&&($(ft,t),$(tt,n))}function Fe(t){ft.current===t&&(P(tt),P(ft)),Zt.current===t&&(P(Zt),nl._currentValue=J)}var oi=Object.prototype.hasOwnProperty,Gn=a.unstable_scheduleCallback,iu=a.unstable_cancelCallback,q0=a.unstable_shouldYield,Y0=a.unstable_requestPaint,He=a.unstable_now,G0=a.unstable_getCurrentPriorityLevel,Yc=a.unstable_ImmediatePriority,Gc=a.unstable_UserBlockingPriority,Dl=a.unstable_NormalPriority,X0=a.unstable_LowPriority,Xc=a.unstable_IdlePriority,Z0=a.log,Q0=a.unstable_setDisableYieldValue,ci=null,de=null;function hn(t){if(typeof Z0=="function"&&Q0(t),de&&typeof de.setStrictMode=="function")try{de.setStrictMode(ci,t)}catch{}}var he=Math.clz32?Math.clz32:F0,K0=Math.log,k0=Math.LN2;function F0(t){return t>>>=0,t===0?32:31-(K0(t)/k0|0)|0}var Cl=256,wl=4194304;function Xn(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Rl(t,e,n){var i=t.pendingLanes;if(i===0)return 0;var s=0,o=t.suspendedLanes,h=t.pingedLanes;t=t.warmLanes;var b=i&134217727;return b!==0?(i=b&~o,i!==0?s=Xn(i):(h&=b,h!==0?s=Xn(h):n||(n=b&~t,n!==0&&(s=Xn(n))))):(b=i&~o,b!==0?s=Xn(b):h!==0?s=Xn(h):n||(n=i&~t,n!==0&&(s=Xn(n)))),s===0?0:e!==0&&e!==s&&(e&o)===0&&(o=s&-s,n=e&-e,o>=n||o===32&&(n&4194048)!==0)?e:s}function fi(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function P0(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Zc(){var t=Cl;return Cl<<=1,(Cl&4194048)===0&&(Cl=256),t}function Qc(){var t=wl;return wl<<=1,(wl&62914560)===0&&(wl=4194304),t}function lu(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function di(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function J0(t,e,n,i,s,o){var h=t.pendingLanes;t.pendingLanes=n,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=n,t.entangledLanes&=n,t.errorRecoveryDisabledLanes&=n,t.shellSuspendCounter=0;var b=t.entanglements,T=t.expirationTimes,R=t.hiddenUpdates;for(n=h&~n;0<n;){var L=31-he(n),q=1<<L;b[L]=0,T[L]=-1;var N=R[L];if(N!==null)for(R[L]=null,L=0;L<N.length;L++){var z=N[L];z!==null&&(z.lane&=-536870913)}n&=~q}i!==0&&Kc(t,i,0),o!==0&&s===0&&t.tag!==0&&(t.suspendedLanes|=o&~(h&~e))}function Kc(t,e,n){t.pendingLanes|=e,t.suspendedLanes&=~e;var i=31-he(e);t.entangledLanes|=e,t.entanglements[i]=t.entanglements[i]|1073741824|n&4194090}function kc(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var i=31-he(n),s=1<<i;s&e|t[i]&e&&(t[i]|=e),n&=~s}}function su(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function uu(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function Fc(){var t=k.p;return t!==0?t:(t=window.event,t===void 0?32:Cm(t.type))}function $0(t,e){var n=k.p;try{return k.p=t,e()}finally{k.p=n}}var mn=Math.random().toString(36).slice(2),It="__reactFiber$"+mn,le="__reactProps$"+mn,ha="__reactContainer$"+mn,ru="__reactEvents$"+mn,W0="__reactListeners$"+mn,I0="__reactHandles$"+mn,Pc="__reactResources$"+mn,hi="__reactMarker$"+mn;function ou(t){delete t[It],delete t[le],delete t[ru],delete t[W0],delete t[I0]}function ma(t){var e=t[It];if(e)return e;for(var n=t.parentNode;n;){if(e=n[ha]||n[It]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=hm(t);t!==null;){if(n=t[It])return n;t=hm(t)}return e}t=n,n=t.parentNode}return null}function pa(t){if(t=t[It]||t[ha]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function mi(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(r(33))}function ga(t){var e=t[Pc];return e||(e=t[Pc]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Qt(t){t[hi]=!0}var Jc=new Set,$c={};function Zn(t,e){ya(t,e),ya(t+"Capture",e)}function ya(t,e){for($c[t]=e,t=0;t<e.length;t++)Jc.add(e[t])}var ty=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Wc={},Ic={};function ey(t){return oi.call(Ic,t)?!0:oi.call(Wc,t)?!1:ty.test(t)?Ic[t]=!0:(Wc[t]=!0,!1)}function Ol(t,e,n){if(ey(e))if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var i=e.toLowerCase().slice(0,5);if(i!=="data-"&&i!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+n)}}function Nl(t,e,n){if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+n)}}function Pe(t,e,n,i){if(i===null)t.removeAttribute(n);else{switch(typeof i){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(n);return}t.setAttributeNS(e,n,""+i)}}var cu,tf;function va(t){if(cu===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);cu=e&&e[1]||"",tf=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+cu+t+tf}var fu=!1;function du(t,e){if(!t||fu)return"";fu=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var i={DetermineComponentFrameRoot:function(){try{if(e){var q=function(){throw Error()};if(Object.defineProperty(q.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(q,[])}catch(z){var N=z}Reflect.construct(t,[],q)}else{try{q.call()}catch(z){N=z}t.call(q.prototype)}}else{try{throw Error()}catch(z){N=z}(q=t())&&typeof q.catch=="function"&&q.catch(function(){})}}catch(z){if(z&&N&&typeof z.stack=="string")return[z.stack,N.stack]}return[null,null]}};i.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var s=Object.getOwnPropertyDescriptor(i.DetermineComponentFrameRoot,"name");s&&s.configurable&&Object.defineProperty(i.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var o=i.DetermineComponentFrameRoot(),h=o[0],b=o[1];if(h&&b){var T=h.split(`
`),R=b.split(`
`);for(s=i=0;i<T.length&&!T[i].includes("DetermineComponentFrameRoot");)i++;for(;s<R.length&&!R[s].includes("DetermineComponentFrameRoot");)s++;if(i===T.length||s===R.length)for(i=T.length-1,s=R.length-1;1<=i&&0<=s&&T[i]!==R[s];)s--;for(;1<=i&&0<=s;i--,s--)if(T[i]!==R[s]){if(i!==1||s!==1)do if(i--,s--,0>s||T[i]!==R[s]){var L=`
`+T[i].replace(" at new "," at ");return t.displayName&&L.includes("<anonymous>")&&(L=L.replace("<anonymous>",t.displayName)),L}while(1<=i&&0<=s);break}}}finally{fu=!1,Error.prepareStackTrace=n}return(n=t?t.displayName||t.name:"")?va(n):""}function ny(t){switch(t.tag){case 26:case 27:case 5:return va(t.type);case 16:return va("Lazy");case 13:return va("Suspense");case 19:return va("SuspenseList");case 0:case 15:return du(t.type,!1);case 11:return du(t.type.render,!1);case 1:return du(t.type,!0);case 31:return va("Activity");default:return""}}function ef(t){try{var e="";do e+=ny(t),t=t.return;while(t);return e}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function Te(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function nf(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function ay(t){var e=nf(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),i=""+t[e];if(!t.hasOwnProperty(e)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,o=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return s.call(this)},set:function(h){i=""+h,o.call(this,h)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return i},setValue:function(h){i=""+h},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function zl(t){t._valueTracker||(t._valueTracker=ay(t))}function af(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),i="";return t&&(i=nf(t)?t.checked?"true":"false":t.value),t=i,t!==n?(e.setValue(t),!0):!1}function Vl(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var iy=/[\n"\\]/g;function Ae(t){return t.replace(iy,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function hu(t,e,n,i,s,o,h,b){t.name="",h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"?t.type=h:t.removeAttribute("type"),e!=null?h==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+Te(e)):t.value!==""+Te(e)&&(t.value=""+Te(e)):h!=="submit"&&h!=="reset"||t.removeAttribute("value"),e!=null?mu(t,h,Te(e)):n!=null?mu(t,h,Te(n)):i!=null&&t.removeAttribute("value"),s==null&&o!=null&&(t.defaultChecked=!!o),s!=null&&(t.checked=s&&typeof s!="function"&&typeof s!="symbol"),b!=null&&typeof b!="function"&&typeof b!="symbol"&&typeof b!="boolean"?t.name=""+Te(b):t.removeAttribute("name")}function lf(t,e,n,i,s,o,h,b){if(o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"&&(t.type=o),e!=null||n!=null){if(!(o!=="submit"&&o!=="reset"||e!=null))return;n=n!=null?""+Te(n):"",e=e!=null?""+Te(e):n,b||e===t.value||(t.value=e),t.defaultValue=e}i=i??s,i=typeof i!="function"&&typeof i!="symbol"&&!!i,t.checked=b?t.checked:!!i,t.defaultChecked=!!i,h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"&&(t.name=h)}function mu(t,e,n){e==="number"&&Vl(t.ownerDocument)===t||t.defaultValue===""+n||(t.defaultValue=""+n)}function ba(t,e,n,i){if(t=t.options,e){e={};for(var s=0;s<n.length;s++)e["$"+n[s]]=!0;for(n=0;n<t.length;n++)s=e.hasOwnProperty("$"+t[n].value),t[n].selected!==s&&(t[n].selected=s),s&&i&&(t[n].defaultSelected=!0)}else{for(n=""+Te(n),e=null,s=0;s<t.length;s++){if(t[s].value===n){t[s].selected=!0,i&&(t[s].defaultSelected=!0);return}e!==null||t[s].disabled||(e=t[s])}e!==null&&(e.selected=!0)}}function sf(t,e,n){if(e!=null&&(e=""+Te(e),e!==t.value&&(t.value=e),n==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=n!=null?""+Te(n):""}function uf(t,e,n,i){if(e==null){if(i!=null){if(n!=null)throw Error(r(92));if(Yt(i)){if(1<i.length)throw Error(r(93));i=i[0]}n=i}n==null&&(n=""),e=n}n=Te(e),t.defaultValue=n,i=t.textContent,i===n&&i!==""&&i!==null&&(t.value=i)}function xa(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var ly=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function rf(t,e,n){var i=e.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?i?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":i?t.setProperty(e,n):typeof n!="number"||n===0||ly.has(e)?e==="float"?t.cssFloat=n:t[e]=(""+n).trim():t[e]=n+"px"}function of(t,e,n){if(e!=null&&typeof e!="object")throw Error(r(62));if(t=t.style,n!=null){for(var i in n)!n.hasOwnProperty(i)||e!=null&&e.hasOwnProperty(i)||(i.indexOf("--")===0?t.setProperty(i,""):i==="float"?t.cssFloat="":t[i]="");for(var s in e)i=e[s],e.hasOwnProperty(s)&&n[s]!==i&&rf(t,s,i)}else for(var o in e)e.hasOwnProperty(o)&&rf(t,o,e[o])}function pu(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var sy=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),uy=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function _l(t){return uy.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var gu=null;function yu(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var Sa=null,Ta=null;function cf(t){var e=pa(t);if(e&&(t=e.stateNode)){var n=t[le]||null;t:switch(t=e.stateNode,e.type){case"input":if(hu(t,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+Ae(""+e)+'"][type="radio"]'),e=0;e<n.length;e++){var i=n[e];if(i!==t&&i.form===t.form){var s=i[le]||null;if(!s)throw Error(r(90));hu(i,s.value,s.defaultValue,s.defaultValue,s.checked,s.defaultChecked,s.type,s.name)}}for(e=0;e<n.length;e++)i=n[e],i.form===t.form&&af(i)}break t;case"textarea":sf(t,n.value,n.defaultValue);break t;case"select":e=n.value,e!=null&&ba(t,!!n.multiple,e,!1)}}}var vu=!1;function ff(t,e,n){if(vu)return t(e,n);vu=!0;try{var i=t(e);return i}finally{if(vu=!1,(Sa!==null||Ta!==null)&&(xs(),Sa&&(e=Sa,t=Ta,Ta=Sa=null,cf(e),t)))for(e=0;e<t.length;e++)cf(t[e])}}function pi(t,e){var n=t.stateNode;if(n===null)return null;var i=n[le]||null;if(i===null)return null;n=i[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(i=!i.disabled)||(t=t.type,i=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!i;break t;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(r(231,e,typeof n));return n}var Je=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),bu=!1;if(Je)try{var gi={};Object.defineProperty(gi,"passive",{get:function(){bu=!0}}),window.addEventListener("test",gi,gi),window.removeEventListener("test",gi,gi)}catch{bu=!1}var pn=null,xu=null,Bl=null;function df(){if(Bl)return Bl;var t,e=xu,n=e.length,i,s="value"in pn?pn.value:pn.textContent,o=s.length;for(t=0;t<n&&e[t]===s[t];t++);var h=n-t;for(i=1;i<=h&&e[n-i]===s[o-i];i++);return Bl=s.slice(t,1<i?1-i:void 0)}function Ul(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function Ll(){return!0}function hf(){return!1}function se(t){function e(n,i,s,o,h){this._reactName=n,this._targetInst=s,this.type=i,this.nativeEvent=o,this.target=h,this.currentTarget=null;for(var b in t)t.hasOwnProperty(b)&&(n=t[b],this[b]=n?n(o):o[b]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?Ll:hf,this.isPropagationStopped=hf,this}return v(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ll)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ll)},persist:function(){},isPersistent:Ll}),e}var Qn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Hl=se(Qn),yi=v({},Qn,{view:0,detail:0}),ry=se(yi),Su,Tu,vi,ql=v({},yi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Eu,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==vi&&(vi&&t.type==="mousemove"?(Su=t.screenX-vi.screenX,Tu=t.screenY-vi.screenY):Tu=Su=0,vi=t),Su)},movementY:function(t){return"movementY"in t?t.movementY:Tu}}),mf=se(ql),oy=v({},ql,{dataTransfer:0}),cy=se(oy),fy=v({},yi,{relatedTarget:0}),Au=se(fy),dy=v({},Qn,{animationName:0,elapsedTime:0,pseudoElement:0}),hy=se(dy),my=v({},Qn,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),py=se(my),gy=v({},Qn,{data:0}),pf=se(gy),yy={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},vy={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},by={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function xy(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=by[t])?!!e[t]:!1}function Eu(){return xy}var Sy=v({},yi,{key:function(t){if(t.key){var e=yy[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=Ul(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?vy[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Eu,charCode:function(t){return t.type==="keypress"?Ul(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Ul(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Ty=se(Sy),Ay=v({},ql,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),gf=se(Ay),Ey=v({},yi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Eu}),My=se(Ey),jy=v({},Qn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Dy=se(jy),Cy=v({},ql,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),wy=se(Cy),Ry=v({},Qn,{newState:0,oldState:0}),Oy=se(Ry),Ny=[9,13,27,32],Mu=Je&&"CompositionEvent"in window,bi=null;Je&&"documentMode"in document&&(bi=document.documentMode);var zy=Je&&"TextEvent"in window&&!bi,yf=Je&&(!Mu||bi&&8<bi&&11>=bi),vf=" ",bf=!1;function xf(t,e){switch(t){case"keyup":return Ny.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Sf(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Aa=!1;function Vy(t,e){switch(t){case"compositionend":return Sf(e);case"keypress":return e.which!==32?null:(bf=!0,vf);case"textInput":return t=e.data,t===vf&&bf?null:t;default:return null}}function _y(t,e){if(Aa)return t==="compositionend"||!Mu&&xf(t,e)?(t=df(),Bl=xu=pn=null,Aa=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return yf&&e.locale!=="ko"?null:e.data;default:return null}}var By={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Tf(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!By[t.type]:e==="textarea"}function Af(t,e,n,i){Sa?Ta?Ta.push(i):Ta=[i]:Sa=i,e=js(e,"onChange"),0<e.length&&(n=new Hl("onChange","change",null,n,i),t.push({event:n,listeners:e}))}var xi=null,Si=null;function Uy(t){am(t,0)}function Yl(t){var e=mi(t);if(af(e))return t}function Ef(t,e){if(t==="change")return e}var Mf=!1;if(Je){var ju;if(Je){var Du="oninput"in document;if(!Du){var jf=document.createElement("div");jf.setAttribute("oninput","return;"),Du=typeof jf.oninput=="function"}ju=Du}else ju=!1;Mf=ju&&(!document.documentMode||9<document.documentMode)}function Df(){xi&&(xi.detachEvent("onpropertychange",Cf),Si=xi=null)}function Cf(t){if(t.propertyName==="value"&&Yl(Si)){var e=[];Af(e,Si,t,yu(t)),ff(Uy,e)}}function Ly(t,e,n){t==="focusin"?(Df(),xi=e,Si=n,xi.attachEvent("onpropertychange",Cf)):t==="focusout"&&Df()}function Hy(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Yl(Si)}function qy(t,e){if(t==="click")return Yl(e)}function Yy(t,e){if(t==="input"||t==="change")return Yl(e)}function Gy(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var me=typeof Object.is=="function"?Object.is:Gy;function Ti(t,e){if(me(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),i=Object.keys(e);if(n.length!==i.length)return!1;for(i=0;i<n.length;i++){var s=n[i];if(!oi.call(e,s)||!me(t[s],e[s]))return!1}return!0}function wf(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Rf(t,e){var n=wf(t);t=0;for(var i;n;){if(n.nodeType===3){if(i=t+n.textContent.length,t<=e&&i>=e)return{node:n,offset:e-t};t=i}t:{for(;n;){if(n.nextSibling){n=n.nextSibling;break t}n=n.parentNode}n=void 0}n=wf(n)}}function Of(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Of(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Nf(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=Vl(t.document);e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch{n=!1}if(n)t=e.contentWindow;else break;e=Vl(t.document)}return e}function Cu(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var Xy=Je&&"documentMode"in document&&11>=document.documentMode,Ea=null,wu=null,Ai=null,Ru=!1;function zf(t,e,n){var i=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ru||Ea==null||Ea!==Vl(i)||(i=Ea,"selectionStart"in i&&Cu(i)?i={start:i.selectionStart,end:i.selectionEnd}:(i=(i.ownerDocument&&i.ownerDocument.defaultView||window).getSelection(),i={anchorNode:i.anchorNode,anchorOffset:i.anchorOffset,focusNode:i.focusNode,focusOffset:i.focusOffset}),Ai&&Ti(Ai,i)||(Ai=i,i=js(wu,"onSelect"),0<i.length&&(e=new Hl("onSelect","select",null,e,n),t.push({event:e,listeners:i}),e.target=Ea)))}function Kn(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var Ma={animationend:Kn("Animation","AnimationEnd"),animationiteration:Kn("Animation","AnimationIteration"),animationstart:Kn("Animation","AnimationStart"),transitionrun:Kn("Transition","TransitionRun"),transitionstart:Kn("Transition","TransitionStart"),transitioncancel:Kn("Transition","TransitionCancel"),transitionend:Kn("Transition","TransitionEnd")},Ou={},Vf={};Je&&(Vf=document.createElement("div").style,"AnimationEvent"in window||(delete Ma.animationend.animation,delete Ma.animationiteration.animation,delete Ma.animationstart.animation),"TransitionEvent"in window||delete Ma.transitionend.transition);function kn(t){if(Ou[t])return Ou[t];if(!Ma[t])return t;var e=Ma[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in Vf)return Ou[t]=e[n];return t}var _f=kn("animationend"),Bf=kn("animationiteration"),Uf=kn("animationstart"),Zy=kn("transitionrun"),Qy=kn("transitionstart"),Ky=kn("transitioncancel"),Lf=kn("transitionend"),Hf=new Map,Nu="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Nu.push("scrollEnd");function _e(t,e){Hf.set(t,e),Zn(e,[t])}var qf=new WeakMap;function Ee(t,e){if(typeof t=="object"&&t!==null){var n=qf.get(t);return n!==void 0?n:(e={value:t,source:e,stack:ef(e)},qf.set(t,e),e)}return{value:t,source:e,stack:ef(e)}}var Me=[],ja=0,zu=0;function Gl(){for(var t=ja,e=zu=ja=0;e<t;){var n=Me[e];Me[e++]=null;var i=Me[e];Me[e++]=null;var s=Me[e];Me[e++]=null;var o=Me[e];if(Me[e++]=null,i!==null&&s!==null){var h=i.pending;h===null?s.next=s:(s.next=h.next,h.next=s),i.pending=s}o!==0&&Yf(n,s,o)}}function Xl(t,e,n,i){Me[ja++]=t,Me[ja++]=e,Me[ja++]=n,Me[ja++]=i,zu|=i,t.lanes|=i,t=t.alternate,t!==null&&(t.lanes|=i)}function Vu(t,e,n,i){return Xl(t,e,n,i),Zl(t)}function Da(t,e){return Xl(t,null,null,e),Zl(t)}function Yf(t,e,n){t.lanes|=n;var i=t.alternate;i!==null&&(i.lanes|=n);for(var s=!1,o=t.return;o!==null;)o.childLanes|=n,i=o.alternate,i!==null&&(i.childLanes|=n),o.tag===22&&(t=o.stateNode,t===null||t._visibility&1||(s=!0)),t=o,o=o.return;return t.tag===3?(o=t.stateNode,s&&e!==null&&(s=31-he(n),t=o.hiddenUpdates,i=t[s],i===null?t[s]=[e]:i.push(e),e.lane=n|536870912),o):null}function Zl(t){if(50<Fi)throw Fi=0,qr=null,Error(r(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var Ca={};function ky(t,e,n,i){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=i,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function pe(t,e,n,i){return new ky(t,e,n,i)}function _u(t){return t=t.prototype,!(!t||!t.isReactComponent)}function $e(t,e){var n=t.alternate;return n===null?(n=pe(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&65011712,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n.refCleanup=t.refCleanup,n}function Gf(t,e){t.flags&=65011714;var n=t.alternate;return n===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=n.childLanes,t.lanes=n.lanes,t.child=n.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=n.memoizedProps,t.memoizedState=n.memoizedState,t.updateQueue=n.updateQueue,t.type=n.type,e=n.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function Ql(t,e,n,i,s,o){var h=0;if(i=t,typeof t=="function")_u(t)&&(h=1);else if(typeof t=="string")h=Pv(t,n,tt.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case F:return t=pe(31,n,e,s),t.elementType=F,t.lanes=o,t;case j:return Fn(n.children,s,o,e);case Y:h=8,s|=24;break;case X:return t=pe(12,n,e,s|2),t.elementType=X,t.lanes=o,t;case O:return t=pe(13,n,e,s),t.elementType=O,t.lanes=o,t;case B:return t=pe(19,n,e,s),t.elementType=B,t.lanes=o,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case Q:case M:h=10;break t;case C:h=9;break t;case _:h=11;break t;case Z:h=14;break t;case K:h=16,i=null;break t}h=29,n=Error(r(130,t===null?"null":typeof t,"")),i=null}return e=pe(h,n,e,s),e.elementType=t,e.type=i,e.lanes=o,e}function Fn(t,e,n,i){return t=pe(7,t,i,e),t.lanes=n,t}function Bu(t,e,n){return t=pe(6,t,null,e),t.lanes=n,t}function Uu(t,e,n){return e=pe(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var wa=[],Ra=0,Kl=null,kl=0,je=[],De=0,Pn=null,We=1,Ie="";function Jn(t,e){wa[Ra++]=kl,wa[Ra++]=Kl,Kl=t,kl=e}function Xf(t,e,n){je[De++]=We,je[De++]=Ie,je[De++]=Pn,Pn=t;var i=We;t=Ie;var s=32-he(i)-1;i&=~(1<<s),n+=1;var o=32-he(e)+s;if(30<o){var h=s-s%5;o=(i&(1<<h)-1).toString(32),i>>=h,s-=h,We=1<<32-he(e)+s|n<<s|i,Ie=o+t}else We=1<<o|n<<s|i,Ie=t}function Lu(t){t.return!==null&&(Jn(t,1),Xf(t,1,0))}function Hu(t){for(;t===Kl;)Kl=wa[--Ra],wa[Ra]=null,kl=wa[--Ra],wa[Ra]=null;for(;t===Pn;)Pn=je[--De],je[De]=null,Ie=je[--De],je[De]=null,We=je[--De],je[De]=null}var ae=null,Nt=null,vt=!1,$n=null,qe=!1,qu=Error(r(519));function Wn(t){var e=Error(r(418,""));throw ji(Ee(e,t)),qu}function Zf(t){var e=t.stateNode,n=t.type,i=t.memoizedProps;switch(e[It]=t,e[le]=i,n){case"dialog":mt("cancel",e),mt("close",e);break;case"iframe":case"object":case"embed":mt("load",e);break;case"video":case"audio":for(n=0;n<Ji.length;n++)mt(Ji[n],e);break;case"source":mt("error",e);break;case"img":case"image":case"link":mt("error",e),mt("load",e);break;case"details":mt("toggle",e);break;case"input":mt("invalid",e),lf(e,i.value,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name,!0),zl(e);break;case"select":mt("invalid",e);break;case"textarea":mt("invalid",e),uf(e,i.value,i.defaultValue,i.children),zl(e)}n=i.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||e.textContent===""+n||i.suppressHydrationWarning===!0||um(e.textContent,n)?(i.popover!=null&&(mt("beforetoggle",e),mt("toggle",e)),i.onScroll!=null&&mt("scroll",e),i.onScrollEnd!=null&&mt("scrollend",e),i.onClick!=null&&(e.onclick=Ds),e=!0):e=!1,e||Wn(t)}function Qf(t){for(ae=t.return;ae;)switch(ae.tag){case 5:case 13:qe=!1;return;case 27:case 3:qe=!0;return;default:ae=ae.return}}function Ei(t){if(t!==ae)return!1;if(!vt)return Qf(t),vt=!0,!1;var e=t.tag,n;if((n=e!==3&&e!==27)&&((n=e===5)&&(n=t.type,n=!(n!=="form"&&n!=="button")||no(t.type,t.memoizedProps)),n=!n),n&&Nt&&Wn(t),Qf(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(r(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(n=t.data,n==="/$"){if(e===0){Nt=Ue(t.nextSibling);break t}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++;t=t.nextSibling}Nt=null}}else e===27?(e=Nt,On(t.type)?(t=so,so=null,Nt=t):Nt=e):Nt=ae?Ue(t.stateNode.nextSibling):null;return!0}function Mi(){Nt=ae=null,vt=!1}function Kf(){var t=$n;return t!==null&&(oe===null?oe=t:oe.push.apply(oe,t),$n=null),t}function ji(t){$n===null?$n=[t]:$n.push(t)}var Yu=G(null),In=null,tn=null;function gn(t,e,n){$(Yu,e._currentValue),e._currentValue=n}function en(t){t._currentValue=Yu.current,P(Yu)}function Gu(t,e,n){for(;t!==null;){var i=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,i!==null&&(i.childLanes|=e)):i!==null&&(i.childLanes&e)!==e&&(i.childLanes|=e),t===n)break;t=t.return}}function Xu(t,e,n,i){var s=t.child;for(s!==null&&(s.return=t);s!==null;){var o=s.dependencies;if(o!==null){var h=s.child;o=o.firstContext;t:for(;o!==null;){var b=o;o=s;for(var T=0;T<e.length;T++)if(b.context===e[T]){o.lanes|=n,b=o.alternate,b!==null&&(b.lanes|=n),Gu(o.return,n,t),i||(h=null);break t}o=b.next}}else if(s.tag===18){if(h=s.return,h===null)throw Error(r(341));h.lanes|=n,o=h.alternate,o!==null&&(o.lanes|=n),Gu(h,n,t),h=null}else h=s.child;if(h!==null)h.return=s;else for(h=s;h!==null;){if(h===t){h=null;break}if(s=h.sibling,s!==null){s.return=h.return,h=s;break}h=h.return}s=h}}function Di(t,e,n,i){t=null;for(var s=e,o=!1;s!==null;){if(!o){if((s.flags&524288)!==0)o=!0;else if((s.flags&262144)!==0)break}if(s.tag===10){var h=s.alternate;if(h===null)throw Error(r(387));if(h=h.memoizedProps,h!==null){var b=s.type;me(s.pendingProps.value,h.value)||(t!==null?t.push(b):t=[b])}}else if(s===Zt.current){if(h=s.alternate,h===null)throw Error(r(387));h.memoizedState.memoizedState!==s.memoizedState.memoizedState&&(t!==null?t.push(nl):t=[nl])}s=s.return}t!==null&&Xu(e,t,n,i),e.flags|=262144}function Fl(t){for(t=t.firstContext;t!==null;){if(!me(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function ta(t){In=t,tn=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function te(t){return kf(In,t)}function Pl(t,e){return In===null&&ta(t),kf(t,e)}function kf(t,e){var n=e._currentValue;if(e={context:e,memoizedValue:n,next:null},tn===null){if(t===null)throw Error(r(308));tn=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else tn=tn.next=e;return n}var Fy=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(n,i){t.push(i)}};this.abort=function(){e.aborted=!0,t.forEach(function(n){return n()})}},Py=a.unstable_scheduleCallback,Jy=a.unstable_NormalPriority,Gt={$$typeof:M,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Zu(){return{controller:new Fy,data:new Map,refCount:0}}function Ci(t){t.refCount--,t.refCount===0&&Py(Jy,function(){t.controller.abort()})}var wi=null,Qu=0,Oa=0,Na=null;function $y(t,e){if(wi===null){var n=wi=[];Qu=0,Oa=kr(),Na={status:"pending",value:void 0,then:function(i){n.push(i)}}}return Qu++,e.then(Ff,Ff),e}function Ff(){if(--Qu===0&&wi!==null){Na!==null&&(Na.status="fulfilled");var t=wi;wi=null,Oa=0,Na=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function Wy(t,e){var n=[],i={status:"pending",value:null,reason:null,then:function(s){n.push(s)}};return t.then(function(){i.status="fulfilled",i.value=e;for(var s=0;s<n.length;s++)(0,n[s])(e)},function(s){for(i.status="rejected",i.reason=s,s=0;s<n.length;s++)(0,n[s])(void 0)}),i}var Pf=U.S;U.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&$y(t,e),Pf!==null&&Pf(t,e)};var ea=G(null);function Ku(){var t=ea.current;return t!==null?t:Dt.pooledCache}function Jl(t,e){e===null?$(ea,ea.current):$(ea,e.pool)}function Jf(){var t=Ku();return t===null?null:{parent:Gt._currentValue,pool:t}}var Ri=Error(r(460)),$f=Error(r(474)),$l=Error(r(542)),ku={then:function(){}};function Wf(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Wl(){}function If(t,e,n){switch(n=t[n],n===void 0?t.push(e):n!==e&&(e.then(Wl,Wl),e=n),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,ed(t),t;default:if(typeof e.status=="string")e.then(Wl,Wl);else{if(t=Dt,t!==null&&100<t.shellSuspendCounter)throw Error(r(482));t=e,t.status="pending",t.then(function(i){if(e.status==="pending"){var s=e;s.status="fulfilled",s.value=i}},function(i){if(e.status==="pending"){var s=e;s.status="rejected",s.reason=i}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,ed(t),t}throw Oi=e,Ri}}var Oi=null;function td(){if(Oi===null)throw Error(r(459));var t=Oi;return Oi=null,t}function ed(t){if(t===Ri||t===$l)throw Error(r(483))}var yn=!1;function Fu(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Pu(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function vn(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function bn(t,e,n){var i=t.updateQueue;if(i===null)return null;if(i=i.shared,(bt&2)!==0){var s=i.pending;return s===null?e.next=e:(e.next=s.next,s.next=e),i.pending=e,e=Zl(t),Yf(t,null,n),e}return Xl(t,i,e,n),Zl(t)}function Ni(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194048)!==0)){var i=e.lanes;i&=t.pendingLanes,n|=i,e.lanes=n,kc(t,n)}}function Ju(t,e){var n=t.updateQueue,i=t.alternate;if(i!==null&&(i=i.updateQueue,n===i)){var s=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var h={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};o===null?s=o=h:o=o.next=h,n=n.next}while(n!==null);o===null?s=o=e:o=o.next=e}else s=o=e;n={baseState:i.baseState,firstBaseUpdate:s,lastBaseUpdate:o,shared:i.shared,callbacks:i.callbacks},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}var $u=!1;function zi(){if($u){var t=Na;if(t!==null)throw t}}function Vi(t,e,n,i){$u=!1;var s=t.updateQueue;yn=!1;var o=s.firstBaseUpdate,h=s.lastBaseUpdate,b=s.shared.pending;if(b!==null){s.shared.pending=null;var T=b,R=T.next;T.next=null,h===null?o=R:h.next=R,h=T;var L=t.alternate;L!==null&&(L=L.updateQueue,b=L.lastBaseUpdate,b!==h&&(b===null?L.firstBaseUpdate=R:b.next=R,L.lastBaseUpdate=T))}if(o!==null){var q=s.baseState;h=0,L=R=T=null,b=o;do{var N=b.lane&-536870913,z=N!==b.lane;if(z?(gt&N)===N:(i&N)===N){N!==0&&N===Oa&&($u=!0),L!==null&&(L=L.next={lane:0,tag:b.tag,payload:b.payload,callback:null,next:null});t:{var lt=t,nt=b;N=e;var Mt=n;switch(nt.tag){case 1:if(lt=nt.payload,typeof lt=="function"){q=lt.call(Mt,q,N);break t}q=lt;break t;case 3:lt.flags=lt.flags&-65537|128;case 0:if(lt=nt.payload,N=typeof lt=="function"?lt.call(Mt,q,N):lt,N==null)break t;q=v({},q,N);break t;case 2:yn=!0}}N=b.callback,N!==null&&(t.flags|=64,z&&(t.flags|=8192),z=s.callbacks,z===null?s.callbacks=[N]:z.push(N))}else z={lane:N,tag:b.tag,payload:b.payload,callback:b.callback,next:null},L===null?(R=L=z,T=q):L=L.next=z,h|=N;if(b=b.next,b===null){if(b=s.shared.pending,b===null)break;z=b,b=z.next,z.next=null,s.lastBaseUpdate=z,s.shared.pending=null}}while(!0);L===null&&(T=q),s.baseState=T,s.firstBaseUpdate=R,s.lastBaseUpdate=L,o===null&&(s.shared.lanes=0),Dn|=h,t.lanes=h,t.memoizedState=q}}function nd(t,e){if(typeof t!="function")throw Error(r(191,t));t.call(e)}function ad(t,e){var n=t.callbacks;if(n!==null)for(t.callbacks=null,t=0;t<n.length;t++)nd(n[t],e)}var za=G(null),Il=G(0);function id(t,e){t=on,$(Il,t),$(za,e),on=t|e.baseLanes}function Wu(){$(Il,on),$(za,za.current)}function Iu(){on=Il.current,P(za),P(Il)}var xn=0,ct=null,At=null,Lt=null,ts=!1,Va=!1,na=!1,es=0,_i=0,_a=null,Iy=0;function _t(){throw Error(r(321))}function tr(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!me(t[n],e[n]))return!1;return!0}function er(t,e,n,i,s,o){return xn=o,ct=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,U.H=t===null||t.memoizedState===null?Yd:Gd,na=!1,o=n(i,s),na=!1,Va&&(o=sd(e,n,i,s)),ld(t),o}function ld(t){U.H=us;var e=At!==null&&At.next!==null;if(xn=0,Lt=At=ct=null,ts=!1,_i=0,_a=null,e)throw Error(r(300));t===null||Kt||(t=t.dependencies,t!==null&&Fl(t)&&(Kt=!0))}function sd(t,e,n,i){ct=t;var s=0;do{if(Va&&(_a=null),_i=0,Va=!1,25<=s)throw Error(r(301));if(s+=1,Lt=At=null,t.updateQueue!=null){var o=t.updateQueue;o.lastEffect=null,o.events=null,o.stores=null,o.memoCache!=null&&(o.memoCache.index=0)}U.H=sv,o=e(n,i)}while(Va);return o}function tv(){var t=U.H,e=t.useState()[0];return e=typeof e.then=="function"?Bi(e):e,t=t.useState()[0],(At!==null?At.memoizedState:null)!==t&&(ct.flags|=1024),e}function nr(){var t=es!==0;return es=0,t}function ar(t,e,n){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~n}function ir(t){if(ts){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}ts=!1}xn=0,Lt=At=ct=null,Va=!1,_i=es=0,_a=null}function ue(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Lt===null?ct.memoizedState=Lt=t:Lt=Lt.next=t,Lt}function Ht(){if(At===null){var t=ct.alternate;t=t!==null?t.memoizedState:null}else t=At.next;var e=Lt===null?ct.memoizedState:Lt.next;if(e!==null)Lt=e,At=t;else{if(t===null)throw ct.alternate===null?Error(r(467)):Error(r(310));At=t,t={memoizedState:At.memoizedState,baseState:At.baseState,baseQueue:At.baseQueue,queue:At.queue,next:null},Lt===null?ct.memoizedState=Lt=t:Lt=Lt.next=t}return Lt}function lr(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Bi(t){var e=_i;return _i+=1,_a===null&&(_a=[]),t=If(_a,t,e),e=ct,(Lt===null?e.memoizedState:Lt.next)===null&&(e=e.alternate,U.H=e===null||e.memoizedState===null?Yd:Gd),t}function ns(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return Bi(t);if(t.$$typeof===M)return te(t)}throw Error(r(438,String(t)))}function sr(t){var e=null,n=ct.updateQueue;if(n!==null&&(e=n.memoCache),e==null){var i=ct.alternate;i!==null&&(i=i.updateQueue,i!==null&&(i=i.memoCache,i!=null&&(e={data:i.data.map(function(s){return s.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),n===null&&(n=lr(),ct.updateQueue=n),n.memoCache=e,n=e.data[e.index],n===void 0)for(n=e.data[e.index]=Array(t),i=0;i<t;i++)n[i]=ut;return e.index++,n}function nn(t,e){return typeof e=="function"?e(t):e}function as(t){var e=Ht();return ur(e,At,t)}function ur(t,e,n){var i=t.queue;if(i===null)throw Error(r(311));i.lastRenderedReducer=n;var s=t.baseQueue,o=i.pending;if(o!==null){if(s!==null){var h=s.next;s.next=o.next,o.next=h}e.baseQueue=s=o,i.pending=null}if(o=t.baseState,s===null)t.memoizedState=o;else{e=s.next;var b=h=null,T=null,R=e,L=!1;do{var q=R.lane&-536870913;if(q!==R.lane?(gt&q)===q:(xn&q)===q){var N=R.revertLane;if(N===0)T!==null&&(T=T.next={lane:0,revertLane:0,action:R.action,hasEagerState:R.hasEagerState,eagerState:R.eagerState,next:null}),q===Oa&&(L=!0);else if((xn&N)===N){R=R.next,N===Oa&&(L=!0);continue}else q={lane:0,revertLane:R.revertLane,action:R.action,hasEagerState:R.hasEagerState,eagerState:R.eagerState,next:null},T===null?(b=T=q,h=o):T=T.next=q,ct.lanes|=N,Dn|=N;q=R.action,na&&n(o,q),o=R.hasEagerState?R.eagerState:n(o,q)}else N={lane:q,revertLane:R.revertLane,action:R.action,hasEagerState:R.hasEagerState,eagerState:R.eagerState,next:null},T===null?(b=T=N,h=o):T=T.next=N,ct.lanes|=q,Dn|=q;R=R.next}while(R!==null&&R!==e);if(T===null?h=o:T.next=b,!me(o,t.memoizedState)&&(Kt=!0,L&&(n=Na,n!==null)))throw n;t.memoizedState=o,t.baseState=h,t.baseQueue=T,i.lastRenderedState=o}return s===null&&(i.lanes=0),[t.memoizedState,i.dispatch]}function rr(t){var e=Ht(),n=e.queue;if(n===null)throw Error(r(311));n.lastRenderedReducer=t;var i=n.dispatch,s=n.pending,o=e.memoizedState;if(s!==null){n.pending=null;var h=s=s.next;do o=t(o,h.action),h=h.next;while(h!==s);me(o,e.memoizedState)||(Kt=!0),e.memoizedState=o,e.baseQueue===null&&(e.baseState=o),n.lastRenderedState=o}return[o,i]}function ud(t,e,n){var i=ct,s=Ht(),o=vt;if(o){if(n===void 0)throw Error(r(407));n=n()}else n=e();var h=!me((At||s).memoizedState,n);h&&(s.memoizedState=n,Kt=!0),s=s.queue;var b=cd.bind(null,i,s,t);if(Ui(2048,8,b,[t]),s.getSnapshot!==e||h||Lt!==null&&Lt.memoizedState.tag&1){if(i.flags|=2048,Ba(9,is(),od.bind(null,i,s,n,e),null),Dt===null)throw Error(r(349));o||(xn&124)!==0||rd(i,e,n)}return n}function rd(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=ct.updateQueue,e===null?(e=lr(),ct.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function od(t,e,n,i){e.value=n,e.getSnapshot=i,fd(e)&&dd(t)}function cd(t,e,n){return n(function(){fd(e)&&dd(t)})}function fd(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!me(t,n)}catch{return!0}}function dd(t){var e=Da(t,2);e!==null&&xe(e,t,2)}function or(t){var e=ue();if(typeof t=="function"){var n=t;if(t=n(),na){hn(!0);try{n()}finally{hn(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:nn,lastRenderedState:t},e}function hd(t,e,n,i){return t.baseState=n,ur(t,At,typeof i=="function"?i:nn)}function ev(t,e,n,i,s){if(ss(t))throw Error(r(485));if(t=e.action,t!==null){var o={payload:s,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(h){o.listeners.push(h)}};U.T!==null?n(!0):o.isTransition=!1,i(o),n=e.pending,n===null?(o.next=e.pending=o,md(e,o)):(o.next=n.next,e.pending=n.next=o)}}function md(t,e){var n=e.action,i=e.payload,s=t.state;if(e.isTransition){var o=U.T,h={};U.T=h;try{var b=n(s,i),T=U.S;T!==null&&T(h,b),pd(t,e,b)}catch(R){cr(t,e,R)}finally{U.T=o}}else try{o=n(s,i),pd(t,e,o)}catch(R){cr(t,e,R)}}function pd(t,e,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(i){gd(t,e,i)},function(i){return cr(t,e,i)}):gd(t,e,n)}function gd(t,e,n){e.status="fulfilled",e.value=n,yd(e),t.state=n,e=t.pending,e!==null&&(n=e.next,n===e?t.pending=null:(n=n.next,e.next=n,md(t,n)))}function cr(t,e,n){var i=t.pending;if(t.pending=null,i!==null){i=i.next;do e.status="rejected",e.reason=n,yd(e),e=e.next;while(e!==i)}t.action=null}function yd(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function vd(t,e){return e}function bd(t,e){if(vt){var n=Dt.formState;if(n!==null){t:{var i=ct;if(vt){if(Nt){e:{for(var s=Nt,o=qe;s.nodeType!==8;){if(!o){s=null;break e}if(s=Ue(s.nextSibling),s===null){s=null;break e}}o=s.data,s=o==="F!"||o==="F"?s:null}if(s){Nt=Ue(s.nextSibling),i=s.data==="F!";break t}}Wn(i)}i=!1}i&&(e=n[0])}}return n=ue(),n.memoizedState=n.baseState=e,i={pending:null,lanes:0,dispatch:null,lastRenderedReducer:vd,lastRenderedState:e},n.queue=i,n=Ld.bind(null,ct,i),i.dispatch=n,i=or(!1),o=pr.bind(null,ct,!1,i.queue),i=ue(),s={state:e,dispatch:null,action:t,pending:null},i.queue=s,n=ev.bind(null,ct,s,o,n),s.dispatch=n,i.memoizedState=t,[e,n,!1]}function xd(t){var e=Ht();return Sd(e,At,t)}function Sd(t,e,n){if(e=ur(t,e,vd)[0],t=as(nn)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var i=Bi(e)}catch(h){throw h===Ri?$l:h}else i=e;e=Ht();var s=e.queue,o=s.dispatch;return n!==e.memoizedState&&(ct.flags|=2048,Ba(9,is(),nv.bind(null,s,n),null)),[i,o,t]}function nv(t,e){t.action=e}function Td(t){var e=Ht(),n=At;if(n!==null)return Sd(e,n,t);Ht(),e=e.memoizedState,n=Ht();var i=n.queue.dispatch;return n.memoizedState=t,[e,i,!1]}function Ba(t,e,n,i){return t={tag:t,create:n,deps:i,inst:e,next:null},e=ct.updateQueue,e===null&&(e=lr(),ct.updateQueue=e),n=e.lastEffect,n===null?e.lastEffect=t.next=t:(i=n.next,n.next=t,t.next=i,e.lastEffect=t),t}function is(){return{destroy:void 0,resource:void 0}}function Ad(){return Ht().memoizedState}function ls(t,e,n,i){var s=ue();i=i===void 0?null:i,ct.flags|=t,s.memoizedState=Ba(1|e,is(),n,i)}function Ui(t,e,n,i){var s=Ht();i=i===void 0?null:i;var o=s.memoizedState.inst;At!==null&&i!==null&&tr(i,At.memoizedState.deps)?s.memoizedState=Ba(e,o,n,i):(ct.flags|=t,s.memoizedState=Ba(1|e,o,n,i))}function Ed(t,e){ls(8390656,8,t,e)}function Md(t,e){Ui(2048,8,t,e)}function jd(t,e){return Ui(4,2,t,e)}function Dd(t,e){return Ui(4,4,t,e)}function Cd(t,e){if(typeof e=="function"){t=t();var n=e(t);return function(){typeof n=="function"?n():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function wd(t,e,n){n=n!=null?n.concat([t]):null,Ui(4,4,Cd.bind(null,e,t),n)}function fr(){}function Rd(t,e){var n=Ht();e=e===void 0?null:e;var i=n.memoizedState;return e!==null&&tr(e,i[1])?i[0]:(n.memoizedState=[t,e],t)}function Od(t,e){var n=Ht();e=e===void 0?null:e;var i=n.memoizedState;if(e!==null&&tr(e,i[1]))return i[0];if(i=t(),na){hn(!0);try{t()}finally{hn(!1)}}return n.memoizedState=[i,e],i}function dr(t,e,n){return n===void 0||(xn&1073741824)!==0?t.memoizedState=e:(t.memoizedState=n,t=Vh(),ct.lanes|=t,Dn|=t,n)}function Nd(t,e,n,i){return me(n,e)?n:za.current!==null?(t=dr(t,n,i),me(t,e)||(Kt=!0),t):(xn&42)===0?(Kt=!0,t.memoizedState=n):(t=Vh(),ct.lanes|=t,Dn|=t,e)}function zd(t,e,n,i,s){var o=k.p;k.p=o!==0&&8>o?o:8;var h=U.T,b={};U.T=b,pr(t,!1,e,n);try{var T=s(),R=U.S;if(R!==null&&R(b,T),T!==null&&typeof T=="object"&&typeof T.then=="function"){var L=Wy(T,i);Li(t,e,L,be(t))}else Li(t,e,i,be(t))}catch(q){Li(t,e,{then:function(){},status:"rejected",reason:q},be())}finally{k.p=o,U.T=h}}function av(){}function hr(t,e,n,i){if(t.tag!==5)throw Error(r(476));var s=Vd(t).queue;zd(t,s,e,J,n===null?av:function(){return _d(t),n(i)})}function Vd(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:J,baseState:J,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:nn,lastRenderedState:J},next:null};var n={};return e.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:nn,lastRenderedState:n},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function _d(t){var e=Vd(t).next.queue;Li(t,e,{},be())}function mr(){return te(nl)}function Bd(){return Ht().memoizedState}function Ud(){return Ht().memoizedState}function iv(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var n=be();t=vn(n);var i=bn(e,t,n);i!==null&&(xe(i,e,n),Ni(i,e,n)),e={cache:Zu()},t.payload=e;return}e=e.return}}function lv(t,e,n){var i=be();n={lane:i,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},ss(t)?Hd(e,n):(n=Vu(t,e,n,i),n!==null&&(xe(n,t,i),qd(n,e,i)))}function Ld(t,e,n){var i=be();Li(t,e,n,i)}function Li(t,e,n,i){var s={lane:i,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(ss(t))Hd(e,s);else{var o=t.alternate;if(t.lanes===0&&(o===null||o.lanes===0)&&(o=e.lastRenderedReducer,o!==null))try{var h=e.lastRenderedState,b=o(h,n);if(s.hasEagerState=!0,s.eagerState=b,me(b,h))return Xl(t,e,s,0),Dt===null&&Gl(),!1}catch{}finally{}if(n=Vu(t,e,s,i),n!==null)return xe(n,t,i),qd(n,e,i),!0}return!1}function pr(t,e,n,i){if(i={lane:2,revertLane:kr(),action:i,hasEagerState:!1,eagerState:null,next:null},ss(t)){if(e)throw Error(r(479))}else e=Vu(t,n,i,2),e!==null&&xe(e,t,2)}function ss(t){var e=t.alternate;return t===ct||e!==null&&e===ct}function Hd(t,e){Va=ts=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function qd(t,e,n){if((n&4194048)!==0){var i=e.lanes;i&=t.pendingLanes,n|=i,e.lanes=n,kc(t,n)}}var us={readContext:te,use:ns,useCallback:_t,useContext:_t,useEffect:_t,useImperativeHandle:_t,useLayoutEffect:_t,useInsertionEffect:_t,useMemo:_t,useReducer:_t,useRef:_t,useState:_t,useDebugValue:_t,useDeferredValue:_t,useTransition:_t,useSyncExternalStore:_t,useId:_t,useHostTransitionStatus:_t,useFormState:_t,useActionState:_t,useOptimistic:_t,useMemoCache:_t,useCacheRefresh:_t},Yd={readContext:te,use:ns,useCallback:function(t,e){return ue().memoizedState=[t,e===void 0?null:e],t},useContext:te,useEffect:Ed,useImperativeHandle:function(t,e,n){n=n!=null?n.concat([t]):null,ls(4194308,4,Cd.bind(null,e,t),n)},useLayoutEffect:function(t,e){return ls(4194308,4,t,e)},useInsertionEffect:function(t,e){ls(4,2,t,e)},useMemo:function(t,e){var n=ue();e=e===void 0?null:e;var i=t();if(na){hn(!0);try{t()}finally{hn(!1)}}return n.memoizedState=[i,e],i},useReducer:function(t,e,n){var i=ue();if(n!==void 0){var s=n(e);if(na){hn(!0);try{n(e)}finally{hn(!1)}}}else s=e;return i.memoizedState=i.baseState=s,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:s},i.queue=t,t=t.dispatch=lv.bind(null,ct,t),[i.memoizedState,t]},useRef:function(t){var e=ue();return t={current:t},e.memoizedState=t},useState:function(t){t=or(t);var e=t.queue,n=Ld.bind(null,ct,e);return e.dispatch=n,[t.memoizedState,n]},useDebugValue:fr,useDeferredValue:function(t,e){var n=ue();return dr(n,t,e)},useTransition:function(){var t=or(!1);return t=zd.bind(null,ct,t.queue,!0,!1),ue().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,n){var i=ct,s=ue();if(vt){if(n===void 0)throw Error(r(407));n=n()}else{if(n=e(),Dt===null)throw Error(r(349));(gt&124)!==0||rd(i,e,n)}s.memoizedState=n;var o={value:n,getSnapshot:e};return s.queue=o,Ed(cd.bind(null,i,o,t),[t]),i.flags|=2048,Ba(9,is(),od.bind(null,i,o,n,e),null),n},useId:function(){var t=ue(),e=Dt.identifierPrefix;if(vt){var n=Ie,i=We;n=(i&~(1<<32-he(i)-1)).toString(32)+n,e="«"+e+"R"+n,n=es++,0<n&&(e+="H"+n.toString(32)),e+="»"}else n=Iy++,e="«"+e+"r"+n.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:mr,useFormState:bd,useActionState:bd,useOptimistic:function(t){var e=ue();e.memoizedState=e.baseState=t;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=n,e=pr.bind(null,ct,!0,n),n.dispatch=e,[t,e]},useMemoCache:sr,useCacheRefresh:function(){return ue().memoizedState=iv.bind(null,ct)}},Gd={readContext:te,use:ns,useCallback:Rd,useContext:te,useEffect:Md,useImperativeHandle:wd,useInsertionEffect:jd,useLayoutEffect:Dd,useMemo:Od,useReducer:as,useRef:Ad,useState:function(){return as(nn)},useDebugValue:fr,useDeferredValue:function(t,e){var n=Ht();return Nd(n,At.memoizedState,t,e)},useTransition:function(){var t=as(nn)[0],e=Ht().memoizedState;return[typeof t=="boolean"?t:Bi(t),e]},useSyncExternalStore:ud,useId:Bd,useHostTransitionStatus:mr,useFormState:xd,useActionState:xd,useOptimistic:function(t,e){var n=Ht();return hd(n,At,t,e)},useMemoCache:sr,useCacheRefresh:Ud},sv={readContext:te,use:ns,useCallback:Rd,useContext:te,useEffect:Md,useImperativeHandle:wd,useInsertionEffect:jd,useLayoutEffect:Dd,useMemo:Od,useReducer:rr,useRef:Ad,useState:function(){return rr(nn)},useDebugValue:fr,useDeferredValue:function(t,e){var n=Ht();return At===null?dr(n,t,e):Nd(n,At.memoizedState,t,e)},useTransition:function(){var t=rr(nn)[0],e=Ht().memoizedState;return[typeof t=="boolean"?t:Bi(t),e]},useSyncExternalStore:ud,useId:Bd,useHostTransitionStatus:mr,useFormState:Td,useActionState:Td,useOptimistic:function(t,e){var n=Ht();return At!==null?hd(n,At,t,e):(n.baseState=t,[t,n.queue.dispatch])},useMemoCache:sr,useCacheRefresh:Ud},Ua=null,Hi=0;function rs(t){var e=Hi;return Hi+=1,Ua===null&&(Ua=[]),If(Ua,t,e)}function qi(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function os(t,e){throw e.$$typeof===x?Error(r(525)):(t=Object.prototype.toString.call(e),Error(r(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function Xd(t){var e=t._init;return e(t._payload)}function Zd(t){function e(D,E){if(t){var w=D.deletions;w===null?(D.deletions=[E],D.flags|=16):w.push(E)}}function n(D,E){if(!t)return null;for(;E!==null;)e(D,E),E=E.sibling;return null}function i(D){for(var E=new Map;D!==null;)D.key!==null?E.set(D.key,D):E.set(D.index,D),D=D.sibling;return E}function s(D,E){return D=$e(D,E),D.index=0,D.sibling=null,D}function o(D,E,w){return D.index=w,t?(w=D.alternate,w!==null?(w=w.index,w<E?(D.flags|=67108866,E):w):(D.flags|=67108866,E)):(D.flags|=1048576,E)}function h(D){return t&&D.alternate===null&&(D.flags|=67108866),D}function b(D,E,w,H){return E===null||E.tag!==6?(E=Bu(w,D.mode,H),E.return=D,E):(E=s(E,w),E.return=D,E)}function T(D,E,w,H){var I=w.type;return I===j?L(D,E,w.props.children,H,w.key):E!==null&&(E.elementType===I||typeof I=="object"&&I!==null&&I.$$typeof===K&&Xd(I)===E.type)?(E=s(E,w.props),qi(E,w),E.return=D,E):(E=Ql(w.type,w.key,w.props,null,D.mode,H),qi(E,w),E.return=D,E)}function R(D,E,w,H){return E===null||E.tag!==4||E.stateNode.containerInfo!==w.containerInfo||E.stateNode.implementation!==w.implementation?(E=Uu(w,D.mode,H),E.return=D,E):(E=s(E,w.children||[]),E.return=D,E)}function L(D,E,w,H,I){return E===null||E.tag!==7?(E=Fn(w,D.mode,H,I),E.return=D,E):(E=s(E,w),E.return=D,E)}function q(D,E,w){if(typeof E=="string"&&E!==""||typeof E=="number"||typeof E=="bigint")return E=Bu(""+E,D.mode,w),E.return=D,E;if(typeof E=="object"&&E!==null){switch(E.$$typeof){case S:return w=Ql(E.type,E.key,E.props,null,D.mode,w),qi(w,E),w.return=D,w;case V:return E=Uu(E,D.mode,w),E.return=D,E;case K:var H=E._init;return E=H(E._payload),q(D,E,w)}if(Yt(E)||St(E))return E=Fn(E,D.mode,w,null),E.return=D,E;if(typeof E.then=="function")return q(D,rs(E),w);if(E.$$typeof===M)return q(D,Pl(D,E),w);os(D,E)}return null}function N(D,E,w,H){var I=E!==null?E.key:null;if(typeof w=="string"&&w!==""||typeof w=="number"||typeof w=="bigint")return I!==null?null:b(D,E,""+w,H);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case S:return w.key===I?T(D,E,w,H):null;case V:return w.key===I?R(D,E,w,H):null;case K:return I=w._init,w=I(w._payload),N(D,E,w,H)}if(Yt(w)||St(w))return I!==null?null:L(D,E,w,H,null);if(typeof w.then=="function")return N(D,E,rs(w),H);if(w.$$typeof===M)return N(D,E,Pl(D,w),H);os(D,w)}return null}function z(D,E,w,H,I){if(typeof H=="string"&&H!==""||typeof H=="number"||typeof H=="bigint")return D=D.get(w)||null,b(E,D,""+H,I);if(typeof H=="object"&&H!==null){switch(H.$$typeof){case S:return D=D.get(H.key===null?w:H.key)||null,T(E,D,H,I);case V:return D=D.get(H.key===null?w:H.key)||null,R(E,D,H,I);case K:var dt=H._init;return H=dt(H._payload),z(D,E,w,H,I)}if(Yt(H)||St(H))return D=D.get(w)||null,L(E,D,H,I,null);if(typeof H.then=="function")return z(D,E,w,rs(H),I);if(H.$$typeof===M)return z(D,E,w,Pl(E,H),I);os(E,H)}return null}function lt(D,E,w,H){for(var I=null,dt=null,et=E,at=E=0,Ft=null;et!==null&&at<w.length;at++){et.index>at?(Ft=et,et=null):Ft=et.sibling;var yt=N(D,et,w[at],H);if(yt===null){et===null&&(et=Ft);break}t&&et&&yt.alternate===null&&e(D,et),E=o(yt,E,at),dt===null?I=yt:dt.sibling=yt,dt=yt,et=Ft}if(at===w.length)return n(D,et),vt&&Jn(D,at),I;if(et===null){for(;at<w.length;at++)et=q(D,w[at],H),et!==null&&(E=o(et,E,at),dt===null?I=et:dt.sibling=et,dt=et);return vt&&Jn(D,at),I}for(et=i(et);at<w.length;at++)Ft=z(et,D,at,w[at],H),Ft!==null&&(t&&Ft.alternate!==null&&et.delete(Ft.key===null?at:Ft.key),E=o(Ft,E,at),dt===null?I=Ft:dt.sibling=Ft,dt=Ft);return t&&et.forEach(function(Bn){return e(D,Bn)}),vt&&Jn(D,at),I}function nt(D,E,w,H){if(w==null)throw Error(r(151));for(var I=null,dt=null,et=E,at=E=0,Ft=null,yt=w.next();et!==null&&!yt.done;at++,yt=w.next()){et.index>at?(Ft=et,et=null):Ft=et.sibling;var Bn=N(D,et,yt.value,H);if(Bn===null){et===null&&(et=Ft);break}t&&et&&Bn.alternate===null&&e(D,et),E=o(Bn,E,at),dt===null?I=Bn:dt.sibling=Bn,dt=Bn,et=Ft}if(yt.done)return n(D,et),vt&&Jn(D,at),I;if(et===null){for(;!yt.done;at++,yt=w.next())yt=q(D,yt.value,H),yt!==null&&(E=o(yt,E,at),dt===null?I=yt:dt.sibling=yt,dt=yt);return vt&&Jn(D,at),I}for(et=i(et);!yt.done;at++,yt=w.next())yt=z(et,D,at,yt.value,H),yt!==null&&(t&&yt.alternate!==null&&et.delete(yt.key===null?at:yt.key),E=o(yt,E,at),dt===null?I=yt:dt.sibling=yt,dt=yt);return t&&et.forEach(function(ub){return e(D,ub)}),vt&&Jn(D,at),I}function Mt(D,E,w,H){if(typeof w=="object"&&w!==null&&w.type===j&&w.key===null&&(w=w.props.children),typeof w=="object"&&w!==null){switch(w.$$typeof){case S:t:{for(var I=w.key;E!==null;){if(E.key===I){if(I=w.type,I===j){if(E.tag===7){n(D,E.sibling),H=s(E,w.props.children),H.return=D,D=H;break t}}else if(E.elementType===I||typeof I=="object"&&I!==null&&I.$$typeof===K&&Xd(I)===E.type){n(D,E.sibling),H=s(E,w.props),qi(H,w),H.return=D,D=H;break t}n(D,E);break}else e(D,E);E=E.sibling}w.type===j?(H=Fn(w.props.children,D.mode,H,w.key),H.return=D,D=H):(H=Ql(w.type,w.key,w.props,null,D.mode,H),qi(H,w),H.return=D,D=H)}return h(D);case V:t:{for(I=w.key;E!==null;){if(E.key===I)if(E.tag===4&&E.stateNode.containerInfo===w.containerInfo&&E.stateNode.implementation===w.implementation){n(D,E.sibling),H=s(E,w.children||[]),H.return=D,D=H;break t}else{n(D,E);break}else e(D,E);E=E.sibling}H=Uu(w,D.mode,H),H.return=D,D=H}return h(D);case K:return I=w._init,w=I(w._payload),Mt(D,E,w,H)}if(Yt(w))return lt(D,E,w,H);if(St(w)){if(I=St(w),typeof I!="function")throw Error(r(150));return w=I.call(w),nt(D,E,w,H)}if(typeof w.then=="function")return Mt(D,E,rs(w),H);if(w.$$typeof===M)return Mt(D,E,Pl(D,w),H);os(D,w)}return typeof w=="string"&&w!==""||typeof w=="number"||typeof w=="bigint"?(w=""+w,E!==null&&E.tag===6?(n(D,E.sibling),H=s(E,w),H.return=D,D=H):(n(D,E),H=Bu(w,D.mode,H),H.return=D,D=H),h(D)):n(D,E)}return function(D,E,w,H){try{Hi=0;var I=Mt(D,E,w,H);return Ua=null,I}catch(et){if(et===Ri||et===$l)throw et;var dt=pe(29,et,null,D.mode);return dt.lanes=H,dt.return=D,dt}finally{}}}var La=Zd(!0),Qd=Zd(!1),Ce=G(null),Ye=null;function Sn(t){var e=t.alternate;$(Xt,Xt.current&1),$(Ce,t),Ye===null&&(e===null||za.current!==null||e.memoizedState!==null)&&(Ye=t)}function Kd(t){if(t.tag===22){if($(Xt,Xt.current),$(Ce,t),Ye===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Ye=t)}}else Tn()}function Tn(){$(Xt,Xt.current),$(Ce,Ce.current)}function an(t){P(Ce),Ye===t&&(Ye=null),P(Xt)}var Xt=G(0);function cs(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||lo(n)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function gr(t,e,n,i){e=t.memoizedState,n=n(i,e),n=n==null?e:v({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var yr={enqueueSetState:function(t,e,n){t=t._reactInternals;var i=be(),s=vn(i);s.payload=e,n!=null&&(s.callback=n),e=bn(t,s,i),e!==null&&(xe(e,t,i),Ni(e,t,i))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var i=be(),s=vn(i);s.tag=1,s.payload=e,n!=null&&(s.callback=n),e=bn(t,s,i),e!==null&&(xe(e,t,i),Ni(e,t,i))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=be(),i=vn(n);i.tag=2,e!=null&&(i.callback=e),e=bn(t,i,n),e!==null&&(xe(e,t,n),Ni(e,t,n))}};function kd(t,e,n,i,s,o,h){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(i,o,h):e.prototype&&e.prototype.isPureReactComponent?!Ti(n,i)||!Ti(s,o):!0}function Fd(t,e,n,i){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,i),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,i),e.state!==t&&yr.enqueueReplaceState(e,e.state,null)}function aa(t,e){var n=e;if("ref"in e){n={};for(var i in e)i!=="ref"&&(n[i]=e[i])}if(t=t.defaultProps){n===e&&(n=v({},n));for(var s in t)n[s]===void 0&&(n[s]=t[s])}return n}var fs=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Pd(t){fs(t)}function Jd(t){console.error(t)}function $d(t){fs(t)}function ds(t,e){try{var n=t.onUncaughtError;n(e.value,{componentStack:e.stack})}catch(i){setTimeout(function(){throw i})}}function Wd(t,e,n){try{var i=t.onCaughtError;i(n.value,{componentStack:n.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(s){setTimeout(function(){throw s})}}function vr(t,e,n){return n=vn(n),n.tag=3,n.payload={element:null},n.callback=function(){ds(t,e)},n}function Id(t){return t=vn(t),t.tag=3,t}function th(t,e,n,i){var s=n.type.getDerivedStateFromError;if(typeof s=="function"){var o=i.value;t.payload=function(){return s(o)},t.callback=function(){Wd(e,n,i)}}var h=n.stateNode;h!==null&&typeof h.componentDidCatch=="function"&&(t.callback=function(){Wd(e,n,i),typeof s!="function"&&(Cn===null?Cn=new Set([this]):Cn.add(this));var b=i.stack;this.componentDidCatch(i.value,{componentStack:b!==null?b:""})})}function uv(t,e,n,i,s){if(n.flags|=32768,i!==null&&typeof i=="object"&&typeof i.then=="function"){if(e=n.alternate,e!==null&&Di(e,n,s,!0),n=Ce.current,n!==null){switch(n.tag){case 13:return Ye===null?Gr():n.alternate===null&&zt===0&&(zt=3),n.flags&=-257,n.flags|=65536,n.lanes=s,i===ku?n.flags|=16384:(e=n.updateQueue,e===null?n.updateQueue=new Set([i]):e.add(i),Zr(t,i,s)),!1;case 22:return n.flags|=65536,i===ku?n.flags|=16384:(e=n.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([i])},n.updateQueue=e):(n=e.retryQueue,n===null?e.retryQueue=new Set([i]):n.add(i)),Zr(t,i,s)),!1}throw Error(r(435,n.tag))}return Zr(t,i,s),Gr(),!1}if(vt)return e=Ce.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=s,i!==qu&&(t=Error(r(422),{cause:i}),ji(Ee(t,n)))):(i!==qu&&(e=Error(r(423),{cause:i}),ji(Ee(e,n))),t=t.current.alternate,t.flags|=65536,s&=-s,t.lanes|=s,i=Ee(i,n),s=vr(t.stateNode,i,s),Ju(t,s),zt!==4&&(zt=2)),!1;var o=Error(r(520),{cause:i});if(o=Ee(o,n),ki===null?ki=[o]:ki.push(o),zt!==4&&(zt=2),e===null)return!0;i=Ee(i,n),n=e;do{switch(n.tag){case 3:return n.flags|=65536,t=s&-s,n.lanes|=t,t=vr(n.stateNode,i,t),Ju(n,t),!1;case 1:if(e=n.type,o=n.stateNode,(n.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||o!==null&&typeof o.componentDidCatch=="function"&&(Cn===null||!Cn.has(o))))return n.flags|=65536,s&=-s,n.lanes|=s,s=Id(s),th(s,t,n,i),Ju(n,s),!1}n=n.return}while(n!==null);return!1}var eh=Error(r(461)),Kt=!1;function Pt(t,e,n,i){e.child=t===null?Qd(e,null,n,i):La(e,t.child,n,i)}function nh(t,e,n,i,s){n=n.render;var o=e.ref;if("ref"in i){var h={};for(var b in i)b!=="ref"&&(h[b]=i[b])}else h=i;return ta(e),i=er(t,e,n,h,o,s),b=nr(),t!==null&&!Kt?(ar(t,e,s),ln(t,e,s)):(vt&&b&&Lu(e),e.flags|=1,Pt(t,e,i,s),e.child)}function ah(t,e,n,i,s){if(t===null){var o=n.type;return typeof o=="function"&&!_u(o)&&o.defaultProps===void 0&&n.compare===null?(e.tag=15,e.type=o,ih(t,e,o,i,s)):(t=Ql(n.type,null,i,e,e.mode,s),t.ref=e.ref,t.return=e,e.child=t)}if(o=t.child,!jr(t,s)){var h=o.memoizedProps;if(n=n.compare,n=n!==null?n:Ti,n(h,i)&&t.ref===e.ref)return ln(t,e,s)}return e.flags|=1,t=$e(o,i),t.ref=e.ref,t.return=e,e.child=t}function ih(t,e,n,i,s){if(t!==null){var o=t.memoizedProps;if(Ti(o,i)&&t.ref===e.ref)if(Kt=!1,e.pendingProps=i=o,jr(t,s))(t.flags&131072)!==0&&(Kt=!0);else return e.lanes=t.lanes,ln(t,e,s)}return br(t,e,n,i,s)}function lh(t,e,n){var i=e.pendingProps,s=i.children,o=t!==null?t.memoizedState:null;if(i.mode==="hidden"){if((e.flags&128)!==0){if(i=o!==null?o.baseLanes|n:n,t!==null){for(s=e.child=t.child,o=0;s!==null;)o=o|s.lanes|s.childLanes,s=s.sibling;e.childLanes=o&~i}else e.childLanes=0,e.child=null;return sh(t,e,i,n)}if((n&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&Jl(e,o!==null?o.cachePool:null),o!==null?id(e,o):Wu(),Kd(e);else return e.lanes=e.childLanes=536870912,sh(t,e,o!==null?o.baseLanes|n:n,n)}else o!==null?(Jl(e,o.cachePool),id(e,o),Tn(),e.memoizedState=null):(t!==null&&Jl(e,null),Wu(),Tn());return Pt(t,e,s,n),e.child}function sh(t,e,n,i){var s=Ku();return s=s===null?null:{parent:Gt._currentValue,pool:s},e.memoizedState={baseLanes:n,cachePool:s},t!==null&&Jl(e,null),Wu(),Kd(e),t!==null&&Di(t,e,i,!0),null}function hs(t,e){var n=e.ref;if(n===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(r(284));(t===null||t.ref!==n)&&(e.flags|=4194816)}}function br(t,e,n,i,s){return ta(e),n=er(t,e,n,i,void 0,s),i=nr(),t!==null&&!Kt?(ar(t,e,s),ln(t,e,s)):(vt&&i&&Lu(e),e.flags|=1,Pt(t,e,n,s),e.child)}function uh(t,e,n,i,s,o){return ta(e),e.updateQueue=null,n=sd(e,i,n,s),ld(t),i=nr(),t!==null&&!Kt?(ar(t,e,o),ln(t,e,o)):(vt&&i&&Lu(e),e.flags|=1,Pt(t,e,n,o),e.child)}function rh(t,e,n,i,s){if(ta(e),e.stateNode===null){var o=Ca,h=n.contextType;typeof h=="object"&&h!==null&&(o=te(h)),o=new n(i,o),e.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,o.updater=yr,e.stateNode=o,o._reactInternals=e,o=e.stateNode,o.props=i,o.state=e.memoizedState,o.refs={},Fu(e),h=n.contextType,o.context=typeof h=="object"&&h!==null?te(h):Ca,o.state=e.memoizedState,h=n.getDerivedStateFromProps,typeof h=="function"&&(gr(e,n,h,i),o.state=e.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(h=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),h!==o.state&&yr.enqueueReplaceState(o,o.state,null),Vi(e,i,o,s),zi(),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308),i=!0}else if(t===null){o=e.stateNode;var b=e.memoizedProps,T=aa(n,b);o.props=T;var R=o.context,L=n.contextType;h=Ca,typeof L=="object"&&L!==null&&(h=te(L));var q=n.getDerivedStateFromProps;L=typeof q=="function"||typeof o.getSnapshotBeforeUpdate=="function",b=e.pendingProps!==b,L||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(b||R!==h)&&Fd(e,o,i,h),yn=!1;var N=e.memoizedState;o.state=N,Vi(e,i,o,s),zi(),R=e.memoizedState,b||N!==R||yn?(typeof q=="function"&&(gr(e,n,q,i),R=e.memoizedState),(T=yn||kd(e,n,T,i,N,R,h))?(L||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(e.flags|=4194308)):(typeof o.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=i,e.memoizedState=R),o.props=i,o.state=R,o.context=h,i=T):(typeof o.componentDidMount=="function"&&(e.flags|=4194308),i=!1)}else{o=e.stateNode,Pu(t,e),h=e.memoizedProps,L=aa(n,h),o.props=L,q=e.pendingProps,N=o.context,R=n.contextType,T=Ca,typeof R=="object"&&R!==null&&(T=te(R)),b=n.getDerivedStateFromProps,(R=typeof b=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(h!==q||N!==T)&&Fd(e,o,i,T),yn=!1,N=e.memoizedState,o.state=N,Vi(e,i,o,s),zi();var z=e.memoizedState;h!==q||N!==z||yn||t!==null&&t.dependencies!==null&&Fl(t.dependencies)?(typeof b=="function"&&(gr(e,n,b,i),z=e.memoizedState),(L=yn||kd(e,n,L,i,N,z,T)||t!==null&&t.dependencies!==null&&Fl(t.dependencies))?(R||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(i,z,T),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(i,z,T)),typeof o.componentDidUpdate=="function"&&(e.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof o.componentDidUpdate!="function"||h===t.memoizedProps&&N===t.memoizedState||(e.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||h===t.memoizedProps&&N===t.memoizedState||(e.flags|=1024),e.memoizedProps=i,e.memoizedState=z),o.props=i,o.state=z,o.context=T,i=L):(typeof o.componentDidUpdate!="function"||h===t.memoizedProps&&N===t.memoizedState||(e.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||h===t.memoizedProps&&N===t.memoizedState||(e.flags|=1024),i=!1)}return o=i,hs(t,e),i=(e.flags&128)!==0,o||i?(o=e.stateNode,n=i&&typeof n.getDerivedStateFromError!="function"?null:o.render(),e.flags|=1,t!==null&&i?(e.child=La(e,t.child,null,s),e.child=La(e,null,n,s)):Pt(t,e,n,s),e.memoizedState=o.state,t=e.child):t=ln(t,e,s),t}function oh(t,e,n,i){return Mi(),e.flags|=256,Pt(t,e,n,i),e.child}var xr={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Sr(t){return{baseLanes:t,cachePool:Jf()}}function Tr(t,e,n){return t=t!==null?t.childLanes&~n:0,e&&(t|=we),t}function ch(t,e,n){var i=e.pendingProps,s=!1,o=(e.flags&128)!==0,h;if((h=o)||(h=t!==null&&t.memoizedState===null?!1:(Xt.current&2)!==0),h&&(s=!0,e.flags&=-129),h=(e.flags&32)!==0,e.flags&=-33,t===null){if(vt){if(s?Sn(e):Tn(),vt){var b=Nt,T;if(T=b){t:{for(T=b,b=qe;T.nodeType!==8;){if(!b){b=null;break t}if(T=Ue(T.nextSibling),T===null){b=null;break t}}b=T}b!==null?(e.memoizedState={dehydrated:b,treeContext:Pn!==null?{id:We,overflow:Ie}:null,retryLane:536870912,hydrationErrors:null},T=pe(18,null,null,0),T.stateNode=b,T.return=e,e.child=T,ae=e,Nt=null,T=!0):T=!1}T||Wn(e)}if(b=e.memoizedState,b!==null&&(b=b.dehydrated,b!==null))return lo(b)?e.lanes=32:e.lanes=536870912,null;an(e)}return b=i.children,i=i.fallback,s?(Tn(),s=e.mode,b=ms({mode:"hidden",children:b},s),i=Fn(i,s,n,null),b.return=e,i.return=e,b.sibling=i,e.child=b,s=e.child,s.memoizedState=Sr(n),s.childLanes=Tr(t,h,n),e.memoizedState=xr,i):(Sn(e),Ar(e,b))}if(T=t.memoizedState,T!==null&&(b=T.dehydrated,b!==null)){if(o)e.flags&256?(Sn(e),e.flags&=-257,e=Er(t,e,n)):e.memoizedState!==null?(Tn(),e.child=t.child,e.flags|=128,e=null):(Tn(),s=i.fallback,b=e.mode,i=ms({mode:"visible",children:i.children},b),s=Fn(s,b,n,null),s.flags|=2,i.return=e,s.return=e,i.sibling=s,e.child=i,La(e,t.child,null,n),i=e.child,i.memoizedState=Sr(n),i.childLanes=Tr(t,h,n),e.memoizedState=xr,e=s);else if(Sn(e),lo(b)){if(h=b.nextSibling&&b.nextSibling.dataset,h)var R=h.dgst;h=R,i=Error(r(419)),i.stack="",i.digest=h,ji({value:i,source:null,stack:null}),e=Er(t,e,n)}else if(Kt||Di(t,e,n,!1),h=(n&t.childLanes)!==0,Kt||h){if(h=Dt,h!==null&&(i=n&-n,i=(i&42)!==0?1:su(i),i=(i&(h.suspendedLanes|n))!==0?0:i,i!==0&&i!==T.retryLane))throw T.retryLane=i,Da(t,i),xe(h,t,i),eh;b.data==="$?"||Gr(),e=Er(t,e,n)}else b.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=T.treeContext,Nt=Ue(b.nextSibling),ae=e,vt=!0,$n=null,qe=!1,t!==null&&(je[De++]=We,je[De++]=Ie,je[De++]=Pn,We=t.id,Ie=t.overflow,Pn=e),e=Ar(e,i.children),e.flags|=4096);return e}return s?(Tn(),s=i.fallback,b=e.mode,T=t.child,R=T.sibling,i=$e(T,{mode:"hidden",children:i.children}),i.subtreeFlags=T.subtreeFlags&65011712,R!==null?s=$e(R,s):(s=Fn(s,b,n,null),s.flags|=2),s.return=e,i.return=e,i.sibling=s,e.child=i,i=s,s=e.child,b=t.child.memoizedState,b===null?b=Sr(n):(T=b.cachePool,T!==null?(R=Gt._currentValue,T=T.parent!==R?{parent:R,pool:R}:T):T=Jf(),b={baseLanes:b.baseLanes|n,cachePool:T}),s.memoizedState=b,s.childLanes=Tr(t,h,n),e.memoizedState=xr,i):(Sn(e),n=t.child,t=n.sibling,n=$e(n,{mode:"visible",children:i.children}),n.return=e,n.sibling=null,t!==null&&(h=e.deletions,h===null?(e.deletions=[t],e.flags|=16):h.push(t)),e.child=n,e.memoizedState=null,n)}function Ar(t,e){return e=ms({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function ms(t,e){return t=pe(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function Er(t,e,n){return La(e,t.child,null,n),t=Ar(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function fh(t,e,n){t.lanes|=e;var i=t.alternate;i!==null&&(i.lanes|=e),Gu(t.return,e,n)}function Mr(t,e,n,i,s){var o=t.memoizedState;o===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:i,tail:n,tailMode:s}:(o.isBackwards=e,o.rendering=null,o.renderingStartTime=0,o.last=i,o.tail=n,o.tailMode=s)}function dh(t,e,n){var i=e.pendingProps,s=i.revealOrder,o=i.tail;if(Pt(t,e,i.children,n),i=Xt.current,(i&2)!==0)i=i&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&fh(t,n,e);else if(t.tag===19)fh(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}i&=1}switch($(Xt,i),s){case"forwards":for(n=e.child,s=null;n!==null;)t=n.alternate,t!==null&&cs(t)===null&&(s=n),n=n.sibling;n=s,n===null?(s=e.child,e.child=null):(s=n.sibling,n.sibling=null),Mr(e,!1,s,n,o);break;case"backwards":for(n=null,s=e.child,e.child=null;s!==null;){if(t=s.alternate,t!==null&&cs(t)===null){e.child=s;break}t=s.sibling,s.sibling=n,n=s,s=t}Mr(e,!0,n,null,o);break;case"together":Mr(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function ln(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),Dn|=e.lanes,(n&e.childLanes)===0)if(t!==null){if(Di(t,e,n,!1),(n&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(r(153));if(e.child!==null){for(t=e.child,n=$e(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=$e(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function jr(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&Fl(t)))}function rv(t,e,n){switch(e.tag){case 3:Tt(e,e.stateNode.containerInfo),gn(e,Gt,t.memoizedState.cache),Mi();break;case 27:case 5:Yn(e);break;case 4:Tt(e,e.stateNode.containerInfo);break;case 10:gn(e,e.type,e.memoizedProps.value);break;case 13:var i=e.memoizedState;if(i!==null)return i.dehydrated!==null?(Sn(e),e.flags|=128,null):(n&e.child.childLanes)!==0?ch(t,e,n):(Sn(e),t=ln(t,e,n),t!==null?t.sibling:null);Sn(e);break;case 19:var s=(t.flags&128)!==0;if(i=(n&e.childLanes)!==0,i||(Di(t,e,n,!1),i=(n&e.childLanes)!==0),s){if(i)return dh(t,e,n);e.flags|=128}if(s=e.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),$(Xt,Xt.current),i)break;return null;case 22:case 23:return e.lanes=0,lh(t,e,n);case 24:gn(e,Gt,t.memoizedState.cache)}return ln(t,e,n)}function hh(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps)Kt=!0;else{if(!jr(t,n)&&(e.flags&128)===0)return Kt=!1,rv(t,e,n);Kt=(t.flags&131072)!==0}else Kt=!1,vt&&(e.flags&1048576)!==0&&Xf(e,kl,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var i=e.elementType,s=i._init;if(i=s(i._payload),e.type=i,typeof i=="function")_u(i)?(t=aa(i,t),e.tag=1,e=rh(null,e,i,t,n)):(e.tag=0,e=br(null,e,i,t,n));else{if(i!=null){if(s=i.$$typeof,s===_){e.tag=11,e=nh(null,e,i,t,n);break t}else if(s===Z){e.tag=14,e=ah(null,e,i,t,n);break t}}throw e=fe(i)||i,Error(r(306,e,""))}}return e;case 0:return br(t,e,e.type,e.pendingProps,n);case 1:return i=e.type,s=aa(i,e.pendingProps),rh(t,e,i,s,n);case 3:t:{if(Tt(e,e.stateNode.containerInfo),t===null)throw Error(r(387));i=e.pendingProps;var o=e.memoizedState;s=o.element,Pu(t,e),Vi(e,i,null,n);var h=e.memoizedState;if(i=h.cache,gn(e,Gt,i),i!==o.cache&&Xu(e,[Gt],n,!0),zi(),i=h.element,o.isDehydrated)if(o={element:i,isDehydrated:!1,cache:h.cache},e.updateQueue.baseState=o,e.memoizedState=o,e.flags&256){e=oh(t,e,i,n);break t}else if(i!==s){s=Ee(Error(r(424)),e),ji(s),e=oh(t,e,i,n);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Nt=Ue(t.firstChild),ae=e,vt=!0,$n=null,qe=!0,n=Qd(e,null,i,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(Mi(),i===s){e=ln(t,e,n);break t}Pt(t,e,i,n)}e=e.child}return e;case 26:return hs(t,e),t===null?(n=ym(e.type,null,e.pendingProps,null))?e.memoizedState=n:vt||(n=e.type,t=e.pendingProps,i=Cs(it.current).createElement(n),i[It]=e,i[le]=t,$t(i,n,t),Qt(i),e.stateNode=i):e.memoizedState=ym(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return Yn(e),t===null&&vt&&(i=e.stateNode=mm(e.type,e.pendingProps,it.current),ae=e,qe=!0,s=Nt,On(e.type)?(so=s,Nt=Ue(i.firstChild)):Nt=s),Pt(t,e,e.pendingProps.children,n),hs(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&vt&&((s=i=Nt)&&(i=Bv(i,e.type,e.pendingProps,qe),i!==null?(e.stateNode=i,ae=e,Nt=Ue(i.firstChild),qe=!1,s=!0):s=!1),s||Wn(e)),Yn(e),s=e.type,o=e.pendingProps,h=t!==null?t.memoizedProps:null,i=o.children,no(s,o)?i=null:h!==null&&no(s,h)&&(e.flags|=32),e.memoizedState!==null&&(s=er(t,e,tv,null,null,n),nl._currentValue=s),hs(t,e),Pt(t,e,i,n),e.child;case 6:return t===null&&vt&&((t=n=Nt)&&(n=Uv(n,e.pendingProps,qe),n!==null?(e.stateNode=n,ae=e,Nt=null,t=!0):t=!1),t||Wn(e)),null;case 13:return ch(t,e,n);case 4:return Tt(e,e.stateNode.containerInfo),i=e.pendingProps,t===null?e.child=La(e,null,i,n):Pt(t,e,i,n),e.child;case 11:return nh(t,e,e.type,e.pendingProps,n);case 7:return Pt(t,e,e.pendingProps,n),e.child;case 8:return Pt(t,e,e.pendingProps.children,n),e.child;case 12:return Pt(t,e,e.pendingProps.children,n),e.child;case 10:return i=e.pendingProps,gn(e,e.type,i.value),Pt(t,e,i.children,n),e.child;case 9:return s=e.type._context,i=e.pendingProps.children,ta(e),s=te(s),i=i(s),e.flags|=1,Pt(t,e,i,n),e.child;case 14:return ah(t,e,e.type,e.pendingProps,n);case 15:return ih(t,e,e.type,e.pendingProps,n);case 19:return dh(t,e,n);case 31:return i=e.pendingProps,n=e.mode,i={mode:i.mode,children:i.children},t===null?(n=ms(i,n),n.ref=e.ref,e.child=n,n.return=e,e=n):(n=$e(t.child,i),n.ref=e.ref,e.child=n,n.return=e,e=n),e;case 22:return lh(t,e,n);case 24:return ta(e),i=te(Gt),t===null?(s=Ku(),s===null&&(s=Dt,o=Zu(),s.pooledCache=o,o.refCount++,o!==null&&(s.pooledCacheLanes|=n),s=o),e.memoizedState={parent:i,cache:s},Fu(e),gn(e,Gt,s)):((t.lanes&n)!==0&&(Pu(t,e),Vi(e,null,null,n),zi()),s=t.memoizedState,o=e.memoizedState,s.parent!==i?(s={parent:i,cache:i},e.memoizedState=s,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=s),gn(e,Gt,i)):(i=o.cache,gn(e,Gt,i),i!==s.cache&&Xu(e,[Gt],n,!0))),Pt(t,e,e.pendingProps.children,n),e.child;case 29:throw e.pendingProps}throw Error(r(156,e.tag))}function sn(t){t.flags|=4}function mh(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Tm(e)){if(e=Ce.current,e!==null&&((gt&4194048)===gt?Ye!==null:(gt&62914560)!==gt&&(gt&536870912)===0||e!==Ye))throw Oi=ku,$f;t.flags|=8192}}function ps(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Qc():536870912,t.lanes|=e,Ga|=e)}function Yi(t,e){if(!vt)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var i=null;n!==null;)n.alternate!==null&&(i=n),n=n.sibling;i===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:i.sibling=null}}function Rt(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,i=0;if(e)for(var s=t.child;s!==null;)n|=s.lanes|s.childLanes,i|=s.subtreeFlags&65011712,i|=s.flags&65011712,s.return=t,s=s.sibling;else for(s=t.child;s!==null;)n|=s.lanes|s.childLanes,i|=s.subtreeFlags,i|=s.flags,s.return=t,s=s.sibling;return t.subtreeFlags|=i,t.childLanes=n,e}function ov(t,e,n){var i=e.pendingProps;switch(Hu(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Rt(e),null;case 1:return Rt(e),null;case 3:return n=e.stateNode,i=null,t!==null&&(i=t.memoizedState.cache),e.memoizedState.cache!==i&&(e.flags|=2048),en(Gt),Ve(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(t===null||t.child===null)&&(Ei(e)?sn(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,Kf())),Rt(e),null;case 26:return n=e.memoizedState,t===null?(sn(e),n!==null?(Rt(e),mh(e,n)):(Rt(e),e.flags&=-16777217)):n?n!==t.memoizedState?(sn(e),Rt(e),mh(e,n)):(Rt(e),e.flags&=-16777217):(t.memoizedProps!==i&&sn(e),Rt(e),e.flags&=-16777217),null;case 27:Fe(e),n=it.current;var s=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==i&&sn(e);else{if(!i){if(e.stateNode===null)throw Error(r(166));return Rt(e),null}t=tt.current,Ei(e)?Zf(e):(t=mm(s,i,n),e.stateNode=t,sn(e))}return Rt(e),null;case 5:if(Fe(e),n=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==i&&sn(e);else{if(!i){if(e.stateNode===null)throw Error(r(166));return Rt(e),null}if(t=tt.current,Ei(e))Zf(e);else{switch(s=Cs(it.current),t){case 1:t=s.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:t=s.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":t=s.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":t=s.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":t=s.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof i.is=="string"?s.createElement("select",{is:i.is}):s.createElement("select"),i.multiple?t.multiple=!0:i.size&&(t.size=i.size);break;default:t=typeof i.is=="string"?s.createElement(n,{is:i.is}):s.createElement(n)}}t[It]=e,t[le]=i;t:for(s=e.child;s!==null;){if(s.tag===5||s.tag===6)t.appendChild(s.stateNode);else if(s.tag!==4&&s.tag!==27&&s.child!==null){s.child.return=s,s=s.child;continue}if(s===e)break t;for(;s.sibling===null;){if(s.return===null||s.return===e)break t;s=s.return}s.sibling.return=s.return,s=s.sibling}e.stateNode=t;t:switch($t(t,n,i),n){case"button":case"input":case"select":case"textarea":t=!!i.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&sn(e)}}return Rt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==i&&sn(e);else{if(typeof i!="string"&&e.stateNode===null)throw Error(r(166));if(t=it.current,Ei(e)){if(t=e.stateNode,n=e.memoizedProps,i=null,s=ae,s!==null)switch(s.tag){case 27:case 5:i=s.memoizedProps}t[It]=e,t=!!(t.nodeValue===n||i!==null&&i.suppressHydrationWarning===!0||um(t.nodeValue,n)),t||Wn(e)}else t=Cs(t).createTextNode(i),t[It]=e,e.stateNode=t}return Rt(e),null;case 13:if(i=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(s=Ei(e),i!==null&&i.dehydrated!==null){if(t===null){if(!s)throw Error(r(318));if(s=e.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(r(317));s[It]=e}else Mi(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Rt(e),s=!1}else s=Kf(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=s),s=!0;if(!s)return e.flags&256?(an(e),e):(an(e),null)}if(an(e),(e.flags&128)!==0)return e.lanes=n,e;if(n=i!==null,t=t!==null&&t.memoizedState!==null,n){i=e.child,s=null,i.alternate!==null&&i.alternate.memoizedState!==null&&i.alternate.memoizedState.cachePool!==null&&(s=i.alternate.memoizedState.cachePool.pool);var o=null;i.memoizedState!==null&&i.memoizedState.cachePool!==null&&(o=i.memoizedState.cachePool.pool),o!==s&&(i.flags|=2048)}return n!==t&&n&&(e.child.flags|=8192),ps(e,e.updateQueue),Rt(e),null;case 4:return Ve(),t===null&&$r(e.stateNode.containerInfo),Rt(e),null;case 10:return en(e.type),Rt(e),null;case 19:if(P(Xt),s=e.memoizedState,s===null)return Rt(e),null;if(i=(e.flags&128)!==0,o=s.rendering,o===null)if(i)Yi(s,!1);else{if(zt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(o=cs(t),o!==null){for(e.flags|=128,Yi(s,!1),t=o.updateQueue,e.updateQueue=t,ps(e,t),e.subtreeFlags=0,t=n,n=e.child;n!==null;)Gf(n,t),n=n.sibling;return $(Xt,Xt.current&1|2),e.child}t=t.sibling}s.tail!==null&&He()>vs&&(e.flags|=128,i=!0,Yi(s,!1),e.lanes=4194304)}else{if(!i)if(t=cs(o),t!==null){if(e.flags|=128,i=!0,t=t.updateQueue,e.updateQueue=t,ps(e,t),Yi(s,!0),s.tail===null&&s.tailMode==="hidden"&&!o.alternate&&!vt)return Rt(e),null}else 2*He()-s.renderingStartTime>vs&&n!==536870912&&(e.flags|=128,i=!0,Yi(s,!1),e.lanes=4194304);s.isBackwards?(o.sibling=e.child,e.child=o):(t=s.last,t!==null?t.sibling=o:e.child=o,s.last=o)}return s.tail!==null?(e=s.tail,s.rendering=e,s.tail=e.sibling,s.renderingStartTime=He(),e.sibling=null,t=Xt.current,$(Xt,i?t&1|2:t&1),e):(Rt(e),null);case 22:case 23:return an(e),Iu(),i=e.memoizedState!==null,t!==null?t.memoizedState!==null!==i&&(e.flags|=8192):i&&(e.flags|=8192),i?(n&536870912)!==0&&(e.flags&128)===0&&(Rt(e),e.subtreeFlags&6&&(e.flags|=8192)):Rt(e),n=e.updateQueue,n!==null&&ps(e,n.retryQueue),n=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),i=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(i=e.memoizedState.cachePool.pool),i!==n&&(e.flags|=2048),t!==null&&P(ea),null;case 24:return n=null,t!==null&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),en(Gt),Rt(e),null;case 25:return null;case 30:return null}throw Error(r(156,e.tag))}function cv(t,e){switch(Hu(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return en(Gt),Ve(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return Fe(e),null;case 13:if(an(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(r(340));Mi()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return P(Xt),null;case 4:return Ve(),null;case 10:return en(e.type),null;case 22:case 23:return an(e),Iu(),t!==null&&P(ea),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return en(Gt),null;case 25:return null;default:return null}}function ph(t,e){switch(Hu(e),e.tag){case 3:en(Gt),Ve();break;case 26:case 27:case 5:Fe(e);break;case 4:Ve();break;case 13:an(e);break;case 19:P(Xt);break;case 10:en(e.type);break;case 22:case 23:an(e),Iu(),t!==null&&P(ea);break;case 24:en(Gt)}}function Gi(t,e){try{var n=e.updateQueue,i=n!==null?n.lastEffect:null;if(i!==null){var s=i.next;n=s;do{if((n.tag&t)===t){i=void 0;var o=n.create,h=n.inst;i=o(),h.destroy=i}n=n.next}while(n!==s)}}catch(b){jt(e,e.return,b)}}function An(t,e,n){try{var i=e.updateQueue,s=i!==null?i.lastEffect:null;if(s!==null){var o=s.next;i=o;do{if((i.tag&t)===t){var h=i.inst,b=h.destroy;if(b!==void 0){h.destroy=void 0,s=e;var T=n,R=b;try{R()}catch(L){jt(s,T,L)}}}i=i.next}while(i!==o)}}catch(L){jt(e,e.return,L)}}function gh(t){var e=t.updateQueue;if(e!==null){var n=t.stateNode;try{ad(e,n)}catch(i){jt(t,t.return,i)}}}function yh(t,e,n){n.props=aa(t.type,t.memoizedProps),n.state=t.memoizedState;try{n.componentWillUnmount()}catch(i){jt(t,e,i)}}function Xi(t,e){try{var n=t.ref;if(n!==null){switch(t.tag){case 26:case 27:case 5:var i=t.stateNode;break;case 30:i=t.stateNode;break;default:i=t.stateNode}typeof n=="function"?t.refCleanup=n(i):n.current=i}}catch(s){jt(t,e,s)}}function Ge(t,e){var n=t.ref,i=t.refCleanup;if(n!==null)if(typeof i=="function")try{i()}catch(s){jt(t,e,s)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(s){jt(t,e,s)}else n.current=null}function vh(t){var e=t.type,n=t.memoizedProps,i=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":n.autoFocus&&i.focus();break t;case"img":n.src?i.src=n.src:n.srcSet&&(i.srcset=n.srcSet)}}catch(s){jt(t,t.return,s)}}function Dr(t,e,n){try{var i=t.stateNode;Ov(i,t.type,n,e),i[le]=e}catch(s){jt(t,t.return,s)}}function bh(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&On(t.type)||t.tag===4}function Cr(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||bh(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&On(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function wr(t,e,n){var i=t.tag;if(i===5||i===6)t=t.stateNode,e?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(t,e):(e=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,e.appendChild(t),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=Ds));else if(i!==4&&(i===27&&On(t.type)&&(n=t.stateNode,e=null),t=t.child,t!==null))for(wr(t,e,n),t=t.sibling;t!==null;)wr(t,e,n),t=t.sibling}function gs(t,e,n){var i=t.tag;if(i===5||i===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(i!==4&&(i===27&&On(t.type)&&(n=t.stateNode),t=t.child,t!==null))for(gs(t,e,n),t=t.sibling;t!==null;)gs(t,e,n),t=t.sibling}function xh(t){var e=t.stateNode,n=t.memoizedProps;try{for(var i=t.type,s=e.attributes;s.length;)e.removeAttributeNode(s[0]);$t(e,i,n),e[It]=t,e[le]=n}catch(o){jt(t,t.return,o)}}var un=!1,Bt=!1,Rr=!1,Sh=typeof WeakSet=="function"?WeakSet:Set,kt=null;function fv(t,e){if(t=t.containerInfo,to=Vs,t=Nf(t),Cu(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else t:{n=(n=t.ownerDocument)&&n.defaultView||window;var i=n.getSelection&&n.getSelection();if(i&&i.rangeCount!==0){n=i.anchorNode;var s=i.anchorOffset,o=i.focusNode;i=i.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break t}var h=0,b=-1,T=-1,R=0,L=0,q=t,N=null;e:for(;;){for(var z;q!==n||s!==0&&q.nodeType!==3||(b=h+s),q!==o||i!==0&&q.nodeType!==3||(T=h+i),q.nodeType===3&&(h+=q.nodeValue.length),(z=q.firstChild)!==null;)N=q,q=z;for(;;){if(q===t)break e;if(N===n&&++R===s&&(b=h),N===o&&++L===i&&(T=h),(z=q.nextSibling)!==null)break;q=N,N=q.parentNode}q=z}n=b===-1||T===-1?null:{start:b,end:T}}else n=null}n=n||{start:0,end:0}}else n=null;for(eo={focusedElem:t,selectionRange:n},Vs=!1,kt=e;kt!==null;)if(e=kt,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,kt=t;else for(;kt!==null;){switch(e=kt,o=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&o!==null){t=void 0,n=e,s=o.memoizedProps,o=o.memoizedState,i=n.stateNode;try{var lt=aa(n.type,s,n.elementType===n.type);t=i.getSnapshotBeforeUpdate(lt,o),i.__reactInternalSnapshotBeforeUpdate=t}catch(nt){jt(n,n.return,nt)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,n=t.nodeType,n===9)io(t);else if(n===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":io(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(r(163))}if(t=e.sibling,t!==null){t.return=e.return,kt=t;break}kt=e.return}}function Th(t,e,n){var i=n.flags;switch(n.tag){case 0:case 11:case 15:En(t,n),i&4&&Gi(5,n);break;case 1:if(En(t,n),i&4)if(t=n.stateNode,e===null)try{t.componentDidMount()}catch(h){jt(n,n.return,h)}else{var s=aa(n.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(s,e,t.__reactInternalSnapshotBeforeUpdate)}catch(h){jt(n,n.return,h)}}i&64&&gh(n),i&512&&Xi(n,n.return);break;case 3:if(En(t,n),i&64&&(t=n.updateQueue,t!==null)){if(e=null,n.child!==null)switch(n.child.tag){case 27:case 5:e=n.child.stateNode;break;case 1:e=n.child.stateNode}try{ad(t,e)}catch(h){jt(n,n.return,h)}}break;case 27:e===null&&i&4&&xh(n);case 26:case 5:En(t,n),e===null&&i&4&&vh(n),i&512&&Xi(n,n.return);break;case 12:En(t,n);break;case 13:En(t,n),i&4&&Mh(t,n),i&64&&(t=n.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(n=xv.bind(null,n),Lv(t,n))));break;case 22:if(i=n.memoizedState!==null||un,!i){e=e!==null&&e.memoizedState!==null||Bt,s=un;var o=Bt;un=i,(Bt=e)&&!o?Mn(t,n,(n.subtreeFlags&8772)!==0):En(t,n),un=s,Bt=o}break;case 30:break;default:En(t,n)}}function Ah(t){var e=t.alternate;e!==null&&(t.alternate=null,Ah(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&ou(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Ct=null,re=!1;function rn(t,e,n){for(n=n.child;n!==null;)Eh(t,e,n),n=n.sibling}function Eh(t,e,n){if(de&&typeof de.onCommitFiberUnmount=="function")try{de.onCommitFiberUnmount(ci,n)}catch{}switch(n.tag){case 26:Bt||Ge(n,e),rn(t,e,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:Bt||Ge(n,e);var i=Ct,s=re;On(n.type)&&(Ct=n.stateNode,re=!1),rn(t,e,n),Wi(n.stateNode),Ct=i,re=s;break;case 5:Bt||Ge(n,e);case 6:if(i=Ct,s=re,Ct=null,rn(t,e,n),Ct=i,re=s,Ct!==null)if(re)try{(Ct.nodeType===9?Ct.body:Ct.nodeName==="HTML"?Ct.ownerDocument.body:Ct).removeChild(n.stateNode)}catch(o){jt(n,e,o)}else try{Ct.removeChild(n.stateNode)}catch(o){jt(n,e,o)}break;case 18:Ct!==null&&(re?(t=Ct,dm(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,n.stateNode),sl(t)):dm(Ct,n.stateNode));break;case 4:i=Ct,s=re,Ct=n.stateNode.containerInfo,re=!0,rn(t,e,n),Ct=i,re=s;break;case 0:case 11:case 14:case 15:Bt||An(2,n,e),Bt||An(4,n,e),rn(t,e,n);break;case 1:Bt||(Ge(n,e),i=n.stateNode,typeof i.componentWillUnmount=="function"&&yh(n,e,i)),rn(t,e,n);break;case 21:rn(t,e,n);break;case 22:Bt=(i=Bt)||n.memoizedState!==null,rn(t,e,n),Bt=i;break;default:rn(t,e,n)}}function Mh(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{sl(t)}catch(n){jt(e,e.return,n)}}function dv(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Sh),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Sh),e;default:throw Error(r(435,t.tag))}}function Or(t,e){var n=dv(t);e.forEach(function(i){var s=Sv.bind(null,t,i);n.has(i)||(n.add(i),i.then(s,s))})}function ge(t,e){var n=e.deletions;if(n!==null)for(var i=0;i<n.length;i++){var s=n[i],o=t,h=e,b=h;t:for(;b!==null;){switch(b.tag){case 27:if(On(b.type)){Ct=b.stateNode,re=!1;break t}break;case 5:Ct=b.stateNode,re=!1;break t;case 3:case 4:Ct=b.stateNode.containerInfo,re=!0;break t}b=b.return}if(Ct===null)throw Error(r(160));Eh(o,h,s),Ct=null,re=!1,o=s.alternate,o!==null&&(o.return=null),s.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)jh(e,t),e=e.sibling}var Be=null;function jh(t,e){var n=t.alternate,i=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:ge(e,t),ye(t),i&4&&(An(3,t,t.return),Gi(3,t),An(5,t,t.return));break;case 1:ge(e,t),ye(t),i&512&&(Bt||n===null||Ge(n,n.return)),i&64&&un&&(t=t.updateQueue,t!==null&&(i=t.callbacks,i!==null&&(n=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=n===null?i:n.concat(i))));break;case 26:var s=Be;if(ge(e,t),ye(t),i&512&&(Bt||n===null||Ge(n,n.return)),i&4){var o=n!==null?n.memoizedState:null;if(i=t.memoizedState,n===null)if(i===null)if(t.stateNode===null){t:{i=t.type,n=t.memoizedProps,s=s.ownerDocument||s;e:switch(i){case"title":o=s.getElementsByTagName("title")[0],(!o||o[hi]||o[It]||o.namespaceURI==="http://www.w3.org/2000/svg"||o.hasAttribute("itemprop"))&&(o=s.createElement(i),s.head.insertBefore(o,s.querySelector("head > title"))),$t(o,i,n),o[It]=t,Qt(o),i=o;break t;case"link":var h=xm("link","href",s).get(i+(n.href||""));if(h){for(var b=0;b<h.length;b++)if(o=h[b],o.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&o.getAttribute("rel")===(n.rel==null?null:n.rel)&&o.getAttribute("title")===(n.title==null?null:n.title)&&o.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){h.splice(b,1);break e}}o=s.createElement(i),$t(o,i,n),s.head.appendChild(o);break;case"meta":if(h=xm("meta","content",s).get(i+(n.content||""))){for(b=0;b<h.length;b++)if(o=h[b],o.getAttribute("content")===(n.content==null?null:""+n.content)&&o.getAttribute("name")===(n.name==null?null:n.name)&&o.getAttribute("property")===(n.property==null?null:n.property)&&o.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&o.getAttribute("charset")===(n.charSet==null?null:n.charSet)){h.splice(b,1);break e}}o=s.createElement(i),$t(o,i,n),s.head.appendChild(o);break;default:throw Error(r(468,i))}o[It]=t,Qt(o),i=o}t.stateNode=i}else Sm(s,t.type,t.stateNode);else t.stateNode=bm(s,i,t.memoizedProps);else o!==i?(o===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):o.count--,i===null?Sm(s,t.type,t.stateNode):bm(s,i,t.memoizedProps)):i===null&&t.stateNode!==null&&Dr(t,t.memoizedProps,n.memoizedProps)}break;case 27:ge(e,t),ye(t),i&512&&(Bt||n===null||Ge(n,n.return)),n!==null&&i&4&&Dr(t,t.memoizedProps,n.memoizedProps);break;case 5:if(ge(e,t),ye(t),i&512&&(Bt||n===null||Ge(n,n.return)),t.flags&32){s=t.stateNode;try{xa(s,"")}catch(z){jt(t,t.return,z)}}i&4&&t.stateNode!=null&&(s=t.memoizedProps,Dr(t,s,n!==null?n.memoizedProps:s)),i&1024&&(Rr=!0);break;case 6:if(ge(e,t),ye(t),i&4){if(t.stateNode===null)throw Error(r(162));i=t.memoizedProps,n=t.stateNode;try{n.nodeValue=i}catch(z){jt(t,t.return,z)}}break;case 3:if(Os=null,s=Be,Be=ws(e.containerInfo),ge(e,t),Be=s,ye(t),i&4&&n!==null&&n.memoizedState.isDehydrated)try{sl(e.containerInfo)}catch(z){jt(t,t.return,z)}Rr&&(Rr=!1,Dh(t));break;case 4:i=Be,Be=ws(t.stateNode.containerInfo),ge(e,t),ye(t),Be=i;break;case 12:ge(e,t),ye(t);break;case 13:ge(e,t),ye(t),t.child.flags&8192&&t.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(Ur=He()),i&4&&(i=t.updateQueue,i!==null&&(t.updateQueue=null,Or(t,i)));break;case 22:s=t.memoizedState!==null;var T=n!==null&&n.memoizedState!==null,R=un,L=Bt;if(un=R||s,Bt=L||T,ge(e,t),Bt=L,un=R,ye(t),i&8192)t:for(e=t.stateNode,e._visibility=s?e._visibility&-2:e._visibility|1,s&&(n===null||T||un||Bt||ia(t)),n=null,e=t;;){if(e.tag===5||e.tag===26){if(n===null){T=n=e;try{if(o=T.stateNode,s)h=o.style,typeof h.setProperty=="function"?h.setProperty("display","none","important"):h.display="none";else{b=T.stateNode;var q=T.memoizedProps.style,N=q!=null&&q.hasOwnProperty("display")?q.display:null;b.style.display=N==null||typeof N=="boolean"?"":(""+N).trim()}}catch(z){jt(T,T.return,z)}}}else if(e.tag===6){if(n===null){T=e;try{T.stateNode.nodeValue=s?"":T.memoizedProps}catch(z){jt(T,T.return,z)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;n===e&&(n=null),e=e.return}n===e&&(n=null),e.sibling.return=e.return,e=e.sibling}i&4&&(i=t.updateQueue,i!==null&&(n=i.retryQueue,n!==null&&(i.retryQueue=null,Or(t,n))));break;case 19:ge(e,t),ye(t),i&4&&(i=t.updateQueue,i!==null&&(t.updateQueue=null,Or(t,i)));break;case 30:break;case 21:break;default:ge(e,t),ye(t)}}function ye(t){var e=t.flags;if(e&2){try{for(var n,i=t.return;i!==null;){if(bh(i)){n=i;break}i=i.return}if(n==null)throw Error(r(160));switch(n.tag){case 27:var s=n.stateNode,o=Cr(t);gs(t,o,s);break;case 5:var h=n.stateNode;n.flags&32&&(xa(h,""),n.flags&=-33);var b=Cr(t);gs(t,b,h);break;case 3:case 4:var T=n.stateNode.containerInfo,R=Cr(t);wr(t,R,T);break;default:throw Error(r(161))}}catch(L){jt(t,t.return,L)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Dh(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Dh(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function En(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Th(t,e.alternate,e),e=e.sibling}function ia(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:An(4,e,e.return),ia(e);break;case 1:Ge(e,e.return);var n=e.stateNode;typeof n.componentWillUnmount=="function"&&yh(e,e.return,n),ia(e);break;case 27:Wi(e.stateNode);case 26:case 5:Ge(e,e.return),ia(e);break;case 22:e.memoizedState===null&&ia(e);break;case 30:ia(e);break;default:ia(e)}t=t.sibling}}function Mn(t,e,n){for(n=n&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var i=e.alternate,s=t,o=e,h=o.flags;switch(o.tag){case 0:case 11:case 15:Mn(s,o,n),Gi(4,o);break;case 1:if(Mn(s,o,n),i=o,s=i.stateNode,typeof s.componentDidMount=="function")try{s.componentDidMount()}catch(R){jt(i,i.return,R)}if(i=o,s=i.updateQueue,s!==null){var b=i.stateNode;try{var T=s.shared.hiddenCallbacks;if(T!==null)for(s.shared.hiddenCallbacks=null,s=0;s<T.length;s++)nd(T[s],b)}catch(R){jt(i,i.return,R)}}n&&h&64&&gh(o),Xi(o,o.return);break;case 27:xh(o);case 26:case 5:Mn(s,o,n),n&&i===null&&h&4&&vh(o),Xi(o,o.return);break;case 12:Mn(s,o,n);break;case 13:Mn(s,o,n),n&&h&4&&Mh(s,o);break;case 22:o.memoizedState===null&&Mn(s,o,n),Xi(o,o.return);break;case 30:break;default:Mn(s,o,n)}e=e.sibling}}function Nr(t,e){var n=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==n&&(t!=null&&t.refCount++,n!=null&&Ci(n))}function zr(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Ci(t))}function Xe(t,e,n,i){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Ch(t,e,n,i),e=e.sibling}function Ch(t,e,n,i){var s=e.flags;switch(e.tag){case 0:case 11:case 15:Xe(t,e,n,i),s&2048&&Gi(9,e);break;case 1:Xe(t,e,n,i);break;case 3:Xe(t,e,n,i),s&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Ci(t)));break;case 12:if(s&2048){Xe(t,e,n,i),t=e.stateNode;try{var o=e.memoizedProps,h=o.id,b=o.onPostCommit;typeof b=="function"&&b(h,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(T){jt(e,e.return,T)}}else Xe(t,e,n,i);break;case 13:Xe(t,e,n,i);break;case 23:break;case 22:o=e.stateNode,h=e.alternate,e.memoizedState!==null?o._visibility&2?Xe(t,e,n,i):Zi(t,e):o._visibility&2?Xe(t,e,n,i):(o._visibility|=2,Ha(t,e,n,i,(e.subtreeFlags&10256)!==0)),s&2048&&Nr(h,e);break;case 24:Xe(t,e,n,i),s&2048&&zr(e.alternate,e);break;default:Xe(t,e,n,i)}}function Ha(t,e,n,i,s){for(s=s&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var o=t,h=e,b=n,T=i,R=h.flags;switch(h.tag){case 0:case 11:case 15:Ha(o,h,b,T,s),Gi(8,h);break;case 23:break;case 22:var L=h.stateNode;h.memoizedState!==null?L._visibility&2?Ha(o,h,b,T,s):Zi(o,h):(L._visibility|=2,Ha(o,h,b,T,s)),s&&R&2048&&Nr(h.alternate,h);break;case 24:Ha(o,h,b,T,s),s&&R&2048&&zr(h.alternate,h);break;default:Ha(o,h,b,T,s)}e=e.sibling}}function Zi(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var n=t,i=e,s=i.flags;switch(i.tag){case 22:Zi(n,i),s&2048&&Nr(i.alternate,i);break;case 24:Zi(n,i),s&2048&&zr(i.alternate,i);break;default:Zi(n,i)}e=e.sibling}}var Qi=8192;function qa(t){if(t.subtreeFlags&Qi)for(t=t.child;t!==null;)wh(t),t=t.sibling}function wh(t){switch(t.tag){case 26:qa(t),t.flags&Qi&&t.memoizedState!==null&&$v(Be,t.memoizedState,t.memoizedProps);break;case 5:qa(t);break;case 3:case 4:var e=Be;Be=ws(t.stateNode.containerInfo),qa(t),Be=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=Qi,Qi=16777216,qa(t),Qi=e):qa(t));break;default:qa(t)}}function Rh(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function Ki(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var i=e[n];kt=i,Nh(i,t)}Rh(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Oh(t),t=t.sibling}function Oh(t){switch(t.tag){case 0:case 11:case 15:Ki(t),t.flags&2048&&An(9,t,t.return);break;case 3:Ki(t);break;case 12:Ki(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,ys(t)):Ki(t);break;default:Ki(t)}}function ys(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var i=e[n];kt=i,Nh(i,t)}Rh(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:An(8,e,e.return),ys(e);break;case 22:n=e.stateNode,n._visibility&2&&(n._visibility&=-3,ys(e));break;default:ys(e)}t=t.sibling}}function Nh(t,e){for(;kt!==null;){var n=kt;switch(n.tag){case 0:case 11:case 15:An(8,n,e);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var i=n.memoizedState.cachePool.pool;i!=null&&i.refCount++}break;case 24:Ci(n.memoizedState.cache)}if(i=n.child,i!==null)i.return=n,kt=i;else t:for(n=t;kt!==null;){i=kt;var s=i.sibling,o=i.return;if(Ah(i),i===n){kt=null;break t}if(s!==null){s.return=o,kt=s;break t}kt=o}}}var hv={getCacheForType:function(t){var e=te(Gt),n=e.data.get(t);return n===void 0&&(n=t(),e.data.set(t,n)),n}},mv=typeof WeakMap=="function"?WeakMap:Map,bt=0,Dt=null,ht=null,gt=0,xt=0,ve=null,jn=!1,Ya=!1,Vr=!1,on=0,zt=0,Dn=0,la=0,_r=0,we=0,Ga=0,ki=null,oe=null,Br=!1,Ur=0,vs=1/0,bs=null,Cn=null,Jt=0,wn=null,Xa=null,Za=0,Lr=0,Hr=null,zh=null,Fi=0,qr=null;function be(){if((bt&2)!==0&&gt!==0)return gt&-gt;if(U.T!==null){var t=Oa;return t!==0?t:kr()}return Fc()}function Vh(){we===0&&(we=(gt&536870912)===0||vt?Zc():536870912);var t=Ce.current;return t!==null&&(t.flags|=32),we}function xe(t,e,n){(t===Dt&&(xt===2||xt===9)||t.cancelPendingCommit!==null)&&(Qa(t,0),Rn(t,gt,we,!1)),di(t,n),((bt&2)===0||t!==Dt)&&(t===Dt&&((bt&2)===0&&(la|=n),zt===4&&Rn(t,gt,we,!1)),Ze(t))}function _h(t,e,n){if((bt&6)!==0)throw Error(r(327));var i=!n&&(e&124)===0&&(e&t.expiredLanes)===0||fi(t,e),s=i?yv(t,e):Xr(t,e,!0),o=i;do{if(s===0){Ya&&!i&&Rn(t,e,0,!1);break}else{if(n=t.current.alternate,o&&!pv(n)){s=Xr(t,e,!1),o=!1;continue}if(s===2){if(o=e,t.errorRecoveryDisabledLanes&o)var h=0;else h=t.pendingLanes&-536870913,h=h!==0?h:h&536870912?536870912:0;if(h!==0){e=h;t:{var b=t;s=ki;var T=b.current.memoizedState.isDehydrated;if(T&&(Qa(b,h).flags|=256),h=Xr(b,h,!1),h!==2){if(Vr&&!T){b.errorRecoveryDisabledLanes|=o,la|=o,s=4;break t}o=oe,oe=s,o!==null&&(oe===null?oe=o:oe.push.apply(oe,o))}s=h}if(o=!1,s!==2)continue}}if(s===1){Qa(t,0),Rn(t,e,0,!0);break}t:{switch(i=t,o=s,o){case 0:case 1:throw Error(r(345));case 4:if((e&4194048)!==e)break;case 6:Rn(i,e,we,!jn);break t;case 2:oe=null;break;case 3:case 5:break;default:throw Error(r(329))}if((e&62914560)===e&&(s=Ur+300-He(),10<s)){if(Rn(i,e,we,!jn),Rl(i,0,!0)!==0)break t;i.timeoutHandle=cm(Bh.bind(null,i,n,oe,bs,Br,e,we,la,Ga,jn,o,2,-0,0),s);break t}Bh(i,n,oe,bs,Br,e,we,la,Ga,jn,o,0,-0,0)}}break}while(!0);Ze(t)}function Bh(t,e,n,i,s,o,h,b,T,R,L,q,N,z){if(t.timeoutHandle=-1,q=e.subtreeFlags,(q&8192||(q&16785408)===16785408)&&(el={stylesheets:null,count:0,unsuspend:Jv},wh(e),q=Wv(),q!==null)){t.cancelPendingCommit=q(Xh.bind(null,t,e,o,n,i,s,h,b,T,L,1,N,z)),Rn(t,o,h,!R);return}Xh(t,e,o,n,i,s,h,b,T)}function pv(t){for(var e=t;;){var n=e.tag;if((n===0||n===11||n===15)&&e.flags&16384&&(n=e.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var i=0;i<n.length;i++){var s=n[i],o=s.getSnapshot;s=s.value;try{if(!me(o(),s))return!1}catch{return!1}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Rn(t,e,n,i){e&=~_r,e&=~la,t.suspendedLanes|=e,t.pingedLanes&=~e,i&&(t.warmLanes|=e),i=t.expirationTimes;for(var s=e;0<s;){var o=31-he(s),h=1<<o;i[o]=-1,s&=~h}n!==0&&Kc(t,n,e)}function xs(){return(bt&6)===0?(Pi(0),!1):!0}function Yr(){if(ht!==null){if(xt===0)var t=ht.return;else t=ht,tn=In=null,ir(t),Ua=null,Hi=0,t=ht;for(;t!==null;)ph(t.alternate,t),t=t.return;ht=null}}function Qa(t,e){var n=t.timeoutHandle;n!==-1&&(t.timeoutHandle=-1,zv(n)),n=t.cancelPendingCommit,n!==null&&(t.cancelPendingCommit=null,n()),Yr(),Dt=t,ht=n=$e(t.current,null),gt=e,xt=0,ve=null,jn=!1,Ya=fi(t,e),Vr=!1,Ga=we=_r=la=Dn=zt=0,oe=ki=null,Br=!1,(e&8)!==0&&(e|=e&32);var i=t.entangledLanes;if(i!==0)for(t=t.entanglements,i&=e;0<i;){var s=31-he(i),o=1<<s;e|=t[s],i&=~o}return on=e,Gl(),n}function Uh(t,e){ct=null,U.H=us,e===Ri||e===$l?(e=td(),xt=3):e===$f?(e=td(),xt=4):xt=e===eh?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,ve=e,ht===null&&(zt=1,ds(t,Ee(e,t.current)))}function Lh(){var t=U.H;return U.H=us,t===null?us:t}function Hh(){var t=U.A;return U.A=hv,t}function Gr(){zt=4,jn||(gt&4194048)!==gt&&Ce.current!==null||(Ya=!0),(Dn&134217727)===0&&(la&134217727)===0||Dt===null||Rn(Dt,gt,we,!1)}function Xr(t,e,n){var i=bt;bt|=2;var s=Lh(),o=Hh();(Dt!==t||gt!==e)&&(bs=null,Qa(t,e)),e=!1;var h=zt;t:do try{if(xt!==0&&ht!==null){var b=ht,T=ve;switch(xt){case 8:Yr(),h=6;break t;case 3:case 2:case 9:case 6:Ce.current===null&&(e=!0);var R=xt;if(xt=0,ve=null,Ka(t,b,T,R),n&&Ya){h=0;break t}break;default:R=xt,xt=0,ve=null,Ka(t,b,T,R)}}gv(),h=zt;break}catch(L){Uh(t,L)}while(!0);return e&&t.shellSuspendCounter++,tn=In=null,bt=i,U.H=s,U.A=o,ht===null&&(Dt=null,gt=0,Gl()),h}function gv(){for(;ht!==null;)qh(ht)}function yv(t,e){var n=bt;bt|=2;var i=Lh(),s=Hh();Dt!==t||gt!==e?(bs=null,vs=He()+500,Qa(t,e)):Ya=fi(t,e);t:do try{if(xt!==0&&ht!==null){e=ht;var o=ve;e:switch(xt){case 1:xt=0,ve=null,Ka(t,e,o,1);break;case 2:case 9:if(Wf(o)){xt=0,ve=null,Yh(e);break}e=function(){xt!==2&&xt!==9||Dt!==t||(xt=7),Ze(t)},o.then(e,e);break t;case 3:xt=7;break t;case 4:xt=5;break t;case 7:Wf(o)?(xt=0,ve=null,Yh(e)):(xt=0,ve=null,Ka(t,e,o,7));break;case 5:var h=null;switch(ht.tag){case 26:h=ht.memoizedState;case 5:case 27:var b=ht;if(!h||Tm(h)){xt=0,ve=null;var T=b.sibling;if(T!==null)ht=T;else{var R=b.return;R!==null?(ht=R,Ss(R)):ht=null}break e}}xt=0,ve=null,Ka(t,e,o,5);break;case 6:xt=0,ve=null,Ka(t,e,o,6);break;case 8:Yr(),zt=6;break t;default:throw Error(r(462))}}vv();break}catch(L){Uh(t,L)}while(!0);return tn=In=null,U.H=i,U.A=s,bt=n,ht!==null?0:(Dt=null,gt=0,Gl(),zt)}function vv(){for(;ht!==null&&!q0();)qh(ht)}function qh(t){var e=hh(t.alternate,t,on);t.memoizedProps=t.pendingProps,e===null?Ss(t):ht=e}function Yh(t){var e=t,n=e.alternate;switch(e.tag){case 15:case 0:e=uh(n,e,e.pendingProps,e.type,void 0,gt);break;case 11:e=uh(n,e,e.pendingProps,e.type.render,e.ref,gt);break;case 5:ir(e);default:ph(n,e),e=ht=Gf(e,on),e=hh(n,e,on)}t.memoizedProps=t.pendingProps,e===null?Ss(t):ht=e}function Ka(t,e,n,i){tn=In=null,ir(e),Ua=null,Hi=0;var s=e.return;try{if(uv(t,s,e,n,gt)){zt=1,ds(t,Ee(n,t.current)),ht=null;return}}catch(o){if(s!==null)throw ht=s,o;zt=1,ds(t,Ee(n,t.current)),ht=null;return}e.flags&32768?(vt||i===1?t=!0:Ya||(gt&536870912)!==0?t=!1:(jn=t=!0,(i===2||i===9||i===3||i===6)&&(i=Ce.current,i!==null&&i.tag===13&&(i.flags|=16384))),Gh(e,t)):Ss(e)}function Ss(t){var e=t;do{if((e.flags&32768)!==0){Gh(e,jn);return}t=e.return;var n=ov(e.alternate,e,on);if(n!==null){ht=n;return}if(e=e.sibling,e!==null){ht=e;return}ht=e=t}while(e!==null);zt===0&&(zt=5)}function Gh(t,e){do{var n=cv(t.alternate,t);if(n!==null){n.flags&=32767,ht=n;return}if(n=t.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!e&&(t=t.sibling,t!==null)){ht=t;return}ht=t=n}while(t!==null);zt=6,ht=null}function Xh(t,e,n,i,s,o,h,b,T){t.cancelPendingCommit=null;do Ts();while(Jt!==0);if((bt&6)!==0)throw Error(r(327));if(e!==null){if(e===t.current)throw Error(r(177));if(o=e.lanes|e.childLanes,o|=zu,J0(t,n,o,h,b,T),t===Dt&&(ht=Dt=null,gt=0),Xa=e,wn=t,Za=n,Lr=o,Hr=s,zh=i,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,Tv(Dl,function(){return Fh(),null})):(t.callbackNode=null,t.callbackPriority=0),i=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||i){i=U.T,U.T=null,s=k.p,k.p=2,h=bt,bt|=4;try{fv(t,e,n)}finally{bt=h,k.p=s,U.T=i}}Jt=1,Zh(),Qh(),Kh()}}function Zh(){if(Jt===1){Jt=0;var t=wn,e=Xa,n=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||n){n=U.T,U.T=null;var i=k.p;k.p=2;var s=bt;bt|=4;try{jh(e,t);var o=eo,h=Nf(t.containerInfo),b=o.focusedElem,T=o.selectionRange;if(h!==b&&b&&b.ownerDocument&&Of(b.ownerDocument.documentElement,b)){if(T!==null&&Cu(b)){var R=T.start,L=T.end;if(L===void 0&&(L=R),"selectionStart"in b)b.selectionStart=R,b.selectionEnd=Math.min(L,b.value.length);else{var q=b.ownerDocument||document,N=q&&q.defaultView||window;if(N.getSelection){var z=N.getSelection(),lt=b.textContent.length,nt=Math.min(T.start,lt),Mt=T.end===void 0?nt:Math.min(T.end,lt);!z.extend&&nt>Mt&&(h=Mt,Mt=nt,nt=h);var D=Rf(b,nt),E=Rf(b,Mt);if(D&&E&&(z.rangeCount!==1||z.anchorNode!==D.node||z.anchorOffset!==D.offset||z.focusNode!==E.node||z.focusOffset!==E.offset)){var w=q.createRange();w.setStart(D.node,D.offset),z.removeAllRanges(),nt>Mt?(z.addRange(w),z.extend(E.node,E.offset)):(w.setEnd(E.node,E.offset),z.addRange(w))}}}}for(q=[],z=b;z=z.parentNode;)z.nodeType===1&&q.push({element:z,left:z.scrollLeft,top:z.scrollTop});for(typeof b.focus=="function"&&b.focus(),b=0;b<q.length;b++){var H=q[b];H.element.scrollLeft=H.left,H.element.scrollTop=H.top}}Vs=!!to,eo=to=null}finally{bt=s,k.p=i,U.T=n}}t.current=e,Jt=2}}function Qh(){if(Jt===2){Jt=0;var t=wn,e=Xa,n=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||n){n=U.T,U.T=null;var i=k.p;k.p=2;var s=bt;bt|=4;try{Th(t,e.alternate,e)}finally{bt=s,k.p=i,U.T=n}}Jt=3}}function Kh(){if(Jt===4||Jt===3){Jt=0,Y0();var t=wn,e=Xa,n=Za,i=zh;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?Jt=5:(Jt=0,Xa=wn=null,kh(t,t.pendingLanes));var s=t.pendingLanes;if(s===0&&(Cn=null),uu(n),e=e.stateNode,de&&typeof de.onCommitFiberRoot=="function")try{de.onCommitFiberRoot(ci,e,void 0,(e.current.flags&128)===128)}catch{}if(i!==null){e=U.T,s=k.p,k.p=2,U.T=null;try{for(var o=t.onRecoverableError,h=0;h<i.length;h++){var b=i[h];o(b.value,{componentStack:b.stack})}}finally{U.T=e,k.p=s}}(Za&3)!==0&&Ts(),Ze(t),s=t.pendingLanes,(n&4194090)!==0&&(s&42)!==0?t===qr?Fi++:(Fi=0,qr=t):Fi=0,Pi(0)}}function kh(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,Ci(e)))}function Ts(t){return Zh(),Qh(),Kh(),Fh()}function Fh(){if(Jt!==5)return!1;var t=wn,e=Lr;Lr=0;var n=uu(Za),i=U.T,s=k.p;try{k.p=32>n?32:n,U.T=null,n=Hr,Hr=null;var o=wn,h=Za;if(Jt=0,Xa=wn=null,Za=0,(bt&6)!==0)throw Error(r(331));var b=bt;if(bt|=4,Oh(o.current),Ch(o,o.current,h,n),bt=b,Pi(0,!1),de&&typeof de.onPostCommitFiberRoot=="function")try{de.onPostCommitFiberRoot(ci,o)}catch{}return!0}finally{k.p=s,U.T=i,kh(t,e)}}function Ph(t,e,n){e=Ee(n,e),e=vr(t.stateNode,e,2),t=bn(t,e,2),t!==null&&(di(t,2),Ze(t))}function jt(t,e,n){if(t.tag===3)Ph(t,t,n);else for(;e!==null;){if(e.tag===3){Ph(e,t,n);break}else if(e.tag===1){var i=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof i.componentDidCatch=="function"&&(Cn===null||!Cn.has(i))){t=Ee(n,t),n=Id(2),i=bn(e,n,2),i!==null&&(th(n,i,e,t),di(i,2),Ze(i));break}}e=e.return}}function Zr(t,e,n){var i=t.pingCache;if(i===null){i=t.pingCache=new mv;var s=new Set;i.set(e,s)}else s=i.get(e),s===void 0&&(s=new Set,i.set(e,s));s.has(n)||(Vr=!0,s.add(n),t=bv.bind(null,t,e,n),e.then(t,t))}function bv(t,e,n){var i=t.pingCache;i!==null&&i.delete(e),t.pingedLanes|=t.suspendedLanes&n,t.warmLanes&=~n,Dt===t&&(gt&n)===n&&(zt===4||zt===3&&(gt&62914560)===gt&&300>He()-Ur?(bt&2)===0&&Qa(t,0):_r|=n,Ga===gt&&(Ga=0)),Ze(t)}function Jh(t,e){e===0&&(e=Qc()),t=Da(t,e),t!==null&&(di(t,e),Ze(t))}function xv(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),Jh(t,n)}function Sv(t,e){var n=0;switch(t.tag){case 13:var i=t.stateNode,s=t.memoizedState;s!==null&&(n=s.retryLane);break;case 19:i=t.stateNode;break;case 22:i=t.stateNode._retryCache;break;default:throw Error(r(314))}i!==null&&i.delete(e),Jh(t,n)}function Tv(t,e){return Gn(t,e)}var As=null,ka=null,Qr=!1,Es=!1,Kr=!1,sa=0;function Ze(t){t!==ka&&t.next===null&&(ka===null?As=ka=t:ka=ka.next=t),Es=!0,Qr||(Qr=!0,Ev())}function Pi(t,e){if(!Kr&&Es){Kr=!0;do for(var n=!1,i=As;i!==null;){if(t!==0){var s=i.pendingLanes;if(s===0)var o=0;else{var h=i.suspendedLanes,b=i.pingedLanes;o=(1<<31-he(42|t)+1)-1,o&=s&~(h&~b),o=o&201326741?o&201326741|1:o?o|2:0}o!==0&&(n=!0,tm(i,o))}else o=gt,o=Rl(i,i===Dt?o:0,i.cancelPendingCommit!==null||i.timeoutHandle!==-1),(o&3)===0||fi(i,o)||(n=!0,tm(i,o));i=i.next}while(n);Kr=!1}}function Av(){$h()}function $h(){Es=Qr=!1;var t=0;sa!==0&&(Nv()&&(t=sa),sa=0);for(var e=He(),n=null,i=As;i!==null;){var s=i.next,o=Wh(i,e);o===0?(i.next=null,n===null?As=s:n.next=s,s===null&&(ka=n)):(n=i,(t!==0||(o&3)!==0)&&(Es=!0)),i=s}Pi(t)}function Wh(t,e){for(var n=t.suspendedLanes,i=t.pingedLanes,s=t.expirationTimes,o=t.pendingLanes&-62914561;0<o;){var h=31-he(o),b=1<<h,T=s[h];T===-1?((b&n)===0||(b&i)!==0)&&(s[h]=P0(b,e)):T<=e&&(t.expiredLanes|=b),o&=~b}if(e=Dt,n=gt,n=Rl(t,t===e?n:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),i=t.callbackNode,n===0||t===e&&(xt===2||xt===9)||t.cancelPendingCommit!==null)return i!==null&&i!==null&&iu(i),t.callbackNode=null,t.callbackPriority=0;if((n&3)===0||fi(t,n)){if(e=n&-n,e===t.callbackPriority)return e;switch(i!==null&&iu(i),uu(n)){case 2:case 8:n=Gc;break;case 32:n=Dl;break;case 268435456:n=Xc;break;default:n=Dl}return i=Ih.bind(null,t),n=Gn(n,i),t.callbackPriority=e,t.callbackNode=n,e}return i!==null&&i!==null&&iu(i),t.callbackPriority=2,t.callbackNode=null,2}function Ih(t,e){if(Jt!==0&&Jt!==5)return t.callbackNode=null,t.callbackPriority=0,null;var n=t.callbackNode;if(Ts()&&t.callbackNode!==n)return null;var i=gt;return i=Rl(t,t===Dt?i:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),i===0?null:(_h(t,i,e),Wh(t,He()),t.callbackNode!=null&&t.callbackNode===n?Ih.bind(null,t):null)}function tm(t,e){if(Ts())return null;_h(t,e,!0)}function Ev(){Vv(function(){(bt&6)!==0?Gn(Yc,Av):$h()})}function kr(){return sa===0&&(sa=Zc()),sa}function em(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:_l(""+t)}function nm(t,e){var n=e.ownerDocument.createElement("input");return n.name=e.name,n.value=e.value,t.id&&n.setAttribute("form",t.id),e.parentNode.insertBefore(n,e),t=new FormData(t),n.parentNode.removeChild(n),t}function Mv(t,e,n,i,s){if(e==="submit"&&n&&n.stateNode===s){var o=em((s[le]||null).action),h=i.submitter;h&&(e=(e=h[le]||null)?em(e.formAction):h.getAttribute("formAction"),e!==null&&(o=e,h=null));var b=new Hl("action","action",null,i,s);t.push({event:b,listeners:[{instance:null,listener:function(){if(i.defaultPrevented){if(sa!==0){var T=h?nm(s,h):new FormData(s);hr(n,{pending:!0,data:T,method:s.method,action:o},null,T)}}else typeof o=="function"&&(b.preventDefault(),T=h?nm(s,h):new FormData(s),hr(n,{pending:!0,data:T,method:s.method,action:o},o,T))},currentTarget:s}]})}}for(var Fr=0;Fr<Nu.length;Fr++){var Pr=Nu[Fr],jv=Pr.toLowerCase(),Dv=Pr[0].toUpperCase()+Pr.slice(1);_e(jv,"on"+Dv)}_e(_f,"onAnimationEnd"),_e(Bf,"onAnimationIteration"),_e(Uf,"onAnimationStart"),_e("dblclick","onDoubleClick"),_e("focusin","onFocus"),_e("focusout","onBlur"),_e(Zy,"onTransitionRun"),_e(Qy,"onTransitionStart"),_e(Ky,"onTransitionCancel"),_e(Lf,"onTransitionEnd"),ya("onMouseEnter",["mouseout","mouseover"]),ya("onMouseLeave",["mouseout","mouseover"]),ya("onPointerEnter",["pointerout","pointerover"]),ya("onPointerLeave",["pointerout","pointerover"]),Zn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Zn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Zn("onBeforeInput",["compositionend","keypress","textInput","paste"]),Zn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Zn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Zn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ji="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Cv=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Ji));function am(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var i=t[n],s=i.event;i=i.listeners;t:{var o=void 0;if(e)for(var h=i.length-1;0<=h;h--){var b=i[h],T=b.instance,R=b.currentTarget;if(b=b.listener,T!==o&&s.isPropagationStopped())break t;o=b,s.currentTarget=R;try{o(s)}catch(L){fs(L)}s.currentTarget=null,o=T}else for(h=0;h<i.length;h++){if(b=i[h],T=b.instance,R=b.currentTarget,b=b.listener,T!==o&&s.isPropagationStopped())break t;o=b,s.currentTarget=R;try{o(s)}catch(L){fs(L)}s.currentTarget=null,o=T}}}}function mt(t,e){var n=e[ru];n===void 0&&(n=e[ru]=new Set);var i=t+"__bubble";n.has(i)||(im(e,t,2,!1),n.add(i))}function Jr(t,e,n){var i=0;e&&(i|=4),im(n,t,i,e)}var Ms="_reactListening"+Math.random().toString(36).slice(2);function $r(t){if(!t[Ms]){t[Ms]=!0,Jc.forEach(function(n){n!=="selectionchange"&&(Cv.has(n)||Jr(n,!1,t),Jr(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Ms]||(e[Ms]=!0,Jr("selectionchange",!1,e))}}function im(t,e,n,i){switch(Cm(e)){case 2:var s=eb;break;case 8:s=nb;break;default:s=fo}n=s.bind(null,e,n,t),s=void 0,!bu||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(s=!0),i?s!==void 0?t.addEventListener(e,n,{capture:!0,passive:s}):t.addEventListener(e,n,!0):s!==void 0?t.addEventListener(e,n,{passive:s}):t.addEventListener(e,n,!1)}function Wr(t,e,n,i,s){var o=i;if((e&1)===0&&(e&2)===0&&i!==null)t:for(;;){if(i===null)return;var h=i.tag;if(h===3||h===4){var b=i.stateNode.containerInfo;if(b===s)break;if(h===4)for(h=i.return;h!==null;){var T=h.tag;if((T===3||T===4)&&h.stateNode.containerInfo===s)return;h=h.return}for(;b!==null;){if(h=ma(b),h===null)return;if(T=h.tag,T===5||T===6||T===26||T===27){i=o=h;continue t}b=b.parentNode}}i=i.return}ff(function(){var R=o,L=yu(n),q=[];t:{var N=Hf.get(t);if(N!==void 0){var z=Hl,lt=t;switch(t){case"keypress":if(Ul(n)===0)break t;case"keydown":case"keyup":z=Ty;break;case"focusin":lt="focus",z=Au;break;case"focusout":lt="blur",z=Au;break;case"beforeblur":case"afterblur":z=Au;break;case"click":if(n.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":z=mf;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":z=cy;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":z=My;break;case _f:case Bf:case Uf:z=hy;break;case Lf:z=Dy;break;case"scroll":case"scrollend":z=ry;break;case"wheel":z=wy;break;case"copy":case"cut":case"paste":z=py;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":z=gf;break;case"toggle":case"beforetoggle":z=Oy}var nt=(e&4)!==0,Mt=!nt&&(t==="scroll"||t==="scrollend"),D=nt?N!==null?N+"Capture":null:N;nt=[];for(var E=R,w;E!==null;){var H=E;if(w=H.stateNode,H=H.tag,H!==5&&H!==26&&H!==27||w===null||D===null||(H=pi(E,D),H!=null&&nt.push($i(E,H,w))),Mt)break;E=E.return}0<nt.length&&(N=new z(N,lt,null,n,L),q.push({event:N,listeners:nt}))}}if((e&7)===0){t:{if(N=t==="mouseover"||t==="pointerover",z=t==="mouseout"||t==="pointerout",N&&n!==gu&&(lt=n.relatedTarget||n.fromElement)&&(ma(lt)||lt[ha]))break t;if((z||N)&&(N=L.window===L?L:(N=L.ownerDocument)?N.defaultView||N.parentWindow:window,z?(lt=n.relatedTarget||n.toElement,z=R,lt=lt?ma(lt):null,lt!==null&&(Mt=d(lt),nt=lt.tag,lt!==Mt||nt!==5&&nt!==27&&nt!==6)&&(lt=null)):(z=null,lt=R),z!==lt)){if(nt=mf,H="onMouseLeave",D="onMouseEnter",E="mouse",(t==="pointerout"||t==="pointerover")&&(nt=gf,H="onPointerLeave",D="onPointerEnter",E="pointer"),Mt=z==null?N:mi(z),w=lt==null?N:mi(lt),N=new nt(H,E+"leave",z,n,L),N.target=Mt,N.relatedTarget=w,H=null,ma(L)===R&&(nt=new nt(D,E+"enter",lt,n,L),nt.target=w,nt.relatedTarget=Mt,H=nt),Mt=H,z&&lt)e:{for(nt=z,D=lt,E=0,w=nt;w;w=Fa(w))E++;for(w=0,H=D;H;H=Fa(H))w++;for(;0<E-w;)nt=Fa(nt),E--;for(;0<w-E;)D=Fa(D),w--;for(;E--;){if(nt===D||D!==null&&nt===D.alternate)break e;nt=Fa(nt),D=Fa(D)}nt=null}else nt=null;z!==null&&lm(q,N,z,nt,!1),lt!==null&&Mt!==null&&lm(q,Mt,lt,nt,!0)}}t:{if(N=R?mi(R):window,z=N.nodeName&&N.nodeName.toLowerCase(),z==="select"||z==="input"&&N.type==="file")var I=Ef;else if(Tf(N))if(Mf)I=Yy;else{I=Hy;var dt=Ly}else z=N.nodeName,!z||z.toLowerCase()!=="input"||N.type!=="checkbox"&&N.type!=="radio"?R&&pu(R.elementType)&&(I=Ef):I=qy;if(I&&(I=I(t,R))){Af(q,I,n,L);break t}dt&&dt(t,N,R),t==="focusout"&&R&&N.type==="number"&&R.memoizedProps.value!=null&&mu(N,"number",N.value)}switch(dt=R?mi(R):window,t){case"focusin":(Tf(dt)||dt.contentEditable==="true")&&(Ea=dt,wu=R,Ai=null);break;case"focusout":Ai=wu=Ea=null;break;case"mousedown":Ru=!0;break;case"contextmenu":case"mouseup":case"dragend":Ru=!1,zf(q,n,L);break;case"selectionchange":if(Xy)break;case"keydown":case"keyup":zf(q,n,L)}var et;if(Mu)t:{switch(t){case"compositionstart":var at="onCompositionStart";break t;case"compositionend":at="onCompositionEnd";break t;case"compositionupdate":at="onCompositionUpdate";break t}at=void 0}else Aa?xf(t,n)&&(at="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(at="onCompositionStart");at&&(yf&&n.locale!=="ko"&&(Aa||at!=="onCompositionStart"?at==="onCompositionEnd"&&Aa&&(et=df()):(pn=L,xu="value"in pn?pn.value:pn.textContent,Aa=!0)),dt=js(R,at),0<dt.length&&(at=new pf(at,t,null,n,L),q.push({event:at,listeners:dt}),et?at.data=et:(et=Sf(n),et!==null&&(at.data=et)))),(et=zy?Vy(t,n):_y(t,n))&&(at=js(R,"onBeforeInput"),0<at.length&&(dt=new pf("onBeforeInput","beforeinput",null,n,L),q.push({event:dt,listeners:at}),dt.data=et)),Mv(q,t,R,n,L)}am(q,e)})}function $i(t,e,n){return{instance:t,listener:e,currentTarget:n}}function js(t,e){for(var n=e+"Capture",i=[];t!==null;){var s=t,o=s.stateNode;if(s=s.tag,s!==5&&s!==26&&s!==27||o===null||(s=pi(t,n),s!=null&&i.unshift($i(t,s,o)),s=pi(t,e),s!=null&&i.push($i(t,s,o))),t.tag===3)return i;t=t.return}return[]}function Fa(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function lm(t,e,n,i,s){for(var o=e._reactName,h=[];n!==null&&n!==i;){var b=n,T=b.alternate,R=b.stateNode;if(b=b.tag,T!==null&&T===i)break;b!==5&&b!==26&&b!==27||R===null||(T=R,s?(R=pi(n,o),R!=null&&h.unshift($i(n,R,T))):s||(R=pi(n,o),R!=null&&h.push($i(n,R,T)))),n=n.return}h.length!==0&&t.push({event:e,listeners:h})}var wv=/\r\n?/g,Rv=/\u0000|\uFFFD/g;function sm(t){return(typeof t=="string"?t:""+t).replace(wv,`
`).replace(Rv,"")}function um(t,e){return e=sm(e),sm(t)===e}function Ds(){}function Et(t,e,n,i,s,o){switch(n){case"children":typeof i=="string"?e==="body"||e==="textarea"&&i===""||xa(t,i):(typeof i=="number"||typeof i=="bigint")&&e!=="body"&&xa(t,""+i);break;case"className":Nl(t,"class",i);break;case"tabIndex":Nl(t,"tabindex",i);break;case"dir":case"role":case"viewBox":case"width":case"height":Nl(t,n,i);break;case"style":of(t,i,o);break;case"data":if(e!=="object"){Nl(t,"data",i);break}case"src":case"href":if(i===""&&(e!=="a"||n!=="href")){t.removeAttribute(n);break}if(i==null||typeof i=="function"||typeof i=="symbol"||typeof i=="boolean"){t.removeAttribute(n);break}i=_l(""+i),t.setAttribute(n,i);break;case"action":case"formAction":if(typeof i=="function"){t.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof o=="function"&&(n==="formAction"?(e!=="input"&&Et(t,e,"name",s.name,s,null),Et(t,e,"formEncType",s.formEncType,s,null),Et(t,e,"formMethod",s.formMethod,s,null),Et(t,e,"formTarget",s.formTarget,s,null)):(Et(t,e,"encType",s.encType,s,null),Et(t,e,"method",s.method,s,null),Et(t,e,"target",s.target,s,null)));if(i==null||typeof i=="symbol"||typeof i=="boolean"){t.removeAttribute(n);break}i=_l(""+i),t.setAttribute(n,i);break;case"onClick":i!=null&&(t.onclick=Ds);break;case"onScroll":i!=null&&mt("scroll",t);break;case"onScrollEnd":i!=null&&mt("scrollend",t);break;case"dangerouslySetInnerHTML":if(i!=null){if(typeof i!="object"||!("__html"in i))throw Error(r(61));if(n=i.__html,n!=null){if(s.children!=null)throw Error(r(60));t.innerHTML=n}}break;case"multiple":t.multiple=i&&typeof i!="function"&&typeof i!="symbol";break;case"muted":t.muted=i&&typeof i!="function"&&typeof i!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(i==null||typeof i=="function"||typeof i=="boolean"||typeof i=="symbol"){t.removeAttribute("xlink:href");break}n=_l(""+i),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":i!=null&&typeof i!="function"&&typeof i!="symbol"?t.setAttribute(n,""+i):t.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":i&&typeof i!="function"&&typeof i!="symbol"?t.setAttribute(n,""):t.removeAttribute(n);break;case"capture":case"download":i===!0?t.setAttribute(n,""):i!==!1&&i!=null&&typeof i!="function"&&typeof i!="symbol"?t.setAttribute(n,i):t.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":i!=null&&typeof i!="function"&&typeof i!="symbol"&&!isNaN(i)&&1<=i?t.setAttribute(n,i):t.removeAttribute(n);break;case"rowSpan":case"start":i==null||typeof i=="function"||typeof i=="symbol"||isNaN(i)?t.removeAttribute(n):t.setAttribute(n,i);break;case"popover":mt("beforetoggle",t),mt("toggle",t),Ol(t,"popover",i);break;case"xlinkActuate":Pe(t,"http://www.w3.org/1999/xlink","xlink:actuate",i);break;case"xlinkArcrole":Pe(t,"http://www.w3.org/1999/xlink","xlink:arcrole",i);break;case"xlinkRole":Pe(t,"http://www.w3.org/1999/xlink","xlink:role",i);break;case"xlinkShow":Pe(t,"http://www.w3.org/1999/xlink","xlink:show",i);break;case"xlinkTitle":Pe(t,"http://www.w3.org/1999/xlink","xlink:title",i);break;case"xlinkType":Pe(t,"http://www.w3.org/1999/xlink","xlink:type",i);break;case"xmlBase":Pe(t,"http://www.w3.org/XML/1998/namespace","xml:base",i);break;case"xmlLang":Pe(t,"http://www.w3.org/XML/1998/namespace","xml:lang",i);break;case"xmlSpace":Pe(t,"http://www.w3.org/XML/1998/namespace","xml:space",i);break;case"is":Ol(t,"is",i);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=sy.get(n)||n,Ol(t,n,i))}}function Ir(t,e,n,i,s,o){switch(n){case"style":of(t,i,o);break;case"dangerouslySetInnerHTML":if(i!=null){if(typeof i!="object"||!("__html"in i))throw Error(r(61));if(n=i.__html,n!=null){if(s.children!=null)throw Error(r(60));t.innerHTML=n}}break;case"children":typeof i=="string"?xa(t,i):(typeof i=="number"||typeof i=="bigint")&&xa(t,""+i);break;case"onScroll":i!=null&&mt("scroll",t);break;case"onScrollEnd":i!=null&&mt("scrollend",t);break;case"onClick":i!=null&&(t.onclick=Ds);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!$c.hasOwnProperty(n))t:{if(n[0]==="o"&&n[1]==="n"&&(s=n.endsWith("Capture"),e=n.slice(2,s?n.length-7:void 0),o=t[le]||null,o=o!=null?o[n]:null,typeof o=="function"&&t.removeEventListener(e,o,s),typeof i=="function")){typeof o!="function"&&o!==null&&(n in t?t[n]=null:t.hasAttribute(n)&&t.removeAttribute(n)),t.addEventListener(e,i,s);break t}n in t?t[n]=i:i===!0?t.setAttribute(n,""):Ol(t,n,i)}}}function $t(t,e,n){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":mt("error",t),mt("load",t);var i=!1,s=!1,o;for(o in n)if(n.hasOwnProperty(o)){var h=n[o];if(h!=null)switch(o){case"src":i=!0;break;case"srcSet":s=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(r(137,e));default:Et(t,e,o,h,n,null)}}s&&Et(t,e,"srcSet",n.srcSet,n,null),i&&Et(t,e,"src",n.src,n,null);return;case"input":mt("invalid",t);var b=o=h=s=null,T=null,R=null;for(i in n)if(n.hasOwnProperty(i)){var L=n[i];if(L!=null)switch(i){case"name":s=L;break;case"type":h=L;break;case"checked":T=L;break;case"defaultChecked":R=L;break;case"value":o=L;break;case"defaultValue":b=L;break;case"children":case"dangerouslySetInnerHTML":if(L!=null)throw Error(r(137,e));break;default:Et(t,e,i,L,n,null)}}lf(t,o,b,T,R,h,s,!1),zl(t);return;case"select":mt("invalid",t),i=h=o=null;for(s in n)if(n.hasOwnProperty(s)&&(b=n[s],b!=null))switch(s){case"value":o=b;break;case"defaultValue":h=b;break;case"multiple":i=b;default:Et(t,e,s,b,n,null)}e=o,n=h,t.multiple=!!i,e!=null?ba(t,!!i,e,!1):n!=null&&ba(t,!!i,n,!0);return;case"textarea":mt("invalid",t),o=s=i=null;for(h in n)if(n.hasOwnProperty(h)&&(b=n[h],b!=null))switch(h){case"value":i=b;break;case"defaultValue":s=b;break;case"children":o=b;break;case"dangerouslySetInnerHTML":if(b!=null)throw Error(r(91));break;default:Et(t,e,h,b,n,null)}uf(t,i,s,o),zl(t);return;case"option":for(T in n)if(n.hasOwnProperty(T)&&(i=n[T],i!=null))switch(T){case"selected":t.selected=i&&typeof i!="function"&&typeof i!="symbol";break;default:Et(t,e,T,i,n,null)}return;case"dialog":mt("beforetoggle",t),mt("toggle",t),mt("cancel",t),mt("close",t);break;case"iframe":case"object":mt("load",t);break;case"video":case"audio":for(i=0;i<Ji.length;i++)mt(Ji[i],t);break;case"image":mt("error",t),mt("load",t);break;case"details":mt("toggle",t);break;case"embed":case"source":case"link":mt("error",t),mt("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(R in n)if(n.hasOwnProperty(R)&&(i=n[R],i!=null))switch(R){case"children":case"dangerouslySetInnerHTML":throw Error(r(137,e));default:Et(t,e,R,i,n,null)}return;default:if(pu(e)){for(L in n)n.hasOwnProperty(L)&&(i=n[L],i!==void 0&&Ir(t,e,L,i,n,void 0));return}}for(b in n)n.hasOwnProperty(b)&&(i=n[b],i!=null&&Et(t,e,b,i,n,null))}function Ov(t,e,n,i){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var s=null,o=null,h=null,b=null,T=null,R=null,L=null;for(z in n){var q=n[z];if(n.hasOwnProperty(z)&&q!=null)switch(z){case"checked":break;case"value":break;case"defaultValue":T=q;default:i.hasOwnProperty(z)||Et(t,e,z,null,i,q)}}for(var N in i){var z=i[N];if(q=n[N],i.hasOwnProperty(N)&&(z!=null||q!=null))switch(N){case"type":o=z;break;case"name":s=z;break;case"checked":R=z;break;case"defaultChecked":L=z;break;case"value":h=z;break;case"defaultValue":b=z;break;case"children":case"dangerouslySetInnerHTML":if(z!=null)throw Error(r(137,e));break;default:z!==q&&Et(t,e,N,z,i,q)}}hu(t,h,b,T,R,L,o,s);return;case"select":z=h=b=N=null;for(o in n)if(T=n[o],n.hasOwnProperty(o)&&T!=null)switch(o){case"value":break;case"multiple":z=T;default:i.hasOwnProperty(o)||Et(t,e,o,null,i,T)}for(s in i)if(o=i[s],T=n[s],i.hasOwnProperty(s)&&(o!=null||T!=null))switch(s){case"value":N=o;break;case"defaultValue":b=o;break;case"multiple":h=o;default:o!==T&&Et(t,e,s,o,i,T)}e=b,n=h,i=z,N!=null?ba(t,!!n,N,!1):!!i!=!!n&&(e!=null?ba(t,!!n,e,!0):ba(t,!!n,n?[]:"",!1));return;case"textarea":z=N=null;for(b in n)if(s=n[b],n.hasOwnProperty(b)&&s!=null&&!i.hasOwnProperty(b))switch(b){case"value":break;case"children":break;default:Et(t,e,b,null,i,s)}for(h in i)if(s=i[h],o=n[h],i.hasOwnProperty(h)&&(s!=null||o!=null))switch(h){case"value":N=s;break;case"defaultValue":z=s;break;case"children":break;case"dangerouslySetInnerHTML":if(s!=null)throw Error(r(91));break;default:s!==o&&Et(t,e,h,s,i,o)}sf(t,N,z);return;case"option":for(var lt in n)if(N=n[lt],n.hasOwnProperty(lt)&&N!=null&&!i.hasOwnProperty(lt))switch(lt){case"selected":t.selected=!1;break;default:Et(t,e,lt,null,i,N)}for(T in i)if(N=i[T],z=n[T],i.hasOwnProperty(T)&&N!==z&&(N!=null||z!=null))switch(T){case"selected":t.selected=N&&typeof N!="function"&&typeof N!="symbol";break;default:Et(t,e,T,N,i,z)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var nt in n)N=n[nt],n.hasOwnProperty(nt)&&N!=null&&!i.hasOwnProperty(nt)&&Et(t,e,nt,null,i,N);for(R in i)if(N=i[R],z=n[R],i.hasOwnProperty(R)&&N!==z&&(N!=null||z!=null))switch(R){case"children":case"dangerouslySetInnerHTML":if(N!=null)throw Error(r(137,e));break;default:Et(t,e,R,N,i,z)}return;default:if(pu(e)){for(var Mt in n)N=n[Mt],n.hasOwnProperty(Mt)&&N!==void 0&&!i.hasOwnProperty(Mt)&&Ir(t,e,Mt,void 0,i,N);for(L in i)N=i[L],z=n[L],!i.hasOwnProperty(L)||N===z||N===void 0&&z===void 0||Ir(t,e,L,N,i,z);return}}for(var D in n)N=n[D],n.hasOwnProperty(D)&&N!=null&&!i.hasOwnProperty(D)&&Et(t,e,D,null,i,N);for(q in i)N=i[q],z=n[q],!i.hasOwnProperty(q)||N===z||N==null&&z==null||Et(t,e,q,N,i,z)}var to=null,eo=null;function Cs(t){return t.nodeType===9?t:t.ownerDocument}function rm(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function om(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function no(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var ao=null;function Nv(){var t=window.event;return t&&t.type==="popstate"?t===ao?!1:(ao=t,!0):(ao=null,!1)}var cm=typeof setTimeout=="function"?setTimeout:void 0,zv=typeof clearTimeout=="function"?clearTimeout:void 0,fm=typeof Promise=="function"?Promise:void 0,Vv=typeof queueMicrotask=="function"?queueMicrotask:typeof fm<"u"?function(t){return fm.resolve(null).then(t).catch(_v)}:cm;function _v(t){setTimeout(function(){throw t})}function On(t){return t==="head"}function dm(t,e){var n=e,i=0,s=0;do{var o=n.nextSibling;if(t.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(0<i&&8>i){n=i;var h=t.ownerDocument;if(n&1&&Wi(h.documentElement),n&2&&Wi(h.body),n&4)for(n=h.head,Wi(n),h=n.firstChild;h;){var b=h.nextSibling,T=h.nodeName;h[hi]||T==="SCRIPT"||T==="STYLE"||T==="LINK"&&h.rel.toLowerCase()==="stylesheet"||n.removeChild(h),h=b}}if(s===0){t.removeChild(o),sl(e);return}s--}else n==="$"||n==="$?"||n==="$!"?s++:i=n.charCodeAt(0)-48;else i=0;n=o}while(n);sl(e)}function io(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var n=e;switch(e=e.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":io(n),ou(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}t.removeChild(n)}}function Bv(t,e,n,i){for(;t.nodeType===1;){var s=n;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!i&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(i){if(!t[hi])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(o=t.getAttribute("rel"),o==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(o!==s.rel||t.getAttribute("href")!==(s.href==null||s.href===""?null:s.href)||t.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin)||t.getAttribute("title")!==(s.title==null?null:s.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(o=t.getAttribute("src"),(o!==(s.src==null?null:s.src)||t.getAttribute("type")!==(s.type==null?null:s.type)||t.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin))&&o&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var o=s.name==null?null:""+s.name;if(s.type==="hidden"&&t.getAttribute("name")===o)return t}else return t;if(t=Ue(t.nextSibling),t===null)break}return null}function Uv(t,e,n){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!n||(t=Ue(t.nextSibling),t===null))return null;return t}function lo(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function Lv(t,e){var n=t.ownerDocument;if(t.data!=="$?"||n.readyState==="complete")e();else{var i=function(){e(),n.removeEventListener("DOMContentLoaded",i)};n.addEventListener("DOMContentLoaded",i),t._reactRetry=i}}function Ue(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var so=null;function hm(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}function mm(t,e,n){switch(e=Cs(n),t){case"html":if(t=e.documentElement,!t)throw Error(r(452));return t;case"head":if(t=e.head,!t)throw Error(r(453));return t;case"body":if(t=e.body,!t)throw Error(r(454));return t;default:throw Error(r(451))}}function Wi(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);ou(t)}var Re=new Map,pm=new Set;function ws(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var cn=k.d;k.d={f:Hv,r:qv,D:Yv,C:Gv,L:Xv,m:Zv,X:Kv,S:Qv,M:kv};function Hv(){var t=cn.f(),e=xs();return t||e}function qv(t){var e=pa(t);e!==null&&e.tag===5&&e.type==="form"?_d(e):cn.r(t)}var Pa=typeof document>"u"?null:document;function gm(t,e,n){var i=Pa;if(i&&typeof e=="string"&&e){var s=Ae(e);s='link[rel="'+t+'"][href="'+s+'"]',typeof n=="string"&&(s+='[crossorigin="'+n+'"]'),pm.has(s)||(pm.add(s),t={rel:t,crossOrigin:n,href:e},i.querySelector(s)===null&&(e=i.createElement("link"),$t(e,"link",t),Qt(e),i.head.appendChild(e)))}}function Yv(t){cn.D(t),gm("dns-prefetch",t,null)}function Gv(t,e){cn.C(t,e),gm("preconnect",t,e)}function Xv(t,e,n){cn.L(t,e,n);var i=Pa;if(i&&t&&e){var s='link[rel="preload"][as="'+Ae(e)+'"]';e==="image"&&n&&n.imageSrcSet?(s+='[imagesrcset="'+Ae(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(s+='[imagesizes="'+Ae(n.imageSizes)+'"]')):s+='[href="'+Ae(t)+'"]';var o=s;switch(e){case"style":o=Ja(t);break;case"script":o=$a(t)}Re.has(o)||(t=v({rel:"preload",href:e==="image"&&n&&n.imageSrcSet?void 0:t,as:e},n),Re.set(o,t),i.querySelector(s)!==null||e==="style"&&i.querySelector(Ii(o))||e==="script"&&i.querySelector(tl(o))||(e=i.createElement("link"),$t(e,"link",t),Qt(e),i.head.appendChild(e)))}}function Zv(t,e){cn.m(t,e);var n=Pa;if(n&&t){var i=e&&typeof e.as=="string"?e.as:"script",s='link[rel="modulepreload"][as="'+Ae(i)+'"][href="'+Ae(t)+'"]',o=s;switch(i){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":o=$a(t)}if(!Re.has(o)&&(t=v({rel:"modulepreload",href:t},e),Re.set(o,t),n.querySelector(s)===null)){switch(i){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(tl(o)))return}i=n.createElement("link"),$t(i,"link",t),Qt(i),n.head.appendChild(i)}}}function Qv(t,e,n){cn.S(t,e,n);var i=Pa;if(i&&t){var s=ga(i).hoistableStyles,o=Ja(t);e=e||"default";var h=s.get(o);if(!h){var b={loading:0,preload:null};if(h=i.querySelector(Ii(o)))b.loading=5;else{t=v({rel:"stylesheet",href:t,"data-precedence":e},n),(n=Re.get(o))&&uo(t,n);var T=h=i.createElement("link");Qt(T),$t(T,"link",t),T._p=new Promise(function(R,L){T.onload=R,T.onerror=L}),T.addEventListener("load",function(){b.loading|=1}),T.addEventListener("error",function(){b.loading|=2}),b.loading|=4,Rs(h,e,i)}h={type:"stylesheet",instance:h,count:1,state:b},s.set(o,h)}}}function Kv(t,e){cn.X(t,e);var n=Pa;if(n&&t){var i=ga(n).hoistableScripts,s=$a(t),o=i.get(s);o||(o=n.querySelector(tl(s)),o||(t=v({src:t,async:!0},e),(e=Re.get(s))&&ro(t,e),o=n.createElement("script"),Qt(o),$t(o,"link",t),n.head.appendChild(o)),o={type:"script",instance:o,count:1,state:null},i.set(s,o))}}function kv(t,e){cn.M(t,e);var n=Pa;if(n&&t){var i=ga(n).hoistableScripts,s=$a(t),o=i.get(s);o||(o=n.querySelector(tl(s)),o||(t=v({src:t,async:!0,type:"module"},e),(e=Re.get(s))&&ro(t,e),o=n.createElement("script"),Qt(o),$t(o,"link",t),n.head.appendChild(o)),o={type:"script",instance:o,count:1,state:null},i.set(s,o))}}function ym(t,e,n,i){var s=(s=it.current)?ws(s):null;if(!s)throw Error(r(446));switch(t){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(e=Ja(n.href),n=ga(s).hoistableStyles,i=n.get(e),i||(i={type:"style",instance:null,count:0,state:null},n.set(e,i)),i):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){t=Ja(n.href);var o=ga(s).hoistableStyles,h=o.get(t);if(h||(s=s.ownerDocument||s,h={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},o.set(t,h),(o=s.querySelector(Ii(t)))&&!o._p&&(h.instance=o,h.state.loading=5),Re.has(t)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Re.set(t,n),o||Fv(s,t,n,h.state))),e&&i===null)throw Error(r(528,""));return h}if(e&&i!==null)throw Error(r(529,""));return null;case"script":return e=n.async,n=n.src,typeof n=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=$a(n),n=ga(s).hoistableScripts,i=n.get(e),i||(i={type:"script",instance:null,count:0,state:null},n.set(e,i)),i):{type:"void",instance:null,count:0,state:null};default:throw Error(r(444,t))}}function Ja(t){return'href="'+Ae(t)+'"'}function Ii(t){return'link[rel="stylesheet"]['+t+"]"}function vm(t){return v({},t,{"data-precedence":t.precedence,precedence:null})}function Fv(t,e,n,i){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?i.loading=1:(e=t.createElement("link"),i.preload=e,e.addEventListener("load",function(){return i.loading|=1}),e.addEventListener("error",function(){return i.loading|=2}),$t(e,"link",n),Qt(e),t.head.appendChild(e))}function $a(t){return'[src="'+Ae(t)+'"]'}function tl(t){return"script[async]"+t}function bm(t,e,n){if(e.count++,e.instance===null)switch(e.type){case"style":var i=t.querySelector('style[data-href~="'+Ae(n.href)+'"]');if(i)return e.instance=i,Qt(i),i;var s=v({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return i=(t.ownerDocument||t).createElement("style"),Qt(i),$t(i,"style",s),Rs(i,n.precedence,t),e.instance=i;case"stylesheet":s=Ja(n.href);var o=t.querySelector(Ii(s));if(o)return e.state.loading|=4,e.instance=o,Qt(o),o;i=vm(n),(s=Re.get(s))&&uo(i,s),o=(t.ownerDocument||t).createElement("link"),Qt(o);var h=o;return h._p=new Promise(function(b,T){h.onload=b,h.onerror=T}),$t(o,"link",i),e.state.loading|=4,Rs(o,n.precedence,t),e.instance=o;case"script":return o=$a(n.src),(s=t.querySelector(tl(o)))?(e.instance=s,Qt(s),s):(i=n,(s=Re.get(o))&&(i=v({},n),ro(i,s)),t=t.ownerDocument||t,s=t.createElement("script"),Qt(s),$t(s,"link",i),t.head.appendChild(s),e.instance=s);case"void":return null;default:throw Error(r(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(i=e.instance,e.state.loading|=4,Rs(i,n.precedence,t));return e.instance}function Rs(t,e,n){for(var i=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),s=i.length?i[i.length-1]:null,o=s,h=0;h<i.length;h++){var b=i[h];if(b.dataset.precedence===e)o=b;else if(o!==s)break}o?o.parentNode.insertBefore(t,o.nextSibling):(e=n.nodeType===9?n.head:n,e.insertBefore(t,e.firstChild))}function uo(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function ro(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Os=null;function xm(t,e,n){if(Os===null){var i=new Map,s=Os=new Map;s.set(n,i)}else s=Os,i=s.get(n),i||(i=new Map,s.set(n,i));if(i.has(t))return i;for(i.set(t,null),n=n.getElementsByTagName(t),s=0;s<n.length;s++){var o=n[s];if(!(o[hi]||o[It]||t==="link"&&o.getAttribute("rel")==="stylesheet")&&o.namespaceURI!=="http://www.w3.org/2000/svg"){var h=o.getAttribute(e)||"";h=t+h;var b=i.get(h);b?b.push(o):i.set(h,[o])}}return i}function Sm(t,e,n){t=t.ownerDocument||t,t.head.insertBefore(n,e==="title"?t.querySelector("head > title"):null)}function Pv(t,e,n){if(n===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Tm(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var el=null;function Jv(){}function $v(t,e,n){if(el===null)throw Error(r(475));var i=el;if(e.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var s=Ja(n.href),o=t.querySelector(Ii(s));if(o){t=o._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(i.count++,i=Ns.bind(i),t.then(i,i)),e.state.loading|=4,e.instance=o,Qt(o);return}o=t.ownerDocument||t,n=vm(n),(s=Re.get(s))&&uo(n,s),o=o.createElement("link"),Qt(o);var h=o;h._p=new Promise(function(b,T){h.onload=b,h.onerror=T}),$t(o,"link",n),e.instance=o}i.stylesheets===null&&(i.stylesheets=new Map),i.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(i.count++,e=Ns.bind(i),t.addEventListener("load",e),t.addEventListener("error",e))}}function Wv(){if(el===null)throw Error(r(475));var t=el;return t.stylesheets&&t.count===0&&oo(t,t.stylesheets),0<t.count?function(e){var n=setTimeout(function(){if(t.stylesheets&&oo(t,t.stylesheets),t.unsuspend){var i=t.unsuspend;t.unsuspend=null,i()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(n)}}:null}function Ns(){if(this.count--,this.count===0){if(this.stylesheets)oo(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var zs=null;function oo(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,zs=new Map,e.forEach(Iv,t),zs=null,Ns.call(t))}function Iv(t,e){if(!(e.state.loading&4)){var n=zs.get(t);if(n)var i=n.get(null);else{n=new Map,zs.set(t,n);for(var s=t.querySelectorAll("link[data-precedence],style[data-precedence]"),o=0;o<s.length;o++){var h=s[o];(h.nodeName==="LINK"||h.getAttribute("media")!=="not all")&&(n.set(h.dataset.precedence,h),i=h)}i&&n.set(null,i)}s=e.instance,h=s.getAttribute("data-precedence"),o=n.get(h)||i,o===i&&n.set(null,s),n.set(h,s),this.count++,i=Ns.bind(this),s.addEventListener("load",i),s.addEventListener("error",i),o?o.parentNode.insertBefore(s,o.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(s,t.firstChild)),e.state.loading|=4}}var nl={$$typeof:M,Provider:null,Consumer:null,_currentValue:J,_currentValue2:J,_threadCount:0};function tb(t,e,n,i,s,o,h,b){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=lu(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=lu(0),this.hiddenUpdates=lu(null),this.identifierPrefix=i,this.onUncaughtError=s,this.onCaughtError=o,this.onRecoverableError=h,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=b,this.incompleteTransitions=new Map}function Am(t,e,n,i,s,o,h,b,T,R,L,q){return t=new tb(t,e,n,h,b,T,R,q),e=1,o===!0&&(e|=24),o=pe(3,null,null,e),t.current=o,o.stateNode=t,e=Zu(),e.refCount++,t.pooledCache=e,e.refCount++,o.memoizedState={element:i,isDehydrated:n,cache:e},Fu(o),t}function Em(t){return t?(t=Ca,t):Ca}function Mm(t,e,n,i,s,o){s=Em(s),i.context===null?i.context=s:i.pendingContext=s,i=vn(e),i.payload={element:n},o=o===void 0?null:o,o!==null&&(i.callback=o),n=bn(t,i,e),n!==null&&(xe(n,t,e),Ni(n,t,e))}function jm(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function co(t,e){jm(t,e),(t=t.alternate)&&jm(t,e)}function Dm(t){if(t.tag===13){var e=Da(t,67108864);e!==null&&xe(e,t,67108864),co(t,67108864)}}var Vs=!0;function eb(t,e,n,i){var s=U.T;U.T=null;var o=k.p;try{k.p=2,fo(t,e,n,i)}finally{k.p=o,U.T=s}}function nb(t,e,n,i){var s=U.T;U.T=null;var o=k.p;try{k.p=8,fo(t,e,n,i)}finally{k.p=o,U.T=s}}function fo(t,e,n,i){if(Vs){var s=ho(i);if(s===null)Wr(t,e,i,_s,n),wm(t,i);else if(ib(s,t,e,n,i))i.stopPropagation();else if(wm(t,i),e&4&&-1<ab.indexOf(t)){for(;s!==null;){var o=pa(s);if(o!==null)switch(o.tag){case 3:if(o=o.stateNode,o.current.memoizedState.isDehydrated){var h=Xn(o.pendingLanes);if(h!==0){var b=o;for(b.pendingLanes|=2,b.entangledLanes|=2;h;){var T=1<<31-he(h);b.entanglements[1]|=T,h&=~T}Ze(o),(bt&6)===0&&(vs=He()+500,Pi(0))}}break;case 13:b=Da(o,2),b!==null&&xe(b,o,2),xs(),co(o,2)}if(o=ho(i),o===null&&Wr(t,e,i,_s,n),o===s)break;s=o}s!==null&&i.stopPropagation()}else Wr(t,e,i,null,n)}}function ho(t){return t=yu(t),mo(t)}var _s=null;function mo(t){if(_s=null,t=ma(t),t!==null){var e=d(t);if(e===null)t=null;else{var n=e.tag;if(n===13){if(t=f(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return _s=t,null}function Cm(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(G0()){case Yc:return 2;case Gc:return 8;case Dl:case X0:return 32;case Xc:return 268435456;default:return 32}default:return 32}}var po=!1,Nn=null,zn=null,Vn=null,al=new Map,il=new Map,_n=[],ab="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function wm(t,e){switch(t){case"focusin":case"focusout":Nn=null;break;case"dragenter":case"dragleave":zn=null;break;case"mouseover":case"mouseout":Vn=null;break;case"pointerover":case"pointerout":al.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":il.delete(e.pointerId)}}function ll(t,e,n,i,s,o){return t===null||t.nativeEvent!==o?(t={blockedOn:e,domEventName:n,eventSystemFlags:i,nativeEvent:o,targetContainers:[s]},e!==null&&(e=pa(e),e!==null&&Dm(e)),t):(t.eventSystemFlags|=i,e=t.targetContainers,s!==null&&e.indexOf(s)===-1&&e.push(s),t)}function ib(t,e,n,i,s){switch(e){case"focusin":return Nn=ll(Nn,t,e,n,i,s),!0;case"dragenter":return zn=ll(zn,t,e,n,i,s),!0;case"mouseover":return Vn=ll(Vn,t,e,n,i,s),!0;case"pointerover":var o=s.pointerId;return al.set(o,ll(al.get(o)||null,t,e,n,i,s)),!0;case"gotpointercapture":return o=s.pointerId,il.set(o,ll(il.get(o)||null,t,e,n,i,s)),!0}return!1}function Rm(t){var e=ma(t.target);if(e!==null){var n=d(e);if(n!==null){if(e=n.tag,e===13){if(e=f(n),e!==null){t.blockedOn=e,$0(t.priority,function(){if(n.tag===13){var i=be();i=su(i);var s=Da(n,i);s!==null&&xe(s,n,i),co(n,i)}});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function Bs(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=ho(t.nativeEvent);if(n===null){n=t.nativeEvent;var i=new n.constructor(n.type,n);gu=i,n.target.dispatchEvent(i),gu=null}else return e=pa(n),e!==null&&Dm(e),t.blockedOn=n,!1;e.shift()}return!0}function Om(t,e,n){Bs(t)&&n.delete(e)}function lb(){po=!1,Nn!==null&&Bs(Nn)&&(Nn=null),zn!==null&&Bs(zn)&&(zn=null),Vn!==null&&Bs(Vn)&&(Vn=null),al.forEach(Om),il.forEach(Om)}function Us(t,e){t.blockedOn===e&&(t.blockedOn=null,po||(po=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,lb)))}var Ls=null;function Nm(t){Ls!==t&&(Ls=t,a.unstable_scheduleCallback(a.unstable_NormalPriority,function(){Ls===t&&(Ls=null);for(var e=0;e<t.length;e+=3){var n=t[e],i=t[e+1],s=t[e+2];if(typeof i!="function"){if(mo(i||n)===null)continue;break}var o=pa(n);o!==null&&(t.splice(e,3),e-=3,hr(o,{pending:!0,data:s,method:n.method,action:i},i,s))}}))}function sl(t){function e(T){return Us(T,t)}Nn!==null&&Us(Nn,t),zn!==null&&Us(zn,t),Vn!==null&&Us(Vn,t),al.forEach(e),il.forEach(e);for(var n=0;n<_n.length;n++){var i=_n[n];i.blockedOn===t&&(i.blockedOn=null)}for(;0<_n.length&&(n=_n[0],n.blockedOn===null);)Rm(n),n.blockedOn===null&&_n.shift();if(n=(t.ownerDocument||t).$$reactFormReplay,n!=null)for(i=0;i<n.length;i+=3){var s=n[i],o=n[i+1],h=s[le]||null;if(typeof o=="function")h||Nm(n);else if(h){var b=null;if(o&&o.hasAttribute("formAction")){if(s=o,h=o[le]||null)b=h.formAction;else if(mo(s)!==null)continue}else b=h.action;typeof b=="function"?n[i+1]=b:(n.splice(i,3),i-=3),Nm(n)}}}function go(t){this._internalRoot=t}Hs.prototype.render=go.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(r(409));var n=e.current,i=be();Mm(n,i,t,e,null,null)},Hs.prototype.unmount=go.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Mm(t.current,2,null,t,null,null),xs(),e[ha]=null}};function Hs(t){this._internalRoot=t}Hs.prototype.unstable_scheduleHydration=function(t){if(t){var e=Fc();t={blockedOn:null,target:t,priority:e};for(var n=0;n<_n.length&&e!==0&&e<_n[n].priority;n++);_n.splice(n,0,t),n===0&&Rm(t)}};var zm=l.version;if(zm!=="19.1.0")throw Error(r(527,zm,"19.1.0"));k.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(r(188)):(t=Object.keys(t).join(","),Error(r(268,t)));return t=p(e),t=t!==null?g(t):null,t=t===null?null:t.stateNode,t};var sb={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:U,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var qs=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!qs.isDisabled&&qs.supportsFiber)try{ci=qs.inject(sb),de=qs}catch{}}return rl.createRoot=function(t,e){if(!c(t))throw Error(r(299));var n=!1,i="",s=Pd,o=Jd,h=$d,b=null;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(i=e.identifierPrefix),e.onUncaughtError!==void 0&&(s=e.onUncaughtError),e.onCaughtError!==void 0&&(o=e.onCaughtError),e.onRecoverableError!==void 0&&(h=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(b=e.unstable_transitionCallbacks)),e=Am(t,1,!1,null,null,n,i,s,o,h,b,null),t[ha]=e.current,$r(t),new go(e)},rl.hydrateRoot=function(t,e,n){if(!c(t))throw Error(r(299));var i=!1,s="",o=Pd,h=Jd,b=$d,T=null,R=null;return n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onUncaughtError!==void 0&&(o=n.onUncaughtError),n.onCaughtError!==void 0&&(h=n.onCaughtError),n.onRecoverableError!==void 0&&(b=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(T=n.unstable_transitionCallbacks),n.formState!==void 0&&(R=n.formState)),e=Am(t,1,!0,e,n??null,i,s,o,h,b,T,R),e.context=Em(null),n=e.current,i=be(),i=su(i),s=vn(i),s.callback=null,bn(n,s,i),n=i,e.current.lanes=n,di(e,n),Ze(e),t[ha]=e.current,$r(t),new Hs(e)},rl.version="19.1.0",rl}var Qm;function gb(){if(Qm)return bo.exports;Qm=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(l){console.error(l)}}return a(),bo.exports=pb(),bo.exports}var yb=gb();const Km=a=>{let l;const u=new Set,r=(g,v)=>{const x=typeof g=="function"?g(l):g;if(!Object.is(x,l)){const S=l;l=v??(typeof x!="object"||x===null)?x:Object.assign({},l,x),u.forEach(V=>V(l,S))}},c=()=>l,m={setState:r,getState:c,getInitialState:()=>p,subscribe:g=>(u.add(g),()=>u.delete(g))},p=l=a(r,c,m);return m},vb=a=>a?Km(a):Km,bb=a=>a;function xb(a,l=bb){const u=Hm.useSyncExternalStore(a.subscribe,()=>l(a.getState()),()=>l(a.getInitialState()));return Hm.useDebugValue(u),u}const km=a=>{const l=vb(a),u=r=>xb(l,r);return Object.assign(u,l),u},Sb=a=>a?km(a):km,ic=Sb(a=>({selectedComponent:"Button",componentConfigs:{Button:{variant:"primary",size:"md",animation:"fade-in",disabled:!1,text:"Click me"},Modal:{size:"md",animation:"scale-in",backdrop:!0,closable:!0,title:"Modal Title",content:"This is modal content"},Carousel:{autoplay:!0,interval:3e3,showDots:!0,showArrows:!0,animation:"slide",items:[{id:1,content:"Slide 1",image:"https://via.placeholder.com/400x200/3B82F6/white?text=Slide+1"},{id:2,content:"Slide 2",image:"https://via.placeholder.com/400x200/10B981/white?text=Slide+2"},{id:3,content:"Slide 3",image:"https://via.placeholder.com/400x200/F59E0B/white?text=Slide+3"}]},Card:{variant:"default",shadow:"md",animation:"fade-in",title:"Card Title",content:"This is card content with some description text.",showImage:!0,imageUrl:"https://via.placeholder.com/300x150/6366F1/white?text=Card+Image"},Toast:{type:"success",position:"top-right",animation:"slide-in",autoClose:!0,duration:3e3,message:"This is a toast message!"}},setSelectedComponent:l=>a({selectedComponent:l}),updateComponentConfig:(l,u)=>a(r=>({componentConfigs:{...r.componentConfigs,[l]:{...r.componentConfigs[l],...u}}})),resetComponentConfig:l=>a(u=>{const r={Button:{variant:"primary",size:"md",animation:"fade-in",disabled:!1,text:"Click me"},Modal:{size:"md",animation:"scale-in",backdrop:!0,closable:!0,title:"Modal Title",content:"This is modal content"},Carousel:{autoplay:!0,interval:3e3,showDots:!0,showArrows:!0,animation:"slide",items:[{id:1,content:"Slide 1",image:"https://via.placeholder.com/400x200/3B82F6/white?text=Slide+1"},{id:2,content:"Slide 2",image:"https://via.placeholder.com/400x200/10B981/white?text=Slide+2"},{id:3,content:"Slide 3",image:"https://via.placeholder.com/400x200/F59E0B/white?text=Slide+3"}]},Card:{variant:"default",shadow:"md",animation:"fade-in",title:"Card Title",content:"This is card content with some description text.",showImage:!0,imageUrl:"https://via.placeholder.com/300x150/6366F1/white?text=Card+Image"},Toast:{type:"success",position:"top-right",animation:"slide-in",autoClose:!0,duration:3e3,message:"This is a toast message!"}};return{componentConfigs:{...u.componentConfigs,[l]:r[l]}}})})),Tb=()=>{const{selectedComponent:a,componentConfigs:l,setSelectedComponent:u,updateComponentConfig:r,resetComponentConfig:c}=ic(),d=l[a],f=(j,Y)=>{r(a,{[j]:Y})},m=()=>{c(a)},p=()=>y.jsxs("div",{className:"space-y-4",children:[y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Text"}),y.jsx("input",{type:"text",value:d.text,onChange:j=>f("text",j.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Variant"}),y.jsxs("select",{value:d.variant,onChange:j=>f("variant",j.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[y.jsx("option",{value:"primary",children:"Primary"}),y.jsx("option",{value:"secondary",children:"Secondary"}),y.jsx("option",{value:"success",children:"Success"}),y.jsx("option",{value:"danger",children:"Danger"}),y.jsx("option",{value:"warning",children:"Warning"}),y.jsx("option",{value:"outline",children:"Outline"}),y.jsx("option",{value:"ghost",children:"Ghost"})]})]}),y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Size"}),y.jsxs("select",{value:d.size,onChange:j=>f("size",j.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[y.jsx("option",{value:"sm",children:"Small"}),y.jsx("option",{value:"md",children:"Medium"}),y.jsx("option",{value:"lg",children:"Large"}),y.jsx("option",{value:"xl",children:"Extra Large"})]})]}),y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Animation"}),y.jsxs("select",{value:d.animation,onChange:j=>f("animation",j.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[y.jsx("option",{value:"fade-in",children:"Fade In"}),y.jsx("option",{value:"slide-in",children:"Slide In"}),y.jsx("option",{value:"bounce-in",children:"Bounce In"}),y.jsx("option",{value:"scale-in",children:"Scale In"}),y.jsx("option",{value:"none",children:"None"})]})]}),y.jsxs("div",{className:"flex items-center",children:[y.jsx("input",{type:"checkbox",id:"disabled",checked:d.disabled,onChange:j=>f("disabled",j.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),y.jsx("label",{htmlFor:"disabled",className:"ml-2 block text-sm text-gray-700",children:"Disabled"})]})]}),g=()=>y.jsxs("div",{className:"space-y-4",children:[y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Title"}),y.jsx("input",{type:"text",value:d.title,onChange:j=>f("title",j.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Content"}),y.jsx("textarea",{value:d.content,onChange:j=>f("content",j.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Size"}),y.jsxs("select",{value:d.size,onChange:j=>f("size",j.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[y.jsx("option",{value:"sm",children:"Small"}),y.jsx("option",{value:"md",children:"Medium"}),y.jsx("option",{value:"lg",children:"Large"}),y.jsx("option",{value:"xl",children:"Extra Large"}),y.jsx("option",{value:"2xl",children:"2X Large"}),y.jsx("option",{value:"full",children:"Full Width"})]})]}),y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Animation"}),y.jsxs("select",{value:d.animation,onChange:j=>f("animation",j.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[y.jsx("option",{value:"fade-in",children:"Fade In"}),y.jsx("option",{value:"slide-in",children:"Slide In"}),y.jsx("option",{value:"bounce-in",children:"Bounce In"}),y.jsx("option",{value:"scale-in",children:"Scale In"})]})]}),y.jsxs("div",{className:"flex items-center",children:[y.jsx("input",{type:"checkbox",id:"backdrop",checked:d.backdrop,onChange:j=>f("backdrop",j.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),y.jsx("label",{htmlFor:"backdrop",className:"ml-2 block text-sm text-gray-700",children:"Show Backdrop"})]}),y.jsxs("div",{className:"flex items-center",children:[y.jsx("input",{type:"checkbox",id:"closable",checked:d.closable,onChange:j=>f("closable",j.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),y.jsx("label",{htmlFor:"closable",className:"ml-2 block text-sm text-gray-700",children:"Closable"})]})]}),v=()=>y.jsxs("div",{className:"space-y-4",children:[y.jsxs("div",{className:"flex items-center",children:[y.jsx("input",{type:"checkbox",id:"autoplay",checked:d.autoplay,onChange:j=>f("autoplay",j.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),y.jsx("label",{htmlFor:"autoplay",className:"ml-2 block text-sm text-gray-700",children:"Autoplay"})]}),y.jsxs("div",{children:[y.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Interval (ms): ",d.interval]}),y.jsx("input",{type:"range",min:"1000",max:"10000",step:"500",value:d.interval,onChange:j=>f("interval",parseInt(j.target.value)),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"})]}),y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Animation"}),y.jsxs("select",{value:d.animation,onChange:j=>f("animation",j.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[y.jsx("option",{value:"slide",children:"Slide"}),y.jsx("option",{value:"fade",children:"Fade"}),y.jsx("option",{value:"scale",children:"Scale"})]})]}),y.jsxs("div",{className:"flex items-center",children:[y.jsx("input",{type:"checkbox",id:"showDots",checked:d.showDots,onChange:j=>f("showDots",j.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),y.jsx("label",{htmlFor:"showDots",className:"ml-2 block text-sm text-gray-700",children:"Show Dots"})]}),y.jsxs("div",{className:"flex items-center",children:[y.jsx("input",{type:"checkbox",id:"showArrows",checked:d.showArrows,onChange:j=>f("showArrows",j.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),y.jsx("label",{htmlFor:"showArrows",className:"ml-2 block text-sm text-gray-700",children:"Show Arrows"})]})]}),x=()=>y.jsxs("div",{className:"space-y-4",children:[y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Title"}),y.jsx("input",{type:"text",value:d.title,onChange:j=>f("title",j.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Content"}),y.jsx("textarea",{value:d.content,onChange:j=>f("content",j.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Variant"}),y.jsxs("select",{value:d.variant,onChange:j=>f("variant",j.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[y.jsx("option",{value:"default",children:"Default"}),y.jsx("option",{value:"elevated",children:"Elevated"}),y.jsx("option",{value:"outlined",children:"Outlined"}),y.jsx("option",{value:"filled",children:"Filled"}),y.jsx("option",{value:"gradient",children:"Gradient"})]})]}),y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Shadow"}),y.jsxs("select",{value:d.shadow,onChange:j=>f("shadow",j.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[y.jsx("option",{value:"none",children:"None"}),y.jsx("option",{value:"sm",children:"Small"}),y.jsx("option",{value:"md",children:"Medium"}),y.jsx("option",{value:"lg",children:"Large"}),y.jsx("option",{value:"xl",children:"Extra Large"}),y.jsx("option",{value:"2xl",children:"2X Large"})]})]}),y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Animation"}),y.jsxs("select",{value:d.animation,onChange:j=>f("animation",j.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[y.jsx("option",{value:"fade-in",children:"Fade In"}),y.jsx("option",{value:"slide-in",children:"Slide In"}),y.jsx("option",{value:"bounce-in",children:"Bounce In"}),y.jsx("option",{value:"scale-in",children:"Scale In"}),y.jsx("option",{value:"none",children:"None"})]})]}),y.jsxs("div",{className:"flex items-center",children:[y.jsx("input",{type:"checkbox",id:"showImage",checked:d.showImage,onChange:j=>f("showImage",j.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),y.jsx("label",{htmlFor:"showImage",className:"ml-2 block text-sm text-gray-700",children:"Show Image"})]}),d.showImage&&y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Image URL"}),y.jsx("input",{type:"url",value:d.imageUrl,onChange:j=>f("imageUrl",j.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),S=()=>y.jsxs("div",{className:"space-y-4",children:[y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Message"}),y.jsx("input",{type:"text",value:d.message,onChange:j=>f("message",j.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Type"}),y.jsxs("select",{value:d.type,onChange:j=>f("type",j.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[y.jsx("option",{value:"success",children:"Success"}),y.jsx("option",{value:"error",children:"Error"}),y.jsx("option",{value:"warning",children:"Warning"}),y.jsx("option",{value:"info",children:"Info"})]})]}),y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Position"}),y.jsxs("select",{value:d.position,onChange:j=>f("position",j.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[y.jsx("option",{value:"top-left",children:"Top Left"}),y.jsx("option",{value:"top-center",children:"Top Center"}),y.jsx("option",{value:"top-right",children:"Top Right"}),y.jsx("option",{value:"bottom-left",children:"Bottom Left"}),y.jsx("option",{value:"bottom-center",children:"Bottom Center"}),y.jsx("option",{value:"bottom-right",children:"Bottom Right"})]})]}),y.jsxs("div",{children:[y.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Animation"}),y.jsxs("select",{value:d.animation,onChange:j=>f("animation",j.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[y.jsx("option",{value:"slide-in",children:"Slide In"}),y.jsx("option",{value:"fade-in",children:"Fade In"}),y.jsx("option",{value:"bounce-in",children:"Bounce In"}),y.jsx("option",{value:"scale-in",children:"Scale In"})]})]}),y.jsxs("div",{className:"flex items-center",children:[y.jsx("input",{type:"checkbox",id:"autoClose",checked:d.autoClose,onChange:j=>f("autoClose",j.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),y.jsx("label",{htmlFor:"autoClose",className:"ml-2 block text-sm text-gray-700",children:"Auto Close"})]}),d.autoClose&&y.jsxs("div",{children:[y.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Duration (ms): ",d.duration]}),y.jsx("input",{type:"range",min:"1000",max:"10000",step:"500",value:d.duration,onChange:j=>f("duration",parseInt(j.target.value)),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"})]})]}),V=()=>{switch(a){case"Button":return p();case"Modal":return g();case"Carousel":return v();case"Card":return x();case"Toast":return S();default:return y.jsx("div",{children:"No controls available"})}};return y.jsx("div",{className:"w-80 bg-white border-r border-gray-200 p-6 overflow-y-auto",children:y.jsxs("div",{className:"mb-6",children:[y.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Component Builder"}),y.jsxs("div",{className:"mb-6",children:[y.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Component"}),y.jsxs("select",{value:a,onChange:j=>u(j.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[y.jsx("option",{value:"Button",children:"Button"}),y.jsx("option",{value:"Modal",children:"Modal"}),y.jsx("option",{value:"Carousel",children:"Carousel"}),y.jsx("option",{value:"Card",children:"Card"}),y.jsx("option",{value:"Toast",children:"Toast"})]})]}),y.jsxs("div",{className:"mb-6",children:[y.jsx("h3",{className:"text-md font-medium text-gray-800 mb-3",children:"Properties"}),V()]}),y.jsx("button",{onClick:m,className:"w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"Reset to Default"})]})})},lc=W.createContext({});function sc(a){const l=W.useRef(null);return l.current===null&&(l.current=a()),l.current}const uc=typeof window<"u",cg=uc?W.useLayoutEffect:W.useEffect,tu=W.createContext(null);function rc(a,l){a.indexOf(l)===-1&&a.push(l)}function oc(a,l){const u=a.indexOf(l);u>-1&&a.splice(u,1)}const fn=(a,l,u)=>u>l?l:u<a?a:u;let cc=()=>{};const dn={},fg=a=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(a);function dg(a){return typeof a=="object"&&a!==null}const hg=a=>/^0[^.\s]+$/u.test(a);function fc(a){let l;return()=>(l===void 0&&(l=a()),l)}const ze=a=>a,Ab=(a,l)=>u=>l(a(u)),Al=(...a)=>a.reduce(Ab),gl=(a,l,u)=>{const r=l-a;return r===0?1:(u-a)/r};class dc{constructor(){this.subscriptions=[]}add(l){return rc(this.subscriptions,l),()=>oc(this.subscriptions,l)}notify(l,u,r){const c=this.subscriptions.length;if(c)if(c===1)this.subscriptions[0](l,u,r);else for(let d=0;d<c;d++){const f=this.subscriptions[d];f&&f(l,u,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Qe=a=>a*1e3,Ke=a=>a/1e3;function mg(a,l){return l?a*(1e3/l):0}const pg=(a,l,u)=>(((1-3*u+3*l)*a+(3*u-6*l))*a+3*l)*a,Eb=1e-7,Mb=12;function jb(a,l,u,r,c){let d,f,m=0;do f=l+(u-l)/2,d=pg(f,r,c)-a,d>0?u=f:l=f;while(Math.abs(d)>Eb&&++m<Mb);return f}function El(a,l,u,r){if(a===l&&u===r)return ze;const c=d=>jb(d,0,1,a,u);return d=>d===0||d===1?d:pg(c(d),l,r)}const gg=a=>l=>l<=.5?a(2*l)/2:(2-a(2*(1-l)))/2,yg=a=>l=>1-a(1-l),vg=El(.33,1.53,.69,.99),hc=yg(vg),bg=gg(hc),xg=a=>(a*=2)<1?.5*hc(a):.5*(2-Math.pow(2,-10*(a-1))),mc=a=>1-Math.sin(Math.acos(a)),Sg=yg(mc),Tg=gg(mc),Db=El(.42,0,1,1),Cb=El(0,0,.58,1),Ag=El(.42,0,.58,1),wb=a=>Array.isArray(a)&&typeof a[0]!="number",Eg=a=>Array.isArray(a)&&typeof a[0]=="number",Rb={linear:ze,easeIn:Db,easeInOut:Ag,easeOut:Cb,circIn:mc,circInOut:Tg,circOut:Sg,backIn:hc,backInOut:bg,backOut:vg,anticipate:xg},Ob=a=>typeof a=="string",Fm=a=>{if(Eg(a)){cc(a.length===4);const[l,u,r,c]=a;return El(l,u,r,c)}else if(Ob(a))return Rb[a];return a},Ys=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],Pm={value:null};function Nb(a,l){let u=new Set,r=new Set,c=!1,d=!1;const f=new WeakSet;let m={delta:0,timestamp:0,isProcessing:!1},p=0;function g(x){f.has(x)&&(v.schedule(x),a()),p++,x(m)}const v={schedule:(x,S=!1,V=!1)=>{const Y=V&&c?u:r;return S&&f.add(x),Y.has(x)||Y.add(x),x},cancel:x=>{r.delete(x),f.delete(x)},process:x=>{if(m=x,c){d=!0;return}c=!0,[u,r]=[r,u],u.forEach(g),l&&Pm.value&&Pm.value.frameloop[l].push(p),p=0,u.clear(),c=!1,d&&(d=!1,v.process(x))}};return v}const zb=40;function Mg(a,l){let u=!1,r=!0;const c={delta:0,timestamp:0,isProcessing:!1},d=()=>u=!0,f=Ys.reduce((M,_)=>(M[_]=Nb(d,l?_:void 0),M),{}),{setup:m,read:p,resolveKeyframes:g,preUpdate:v,update:x,preRender:S,render:V,postRender:j}=f,Y=()=>{const M=dn.useManualTiming?c.timestamp:performance.now();u=!1,dn.useManualTiming||(c.delta=r?1e3/60:Math.max(Math.min(M-c.timestamp,zb),1)),c.timestamp=M,c.isProcessing=!0,m.process(c),p.process(c),g.process(c),v.process(c),x.process(c),S.process(c),V.process(c),j.process(c),c.isProcessing=!1,u&&l&&(r=!1,a(Y))},X=()=>{u=!0,r=!0,c.isProcessing||a(Y)};return{schedule:Ys.reduce((M,_)=>{const O=f[_];return M[_]=(B,Z=!1,K=!1)=>(u||X(),O.schedule(B,Z,K)),M},{}),cancel:M=>{for(let _=0;_<Ys.length;_++)f[Ys[_]].cancel(M)},state:c,steps:f}}const{schedule:wt,cancel:Ln,state:Wt,steps:Ao}=Mg(typeof requestAnimationFrame<"u"?requestAnimationFrame:ze,!0);let Qs;function Vb(){Qs=void 0}const ce={now:()=>(Qs===void 0&&ce.set(Wt.isProcessing||dn.useManualTiming?Wt.timestamp:performance.now()),Qs),set:a=>{Qs=a,queueMicrotask(Vb)}},jg=a=>l=>typeof l=="string"&&l.startsWith(a),pc=jg("--"),_b=jg("var(--"),gc=a=>_b(a)?Bb.test(a.split("/*")[0].trim()):!1,Bb=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,si={test:a=>typeof a=="number",parse:parseFloat,transform:a=>a},yl={...si,transform:a=>fn(0,1,a)},Gs={...si,default:1},fl=a=>Math.round(a*1e5)/1e5,yc=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function Ub(a){return a==null}const Lb=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,vc=(a,l)=>u=>!!(typeof u=="string"&&Lb.test(u)&&u.startsWith(a)||l&&!Ub(u)&&Object.prototype.hasOwnProperty.call(u,l)),Dg=(a,l,u)=>r=>{if(typeof r!="string")return r;const[c,d,f,m]=r.match(yc);return{[a]:parseFloat(c),[l]:parseFloat(d),[u]:parseFloat(f),alpha:m!==void 0?parseFloat(m):1}},Hb=a=>fn(0,255,a),Eo={...si,transform:a=>Math.round(Hb(a))},oa={test:vc("rgb","red"),parse:Dg("red","green","blue"),transform:({red:a,green:l,blue:u,alpha:r=1})=>"rgba("+Eo.transform(a)+", "+Eo.transform(l)+", "+Eo.transform(u)+", "+fl(yl.transform(r))+")"};function qb(a){let l="",u="",r="",c="";return a.length>5?(l=a.substring(1,3),u=a.substring(3,5),r=a.substring(5,7),c=a.substring(7,9)):(l=a.substring(1,2),u=a.substring(2,3),r=a.substring(3,4),c=a.substring(4,5),l+=l,u+=u,r+=r,c+=c),{red:parseInt(l,16),green:parseInt(u,16),blue:parseInt(r,16),alpha:c?parseInt(c,16)/255:1}}const Uo={test:vc("#"),parse:qb,transform:oa.transform},Ml=a=>({test:l=>typeof l=="string"&&l.endsWith(a)&&l.split(" ").length===1,parse:parseFloat,transform:l=>`${l}${a}`}),Un=Ml("deg"),ke=Ml("%"),st=Ml("px"),Yb=Ml("vh"),Gb=Ml("vw"),Jm={...ke,parse:a=>ke.parse(a)/100,transform:a=>ke.transform(a*100)},Wa={test:vc("hsl","hue"),parse:Dg("hue","saturation","lightness"),transform:({hue:a,saturation:l,lightness:u,alpha:r=1})=>"hsla("+Math.round(a)+", "+ke.transform(fl(l))+", "+ke.transform(fl(u))+", "+fl(yl.transform(r))+")"},qt={test:a=>oa.test(a)||Uo.test(a)||Wa.test(a),parse:a=>oa.test(a)?oa.parse(a):Wa.test(a)?Wa.parse(a):Uo.parse(a),transform:a=>typeof a=="string"?a:a.hasOwnProperty("red")?oa.transform(a):Wa.transform(a),getAnimatableNone:a=>{const l=qt.parse(a);return l.alpha=0,qt.transform(l)}},Xb=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function Zb(a){return isNaN(a)&&typeof a=="string"&&(a.match(yc)?.length||0)+(a.match(Xb)?.length||0)>0}const Cg="number",wg="color",Qb="var",Kb="var(",$m="${}",kb=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function vl(a){const l=a.toString(),u=[],r={color:[],number:[],var:[]},c=[];let d=0;const m=l.replace(kb,p=>(qt.test(p)?(r.color.push(d),c.push(wg),u.push(qt.parse(p))):p.startsWith(Kb)?(r.var.push(d),c.push(Qb),u.push(p)):(r.number.push(d),c.push(Cg),u.push(parseFloat(p))),++d,$m)).split($m);return{values:u,split:m,indexes:r,types:c}}function Rg(a){return vl(a).values}function Og(a){const{split:l,types:u}=vl(a),r=l.length;return c=>{let d="";for(let f=0;f<r;f++)if(d+=l[f],c[f]!==void 0){const m=u[f];m===Cg?d+=fl(c[f]):m===wg?d+=qt.transform(c[f]):d+=c[f]}return d}}const Fb=a=>typeof a=="number"?0:qt.test(a)?qt.getAnimatableNone(a):a;function Pb(a){const l=Rg(a);return Og(a)(l.map(Fb))}const Hn={test:Zb,parse:Rg,createTransformer:Og,getAnimatableNone:Pb};function Mo(a,l,u){return u<0&&(u+=1),u>1&&(u-=1),u<1/6?a+(l-a)*6*u:u<1/2?l:u<2/3?a+(l-a)*(2/3-u)*6:a}function Jb({hue:a,saturation:l,lightness:u,alpha:r}){a/=360,l/=100,u/=100;let c=0,d=0,f=0;if(!l)c=d=f=u;else{const m=u<.5?u*(1+l):u+l-u*l,p=2*u-m;c=Mo(p,m,a+1/3),d=Mo(p,m,a),f=Mo(p,m,a-1/3)}return{red:Math.round(c*255),green:Math.round(d*255),blue:Math.round(f*255),alpha:r}}function Ps(a,l){return u=>u>0?l:a}const Ot=(a,l,u)=>a+(l-a)*u,jo=(a,l,u)=>{const r=a*a,c=u*(l*l-r)+r;return c<0?0:Math.sqrt(c)},$b=[Uo,oa,Wa],Wb=a=>$b.find(l=>l.test(a));function Wm(a){const l=Wb(a);if(!l)return!1;let u=l.parse(a);return l===Wa&&(u=Jb(u)),u}const Im=(a,l)=>{const u=Wm(a),r=Wm(l);if(!u||!r)return Ps(a,l);const c={...u};return d=>(c.red=jo(u.red,r.red,d),c.green=jo(u.green,r.green,d),c.blue=jo(u.blue,r.blue,d),c.alpha=Ot(u.alpha,r.alpha,d),oa.transform(c))},Lo=new Set(["none","hidden"]);function Ib(a,l){return Lo.has(a)?u=>u<=0?a:l:u=>u>=1?l:a}function tx(a,l){return u=>Ot(a,l,u)}function bc(a){return typeof a=="number"?tx:typeof a=="string"?gc(a)?Ps:qt.test(a)?Im:ax:Array.isArray(a)?Ng:typeof a=="object"?qt.test(a)?Im:ex:Ps}function Ng(a,l){const u=[...a],r=u.length,c=a.map((d,f)=>bc(d)(d,l[f]));return d=>{for(let f=0;f<r;f++)u[f]=c[f](d);return u}}function ex(a,l){const u={...a,...l},r={};for(const c in u)a[c]!==void 0&&l[c]!==void 0&&(r[c]=bc(a[c])(a[c],l[c]));return c=>{for(const d in r)u[d]=r[d](c);return u}}function nx(a,l){const u=[],r={color:0,var:0,number:0};for(let c=0;c<l.values.length;c++){const d=l.types[c],f=a.indexes[d][r[d]],m=a.values[f]??0;u[c]=m,r[d]++}return u}const ax=(a,l)=>{const u=Hn.createTransformer(l),r=vl(a),c=vl(l);return r.indexes.var.length===c.indexes.var.length&&r.indexes.color.length===c.indexes.color.length&&r.indexes.number.length>=c.indexes.number.length?Lo.has(a)&&!c.values.length||Lo.has(l)&&!r.values.length?Ib(a,l):Al(Ng(nx(r,c),c.values),u):Ps(a,l)};function zg(a,l,u){return typeof a=="number"&&typeof l=="number"&&typeof u=="number"?Ot(a,l,u):bc(a)(a,l)}const ix=a=>{const l=({timestamp:u})=>a(u);return{start:(u=!0)=>wt.update(l,u),stop:()=>Ln(l),now:()=>Wt.isProcessing?Wt.timestamp:ce.now()}},Vg=(a,l,u=10)=>{let r="";const c=Math.max(Math.round(l/u),2);for(let d=0;d<c;d++)r+=Math.round(a(d/(c-1))*1e4)/1e4+", ";return`linear(${r.substring(0,r.length-2)})`},Js=2e4;function xc(a){let l=0;const u=50;let r=a.next(l);for(;!r.done&&l<Js;)l+=u,r=a.next(l);return l>=Js?1/0:l}function lx(a,l=100,u){const r=u({...a,keyframes:[0,l]}),c=Math.min(xc(r),Js);return{type:"keyframes",ease:d=>r.next(c*d).value/l,duration:Ke(c)}}const sx=5;function _g(a,l,u){const r=Math.max(l-sx,0);return mg(u-a(r),l-r)}const Vt={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},Do=.001;function ux({duration:a=Vt.duration,bounce:l=Vt.bounce,velocity:u=Vt.velocity,mass:r=Vt.mass}){let c,d,f=1-l;f=fn(Vt.minDamping,Vt.maxDamping,f),a=fn(Vt.minDuration,Vt.maxDuration,Ke(a)),f<1?(c=g=>{const v=g*f,x=v*a,S=v-u,V=Ho(g,f),j=Math.exp(-x);return Do-S/V*j},d=g=>{const x=g*f*a,S=x*u+u,V=Math.pow(f,2)*Math.pow(g,2)*a,j=Math.exp(-x),Y=Ho(Math.pow(g,2),f);return(-c(g)+Do>0?-1:1)*((S-V)*j)/Y}):(c=g=>{const v=Math.exp(-g*a),x=(g-u)*a+1;return-Do+v*x},d=g=>{const v=Math.exp(-g*a),x=(u-g)*(a*a);return v*x});const m=5/a,p=ox(c,d,m);if(a=Qe(a),isNaN(p))return{stiffness:Vt.stiffness,damping:Vt.damping,duration:a};{const g=Math.pow(p,2)*r;return{stiffness:g,damping:f*2*Math.sqrt(r*g),duration:a}}}const rx=12;function ox(a,l,u){let r=u;for(let c=1;c<rx;c++)r=r-a(r)/l(r);return r}function Ho(a,l){return a*Math.sqrt(1-l*l)}const cx=["duration","bounce"],fx=["stiffness","damping","mass"];function tp(a,l){return l.some(u=>a[u]!==void 0)}function dx(a){let l={velocity:Vt.velocity,stiffness:Vt.stiffness,damping:Vt.damping,mass:Vt.mass,isResolvedFromDuration:!1,...a};if(!tp(a,fx)&&tp(a,cx))if(a.visualDuration){const u=a.visualDuration,r=2*Math.PI/(u*1.2),c=r*r,d=2*fn(.05,1,1-(a.bounce||0))*Math.sqrt(c);l={...l,mass:Vt.mass,stiffness:c,damping:d}}else{const u=ux(a);l={...l,...u,mass:Vt.mass},l.isResolvedFromDuration=!0}return l}function $s(a=Vt.visualDuration,l=Vt.bounce){const u=typeof a!="object"?{visualDuration:a,keyframes:[0,1],bounce:l}:a;let{restSpeed:r,restDelta:c}=u;const d=u.keyframes[0],f=u.keyframes[u.keyframes.length-1],m={done:!1,value:d},{stiffness:p,damping:g,mass:v,duration:x,velocity:S,isResolvedFromDuration:V}=dx({...u,velocity:-Ke(u.velocity||0)}),j=S||0,Y=g/(2*Math.sqrt(p*v)),X=f-d,Q=Ke(Math.sqrt(p/v)),C=Math.abs(X)<5;r||(r=C?Vt.restSpeed.granular:Vt.restSpeed.default),c||(c=C?Vt.restDelta.granular:Vt.restDelta.default);let M;if(Y<1){const O=Ho(Q,Y);M=B=>{const Z=Math.exp(-Y*Q*B);return f-Z*((j+Y*Q*X)/O*Math.sin(O*B)+X*Math.cos(O*B))}}else if(Y===1)M=O=>f-Math.exp(-Q*O)*(X+(j+Q*X)*O);else{const O=Q*Math.sqrt(Y*Y-1);M=B=>{const Z=Math.exp(-Y*Q*B),K=Math.min(O*B,300);return f-Z*((j+Y*Q*X)*Math.sinh(K)+O*X*Math.cosh(K))/O}}const _={calculatedDuration:V&&x||null,next:O=>{const B=M(O);if(V)m.done=O>=x;else{let Z=O===0?j:0;Y<1&&(Z=O===0?Qe(j):_g(M,O,B));const K=Math.abs(Z)<=r,F=Math.abs(f-B)<=c;m.done=K&&F}return m.value=m.done?f:B,m},toString:()=>{const O=Math.min(xc(_),Js),B=Vg(Z=>_.next(O*Z).value,O,30);return O+"ms "+B},toTransition:()=>{}};return _}$s.applyToOptions=a=>{const l=lx(a,100,$s);return a.ease=l.ease,a.duration=Qe(l.duration),a.type="keyframes",a};function qo({keyframes:a,velocity:l=0,power:u=.8,timeConstant:r=325,bounceDamping:c=10,bounceStiffness:d=500,modifyTarget:f,min:m,max:p,restDelta:g=.5,restSpeed:v}){const x=a[0],S={done:!1,value:x},V=K=>m!==void 0&&K<m||p!==void 0&&K>p,j=K=>m===void 0?p:p===void 0||Math.abs(m-K)<Math.abs(p-K)?m:p;let Y=u*l;const X=x+Y,Q=f===void 0?X:f(X);Q!==X&&(Y=Q-x);const C=K=>-Y*Math.exp(-K/r),M=K=>Q+C(K),_=K=>{const F=C(K),ut=M(K);S.done=Math.abs(F)<=g,S.value=S.done?Q:ut};let O,B;const Z=K=>{V(S.value)&&(O=K,B=$s({keyframes:[S.value,j(S.value)],velocity:_g(M,K,S.value),damping:c,stiffness:d,restDelta:g,restSpeed:v}))};return Z(0),{calculatedDuration:null,next:K=>{let F=!1;return!B&&O===void 0&&(F=!0,_(K),Z(K)),O!==void 0&&K>=O?B.next(K-O):(!F&&_(K),S)}}}function hx(a,l,u){const r=[],c=u||dn.mix||zg,d=a.length-1;for(let f=0;f<d;f++){let m=c(a[f],a[f+1]);if(l){const p=Array.isArray(l)?l[f]||ze:l;m=Al(p,m)}r.push(m)}return r}function mx(a,l,{clamp:u=!0,ease:r,mixer:c}={}){const d=a.length;if(cc(d===l.length),d===1)return()=>l[0];if(d===2&&l[0]===l[1])return()=>l[1];const f=a[0]===a[1];a[0]>a[d-1]&&(a=[...a].reverse(),l=[...l].reverse());const m=hx(l,r,c),p=m.length,g=v=>{if(f&&v<a[0])return l[0];let x=0;if(p>1)for(;x<a.length-2&&!(v<a[x+1]);x++);const S=gl(a[x],a[x+1],v);return m[x](S)};return u?v=>g(fn(a[0],a[d-1],v)):g}function px(a,l){const u=a[a.length-1];for(let r=1;r<=l;r++){const c=gl(0,l,r);a.push(Ot(u,1,c))}}function gx(a){const l=[0];return px(l,a.length-1),l}function yx(a,l){return a.map(u=>u*l)}function vx(a,l){return a.map(()=>l||Ag).splice(0,a.length-1)}function dl({duration:a=300,keyframes:l,times:u,ease:r="easeInOut"}){const c=wb(r)?r.map(Fm):Fm(r),d={done:!1,value:l[0]},f=yx(u&&u.length===l.length?u:gx(l),a),m=mx(f,l,{ease:Array.isArray(c)?c:vx(l,c)});return{calculatedDuration:a,next:p=>(d.value=m(p),d.done=p>=a,d)}}const bx=a=>a!==null;function Sc(a,{repeat:l,repeatType:u="loop"},r,c=1){const d=a.filter(bx),m=c<0||l&&u!=="loop"&&l%2===1?0:d.length-1;return!m||r===void 0?d[m]:r}const xx={decay:qo,inertia:qo,tween:dl,keyframes:dl,spring:$s};function Bg(a){typeof a.type=="string"&&(a.type=xx[a.type])}class Tc{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(l=>{this.resolve=l})}notifyFinished(){this.resolve()}then(l,u){return this.finished.then(l,u)}}const Sx=a=>a/100;class Ac extends Tc{constructor(l){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:u}=this.options;u&&u.updatedAt!==ce.now()&&this.tick(ce.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),this.options.onStop?.())},this.options=l,this.initAnimation(),this.play(),l.autoplay===!1&&this.pause()}initAnimation(){const{options:l}=this;Bg(l);const{type:u=dl,repeat:r=0,repeatDelay:c=0,repeatType:d,velocity:f=0}=l;let{keyframes:m}=l;const p=u||dl;p!==dl&&typeof m[0]!="number"&&(this.mixKeyframes=Al(Sx,zg(m[0],m[1])),m=[0,100]);const g=p({...l,keyframes:m});d==="mirror"&&(this.mirroredGenerator=p({...l,keyframes:[...m].reverse(),velocity:-f})),g.calculatedDuration===null&&(g.calculatedDuration=xc(g));const{calculatedDuration:v}=g;this.calculatedDuration=v,this.resolvedDuration=v+c,this.totalDuration=this.resolvedDuration*(r+1)-c,this.generator=g}updateTime(l){const u=Math.round(l-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=u}tick(l,u=!1){const{generator:r,totalDuration:c,mixKeyframes:d,mirroredGenerator:f,resolvedDuration:m,calculatedDuration:p}=this;if(this.startTime===null)return r.next(0);const{delay:g=0,keyframes:v,repeat:x,repeatType:S,repeatDelay:V,type:j,onUpdate:Y,finalKeyframe:X}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,l):this.speed<0&&(this.startTime=Math.min(l-c/this.speed,this.startTime)),u?this.currentTime=l:this.updateTime(l);const Q=this.currentTime-g*(this.playbackSpeed>=0?1:-1),C=this.playbackSpeed>=0?Q<0:Q>c;this.currentTime=Math.max(Q,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=c);let M=this.currentTime,_=r;if(x){const K=Math.min(this.currentTime,c)/m;let F=Math.floor(K),ut=K%1;!ut&&K>=1&&(ut=1),ut===1&&F--,F=Math.min(F,x+1),!!(F%2)&&(S==="reverse"?(ut=1-ut,V&&(ut-=V/m)):S==="mirror"&&(_=f)),M=fn(0,1,ut)*m}const O=C?{done:!1,value:v[0]}:_.next(M);d&&(O.value=d(O.value));let{done:B}=O;!C&&p!==null&&(B=this.playbackSpeed>=0?this.currentTime>=c:this.currentTime<=0);const Z=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&B);return Z&&j!==qo&&(O.value=Sc(v,this.options,X,this.speed)),Y&&Y(O.value),Z&&this.finish(),O}then(l,u){return this.finished.then(l,u)}get duration(){return Ke(this.calculatedDuration)}get time(){return Ke(this.currentTime)}set time(l){l=Qe(l),this.currentTime=l,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=l:this.driver&&(this.startTime=this.driver.now()-l/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(l){this.updateTime(ce.now());const u=this.playbackSpeed!==l;this.playbackSpeed=l,u&&(this.time=Ke(this.currentTime))}play(){if(this.isStopped)return;const{driver:l=ix,startTime:u}=this.options;this.driver||(this.driver=l(c=>this.tick(c))),this.options.onPlay?.();const r=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=r):this.holdTime!==null?this.startTime=r-this.holdTime:this.startTime||(this.startTime=u??r),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(ce.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(l){return this.startTime=0,this.tick(l,!0)}attachTimeline(l){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),l.observe(this)}}function Tx(a){for(let l=1;l<a.length;l++)a[l]??(a[l]=a[l-1])}const ca=a=>a*180/Math.PI,Yo=a=>{const l=ca(Math.atan2(a[1],a[0]));return Go(l)},Ax={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:a=>(Math.abs(a[0])+Math.abs(a[3]))/2,rotate:Yo,rotateZ:Yo,skewX:a=>ca(Math.atan(a[1])),skewY:a=>ca(Math.atan(a[2])),skew:a=>(Math.abs(a[1])+Math.abs(a[2]))/2},Go=a=>(a=a%360,a<0&&(a+=360),a),ep=Yo,np=a=>Math.sqrt(a[0]*a[0]+a[1]*a[1]),ap=a=>Math.sqrt(a[4]*a[4]+a[5]*a[5]),Ex={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:np,scaleY:ap,scale:a=>(np(a)+ap(a))/2,rotateX:a=>Go(ca(Math.atan2(a[6],a[5]))),rotateY:a=>Go(ca(Math.atan2(-a[2],a[0]))),rotateZ:ep,rotate:ep,skewX:a=>ca(Math.atan(a[4])),skewY:a=>ca(Math.atan(a[1])),skew:a=>(Math.abs(a[1])+Math.abs(a[4]))/2};function Xo(a){return a.includes("scale")?1:0}function Zo(a,l){if(!a||a==="none")return Xo(l);const u=a.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let r,c;if(u)r=Ex,c=u;else{const m=a.match(/^matrix\(([-\d.e\s,]+)\)$/u);r=Ax,c=m}if(!c)return Xo(l);const d=r[l],f=c[1].split(",").map(jx);return typeof d=="function"?d(f):f[d]}const Mx=(a,l)=>{const{transform:u="none"}=getComputedStyle(a);return Zo(u,l)};function jx(a){return parseFloat(a.trim())}const ui=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],ri=new Set(ui),ip=a=>a===si||a===st,Dx=new Set(["x","y","z"]),Cx=ui.filter(a=>!Dx.has(a));function wx(a){const l=[];return Cx.forEach(u=>{const r=a.getValue(u);r!==void 0&&(l.push([u,r.get()]),r.set(u.startsWith("scale")?1:0))}),l}const fa={width:({x:a},{paddingLeft:l="0",paddingRight:u="0"})=>a.max-a.min-parseFloat(l)-parseFloat(u),height:({y:a},{paddingTop:l="0",paddingBottom:u="0"})=>a.max-a.min-parseFloat(l)-parseFloat(u),top:(a,{top:l})=>parseFloat(l),left:(a,{left:l})=>parseFloat(l),bottom:({y:a},{top:l})=>parseFloat(l)+(a.max-a.min),right:({x:a},{left:l})=>parseFloat(l)+(a.max-a.min),x:(a,{transform:l})=>Zo(l,"x"),y:(a,{transform:l})=>Zo(l,"y")};fa.translateX=fa.x;fa.translateY=fa.y;const da=new Set;let Qo=!1,Ko=!1,ko=!1;function Ug(){if(Ko){const a=Array.from(da).filter(r=>r.needsMeasurement),l=new Set(a.map(r=>r.element)),u=new Map;l.forEach(r=>{const c=wx(r);c.length&&(u.set(r,c),r.render())}),a.forEach(r=>r.measureInitialState()),l.forEach(r=>{r.render();const c=u.get(r);c&&c.forEach(([d,f])=>{r.getValue(d)?.set(f)})}),a.forEach(r=>r.measureEndState()),a.forEach(r=>{r.suspendedScrollY!==void 0&&window.scrollTo(0,r.suspendedScrollY)})}Ko=!1,Qo=!1,da.forEach(a=>a.complete(ko)),da.clear()}function Lg(){da.forEach(a=>{a.readKeyframes(),a.needsMeasurement&&(Ko=!0)})}function Rx(){ko=!0,Lg(),Ug(),ko=!1}class Ec{constructor(l,u,r,c,d,f=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...l],this.onComplete=u,this.name=r,this.motionValue=c,this.element=d,this.isAsync=f}scheduleResolve(){this.state="scheduled",this.isAsync?(da.add(this),Qo||(Qo=!0,wt.read(Lg),wt.resolveKeyframes(Ug))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:l,name:u,element:r,motionValue:c}=this;if(l[0]===null){const d=c?.get(),f=l[l.length-1];if(d!==void 0)l[0]=d;else if(r&&u){const m=r.readValue(u,f);m!=null&&(l[0]=m)}l[0]===void 0&&(l[0]=f),c&&d===void 0&&c.set(l[0])}Tx(l)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(l=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,l),da.delete(this)}cancel(){this.state==="scheduled"&&(da.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const Ox=a=>a.startsWith("--");function Nx(a,l,u){Ox(l)?a.style.setProperty(l,u):a.style[l]=u}const zx=fc(()=>window.ScrollTimeline!==void 0),Vx={};function _x(a,l){const u=fc(a);return()=>Vx[l]??u()}const Hg=_x(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),cl=([a,l,u,r])=>`cubic-bezier(${a}, ${l}, ${u}, ${r})`,lp={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:cl([0,.65,.55,1]),circOut:cl([.55,0,1,.45]),backIn:cl([.31,.01,.66,-.59]),backOut:cl([.33,1.53,.69,.99])};function qg(a,l){if(a)return typeof a=="function"?Hg()?Vg(a,l):"ease-out":Eg(a)?cl(a):Array.isArray(a)?a.map(u=>qg(u,l)||lp.easeOut):lp[a]}function Bx(a,l,u,{delay:r=0,duration:c=300,repeat:d=0,repeatType:f="loop",ease:m="easeOut",times:p}={},g=void 0){const v={[l]:u};p&&(v.offset=p);const x=qg(m,c);Array.isArray(x)&&(v.easing=x);const S={delay:r,duration:c,easing:Array.isArray(x)?"linear":x,fill:"both",iterations:d+1,direction:f==="reverse"?"alternate":"normal"};return g&&(S.pseudoElement=g),a.animate(v,S)}function Yg(a){return typeof a=="function"&&"applyToOptions"in a}function Ux({type:a,...l}){return Yg(a)&&Hg()?a.applyToOptions(l):(l.duration??(l.duration=300),l.ease??(l.ease="easeOut"),l)}class Lx extends Tc{constructor(l){if(super(),this.finishedTime=null,this.isStopped=!1,!l)return;const{element:u,name:r,keyframes:c,pseudoElement:d,allowFlatten:f=!1,finalKeyframe:m,onComplete:p}=l;this.isPseudoElement=!!d,this.allowFlatten=f,this.options=l,cc(typeof l.type!="string");const g=Ux(l);this.animation=Bx(u,r,c,g,d),g.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!d){const v=Sc(c,this.options,m,this.speed);this.updateMotionValue?this.updateMotionValue(v):Nx(u,r,v),this.animation.cancel()}p?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:l}=this;l==="idle"||l==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const l=this.animation.effect?.getComputedTiming?.().duration||0;return Ke(Number(l))}get time(){return Ke(Number(this.animation.currentTime)||0)}set time(l){this.finishedTime=null,this.animation.currentTime=Qe(l)}get speed(){return this.animation.playbackRate}set speed(l){l<0&&(this.finishedTime=null),this.animation.playbackRate=l}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(l){this.animation.startTime=l}attachTimeline({timeline:l,observe:u}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,l&&zx()?(this.animation.timeline=l,ze):u(this)}}const Gg={anticipate:xg,backInOut:bg,circInOut:Tg};function Hx(a){return a in Gg}function qx(a){typeof a.ease=="string"&&Hx(a.ease)&&(a.ease=Gg[a.ease])}const sp=10;class Yx extends Lx{constructor(l){qx(l),Bg(l),super(l),l.startTime&&(this.startTime=l.startTime),this.options=l}updateMotionValue(l){const{motionValue:u,onUpdate:r,onComplete:c,element:d,...f}=this.options;if(!u)return;if(l!==void 0){u.set(l);return}const m=new Ac({...f,autoplay:!1}),p=Qe(this.finishedTime??this.time);u.setWithVelocity(m.sample(p-sp).value,m.sample(p).value,sp),m.stop()}}const up=(a,l)=>l==="zIndex"?!1:!!(typeof a=="number"||Array.isArray(a)||typeof a=="string"&&(Hn.test(a)||a==="0")&&!a.startsWith("url("));function Gx(a){const l=a[0];if(a.length===1)return!0;for(let u=0;u<a.length;u++)if(a[u]!==l)return!0}function Xx(a,l,u,r){const c=a[0];if(c===null)return!1;if(l==="display"||l==="visibility")return!0;const d=a[a.length-1],f=up(c,l),m=up(d,l);return!f||!m?!1:Gx(a)||(u==="spring"||Yg(u))&&r}function Mc(a){return dg(a)&&"offsetHeight"in a}const Zx=new Set(["opacity","clipPath","filter","transform"]),Qx=fc(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function Kx(a){const{motionValue:l,name:u,repeatDelay:r,repeatType:c,damping:d,type:f}=a;if(!Mc(l?.owner?.current))return!1;const{onUpdate:m,transformTemplate:p}=l.owner.getProps();return Qx()&&u&&Zx.has(u)&&(u!=="transform"||!p)&&!m&&!r&&c!=="mirror"&&d!==0&&f!=="inertia"}const kx=40;class Fx extends Tc{constructor({autoplay:l=!0,delay:u=0,type:r="keyframes",repeat:c=0,repeatDelay:d=0,repeatType:f="loop",keyframes:m,name:p,motionValue:g,element:v,...x}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=ce.now();const S={autoplay:l,delay:u,type:r,repeat:c,repeatDelay:d,repeatType:f,name:p,motionValue:g,element:v,...x},V=v?.KeyframeResolver||Ec;this.keyframeResolver=new V(m,(j,Y,X)=>this.onKeyframesResolved(j,Y,S,!X),p,g,v),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(l,u,r,c){this.keyframeResolver=void 0;const{name:d,type:f,velocity:m,delay:p,isHandoff:g,onUpdate:v}=r;this.resolvedAt=ce.now(),Xx(l,d,f,m)||((dn.instantAnimations||!p)&&v?.(Sc(l,r,u)),l[0]=l[l.length-1],r.duration=0,r.repeat=0);const S={startTime:c?this.resolvedAt?this.resolvedAt-this.createdAt>kx?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:u,...r,keyframes:l},V=!g&&Kx(S)?new Yx({...S,element:S.motionValue.owner.current}):new Ac(S);V.finished.then(()=>this.notifyFinished()).catch(ze),this.pendingTimeline&&(this.stopTimeline=V.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=V}get finished(){return this._animation?this.animation.finished:this._finished}then(l,u){return this.finished.finally(l).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),Rx()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(l){this.animation.time=l}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(l){this.animation.speed=l}get startTime(){return this.animation.startTime}attachTimeline(l){return this._animation?this.stopTimeline=this.animation.attachTimeline(l):this.pendingTimeline=l,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}const Px=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Jx(a){const l=Px.exec(a);if(!l)return[,];const[,u,r,c]=l;return[`--${u??r}`,c]}function Xg(a,l,u=1){const[r,c]=Jx(a);if(!r)return;const d=window.getComputedStyle(l).getPropertyValue(r);if(d){const f=d.trim();return fg(f)?parseFloat(f):f}return gc(c)?Xg(c,l,u+1):c}function jc(a,l){return a?.[l]??a?.default??a}const Zg=new Set(["width","height","top","left","right","bottom",...ui]),$x={test:a=>a==="auto",parse:a=>a},Qg=a=>l=>l.test(a),Kg=[si,st,ke,Un,Gb,Yb,$x],rp=a=>Kg.find(Qg(a));function Wx(a){return typeof a=="number"?a===0:a!==null?a==="none"||a==="0"||hg(a):!0}const Ix=new Set(["brightness","contrast","saturate","opacity"]);function t1(a){const[l,u]=a.slice(0,-1).split("(");if(l==="drop-shadow")return a;const[r]=u.match(yc)||[];if(!r)return a;const c=u.replace(r,"");let d=Ix.has(l)?1:0;return r!==u&&(d*=100),l+"("+d+c+")"}const e1=/\b([a-z-]*)\(.*?\)/gu,Fo={...Hn,getAnimatableNone:a=>{const l=a.match(e1);return l?l.map(t1).join(" "):a}},op={...si,transform:Math.round},n1={rotate:Un,rotateX:Un,rotateY:Un,rotateZ:Un,scale:Gs,scaleX:Gs,scaleY:Gs,scaleZ:Gs,skew:Un,skewX:Un,skewY:Un,distance:st,translateX:st,translateY:st,translateZ:st,x:st,y:st,z:st,perspective:st,transformPerspective:st,opacity:yl,originX:Jm,originY:Jm,originZ:st},Dc={borderWidth:st,borderTopWidth:st,borderRightWidth:st,borderBottomWidth:st,borderLeftWidth:st,borderRadius:st,radius:st,borderTopLeftRadius:st,borderTopRightRadius:st,borderBottomRightRadius:st,borderBottomLeftRadius:st,width:st,maxWidth:st,height:st,maxHeight:st,top:st,right:st,bottom:st,left:st,padding:st,paddingTop:st,paddingRight:st,paddingBottom:st,paddingLeft:st,margin:st,marginTop:st,marginRight:st,marginBottom:st,marginLeft:st,backgroundPositionX:st,backgroundPositionY:st,...n1,zIndex:op,fillOpacity:yl,strokeOpacity:yl,numOctaves:op},a1={...Dc,color:qt,backgroundColor:qt,outlineColor:qt,fill:qt,stroke:qt,borderColor:qt,borderTopColor:qt,borderRightColor:qt,borderBottomColor:qt,borderLeftColor:qt,filter:Fo,WebkitFilter:Fo},kg=a=>a1[a];function Fg(a,l){let u=kg(a);return u!==Fo&&(u=Hn),u.getAnimatableNone?u.getAnimatableNone(l):void 0}const i1=new Set(["auto","none","0"]);function l1(a,l,u){let r=0,c;for(;r<a.length&&!c;){const d=a[r];typeof d=="string"&&!i1.has(d)&&vl(d).values.length&&(c=a[r]),r++}if(c&&u)for(const d of l)a[d]=Fg(u,c)}class s1 extends Ec{constructor(l,u,r,c,d){super(l,u,r,c,d,!0)}readKeyframes(){const{unresolvedKeyframes:l,element:u,name:r}=this;if(!u||!u.current)return;super.readKeyframes();for(let p=0;p<l.length;p++){let g=l[p];if(typeof g=="string"&&(g=g.trim(),gc(g))){const v=Xg(g,u.current);v!==void 0&&(l[p]=v),p===l.length-1&&(this.finalKeyframe=g)}}if(this.resolveNoneKeyframes(),!Zg.has(r)||l.length!==2)return;const[c,d]=l,f=rp(c),m=rp(d);if(f!==m)if(ip(f)&&ip(m))for(let p=0;p<l.length;p++){const g=l[p];typeof g=="string"&&(l[p]=parseFloat(g))}else fa[r]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:l,name:u}=this,r=[];for(let c=0;c<l.length;c++)(l[c]===null||Wx(l[c]))&&r.push(c);r.length&&l1(l,r,u)}measureInitialState(){const{element:l,unresolvedKeyframes:u,name:r}=this;if(!l||!l.current)return;r==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=fa[r](l.measureViewportBox(),window.getComputedStyle(l.current)),u[0]=this.measuredOrigin;const c=u[u.length-1];c!==void 0&&l.getValue(r,c).jump(c,!1)}measureEndState(){const{element:l,name:u,unresolvedKeyframes:r}=this;if(!l||!l.current)return;const c=l.getValue(u);c&&c.jump(this.measuredOrigin,!1);const d=r.length-1,f=r[d];r[d]=fa[u](l.measureViewportBox(),window.getComputedStyle(l.current)),f!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=f),this.removedTransforms?.length&&this.removedTransforms.forEach(([m,p])=>{l.getValue(m).set(p)}),this.resolveNoneKeyframes()}}function u1(a,l,u){if(a instanceof EventTarget)return[a];if(typeof a=="string"){let r=document;const c=u?.[a]??r.querySelectorAll(a);return c?Array.from(c):[]}return Array.from(a)}const Pg=(a,l)=>l&&typeof a=="number"?l.transform(a):a,cp=30,r1=a=>!isNaN(parseFloat(a));class o1{constructor(l,u={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(r,c=!0)=>{const d=ce.now();if(this.updatedAt!==d&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(r),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const f of this.dependents)f.dirty();c&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(l),this.owner=u.owner}setCurrent(l){this.current=l,this.updatedAt=ce.now(),this.canTrackVelocity===null&&l!==void 0&&(this.canTrackVelocity=r1(this.current))}setPrevFrameValue(l=this.current){this.prevFrameValue=l,this.prevUpdatedAt=this.updatedAt}onChange(l){return this.on("change",l)}on(l,u){this.events[l]||(this.events[l]=new dc);const r=this.events[l].add(u);return l==="change"?()=>{r(),wt.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const l in this.events)this.events[l].clear()}attach(l,u){this.passiveEffect=l,this.stopPassiveEffect=u}set(l,u=!0){!u||!this.passiveEffect?this.updateAndNotify(l,u):this.passiveEffect(l,this.updateAndNotify)}setWithVelocity(l,u,r){this.set(u),this.prev=void 0,this.prevFrameValue=l,this.prevUpdatedAt=this.updatedAt-r}jump(l,u=!0){this.updateAndNotify(l),this.prev=l,this.prevUpdatedAt=this.prevFrameValue=void 0,u&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(l){this.dependents||(this.dependents=new Set),this.dependents.add(l)}removeDependent(l){this.dependents&&this.dependents.delete(l)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const l=ce.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||l-this.updatedAt>cp)return 0;const u=Math.min(this.updatedAt-this.prevUpdatedAt,cp);return mg(parseFloat(this.current)-parseFloat(this.prevFrameValue),u)}start(l){return this.stop(),new Promise(u=>{this.hasAnimated=!0,this.animation=l(u),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ai(a,l){return new o1(a,l)}const{schedule:Cc}=Mg(queueMicrotask,!1),Le={x:!1,y:!1};function Jg(){return Le.x||Le.y}function c1(a){return a==="x"||a==="y"?Le[a]?null:(Le[a]=!0,()=>{Le[a]=!1}):Le.x||Le.y?null:(Le.x=Le.y=!0,()=>{Le.x=Le.y=!1})}function $g(a,l){const u=u1(a),r=new AbortController,c={passive:!0,...l,signal:r.signal};return[u,c,()=>r.abort()]}function fp(a){return!(a.pointerType==="touch"||Jg())}function f1(a,l,u={}){const[r,c,d]=$g(a,u),f=m=>{if(!fp(m))return;const{target:p}=m,g=l(p,m);if(typeof g!="function"||!p)return;const v=x=>{fp(x)&&(g(x),p.removeEventListener("pointerleave",v))};p.addEventListener("pointerleave",v,c)};return r.forEach(m=>{m.addEventListener("pointerenter",f,c)}),d}const Wg=(a,l)=>l?a===l?!0:Wg(a,l.parentElement):!1,wc=a=>a.pointerType==="mouse"?typeof a.button!="number"||a.button<=0:a.isPrimary!==!1,d1=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function h1(a){return d1.has(a.tagName)||a.tabIndex!==-1}const Ks=new WeakSet;function dp(a){return l=>{l.key==="Enter"&&a(l)}}function Co(a,l){a.dispatchEvent(new PointerEvent("pointer"+l,{isPrimary:!0,bubbles:!0}))}const m1=(a,l)=>{const u=a.currentTarget;if(!u)return;const r=dp(()=>{if(Ks.has(u))return;Co(u,"down");const c=dp(()=>{Co(u,"up")}),d=()=>Co(u,"cancel");u.addEventListener("keyup",c,l),u.addEventListener("blur",d,l)});u.addEventListener("keydown",r,l),u.addEventListener("blur",()=>u.removeEventListener("keydown",r),l)};function hp(a){return wc(a)&&!Jg()}function p1(a,l,u={}){const[r,c,d]=$g(a,u),f=m=>{const p=m.currentTarget;if(!hp(m))return;Ks.add(p);const g=l(p,m),v=(V,j)=>{window.removeEventListener("pointerup",x),window.removeEventListener("pointercancel",S),Ks.has(p)&&Ks.delete(p),hp(V)&&typeof g=="function"&&g(V,{success:j})},x=V=>{v(V,p===window||p===document||u.useGlobalTarget||Wg(p,V.target))},S=V=>{v(V,!1)};window.addEventListener("pointerup",x,c),window.addEventListener("pointercancel",S,c)};return r.forEach(m=>{(u.useGlobalTarget?window:m).addEventListener("pointerdown",f,c),Mc(m)&&(m.addEventListener("focus",g=>m1(g,c)),!h1(m)&&!m.hasAttribute("tabindex")&&(m.tabIndex=0))}),d}function Ig(a){return dg(a)&&"ownerSVGElement"in a}function g1(a){return Ig(a)&&a.tagName==="svg"}const ne=a=>!!(a&&a.getVelocity),y1=[...Kg,qt,Hn],v1=a=>y1.find(Qg(a)),Rc=W.createContext({transformPagePoint:a=>a,isStatic:!1,reducedMotion:"never"});class b1 extends W.Component{getSnapshotBeforeUpdate(l){const u=this.props.childRef.current;if(u&&l.isPresent&&!this.props.isPresent){const r=u.offsetParent,c=Mc(r)&&r.offsetWidth||0,d=this.props.sizeRef.current;d.height=u.offsetHeight||0,d.width=u.offsetWidth||0,d.top=u.offsetTop,d.left=u.offsetLeft,d.right=c-d.width-d.left}return null}componentDidUpdate(){}render(){return this.props.children}}function x1({children:a,isPresent:l,anchorX:u,root:r}){const c=W.useId(),d=W.useRef(null),f=W.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:m}=W.useContext(Rc);return W.useInsertionEffect(()=>{const{width:p,height:g,top:v,left:x,right:S}=f.current;if(l||!d.current||!p||!g)return;const V=u==="left"?`left: ${x}`:`right: ${S}`;d.current.dataset.motionPopId=c;const j=document.createElement("style");m&&(j.nonce=m);const Y=r??document.head;return Y.appendChild(j),j.sheet&&j.sheet.insertRule(`
          [data-motion-pop-id="${c}"] {
            position: absolute !important;
            width: ${p}px !important;
            height: ${g}px !important;
            ${V}px !important;
            top: ${v}px !important;
          }
        `),()=>{Y.removeChild(j),Y.contains(j)&&Y.removeChild(j)}},[l]),y.jsx(b1,{isPresent:l,childRef:d,sizeRef:f,children:W.cloneElement(a,{ref:d})})}const S1=({children:a,initial:l,isPresent:u,onExitComplete:r,custom:c,presenceAffectsLayout:d,mode:f,anchorX:m,root:p})=>{const g=sc(T1),v=W.useId();let x=!0,S=W.useMemo(()=>(x=!1,{id:v,initial:l,isPresent:u,custom:c,onExitComplete:V=>{g.set(V,!0);for(const j of g.values())if(!j)return;r&&r()},register:V=>(g.set(V,!1),()=>g.delete(V))}),[u,g,r]);return d&&x&&(S={...S}),W.useMemo(()=>{g.forEach((V,j)=>g.set(j,!1))},[u]),W.useEffect(()=>{!u&&!g.size&&r&&r()},[u]),f==="popLayout"&&(a=y.jsx(x1,{isPresent:u,anchorX:m,root:p,children:a})),y.jsx(tu.Provider,{value:S,children:a})};function T1(){return new Map}function t0(a=!0){const l=W.useContext(tu);if(l===null)return[!0,null];const{isPresent:u,onExitComplete:r,register:c}=l,d=W.useId();W.useEffect(()=>{if(a)return c(d)},[a]);const f=W.useCallback(()=>a&&r&&r(d),[d,r,a]);return!u&&r?[!1,f]:[!0]}const Xs=a=>a.key||"";function mp(a){const l=[];return W.Children.forEach(a,u=>{W.isValidElement(u)&&l.push(u)}),l}const Oc=({children:a,custom:l,initial:u=!0,onExitComplete:r,presenceAffectsLayout:c=!0,mode:d="sync",propagate:f=!1,anchorX:m="left",root:p})=>{const[g,v]=t0(f),x=W.useMemo(()=>mp(a),[a]),S=f&&!g?[]:x.map(Xs),V=W.useRef(!0),j=W.useRef(x),Y=sc(()=>new Map),[X,Q]=W.useState(x),[C,M]=W.useState(x);cg(()=>{V.current=!1,j.current=x;for(let B=0;B<C.length;B++){const Z=Xs(C[B]);S.includes(Z)?Y.delete(Z):Y.get(Z)!==!0&&Y.set(Z,!1)}},[C,S.length,S.join("-")]);const _=[];if(x!==X){let B=[...x];for(let Z=0;Z<C.length;Z++){const K=C[Z],F=Xs(K);S.includes(F)||(B.splice(Z,0,K),_.push(K))}return d==="wait"&&_.length&&(B=_),M(mp(B)),Q(x),null}const{forceRender:O}=W.useContext(lc);return y.jsx(y.Fragment,{children:C.map(B=>{const Z=Xs(B),K=f&&!g?!1:x===C||S.includes(Z),F=()=>{if(Y.has(Z))Y.set(Z,!0);else return;let ut=!0;Y.forEach(pt=>{pt||(ut=!1)}),ut&&(O?.(),M(j.current),f&&v?.(),r&&r())};return y.jsx(S1,{isPresent:K,initial:!V.current||u?void 0:!1,custom:l,presenceAffectsLayout:c,mode:d,root:p,onExitComplete:K?void 0:F,anchorX:m,children:B},Z)})})},e0=W.createContext({strict:!1}),pp={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},ii={};for(const a in pp)ii[a]={isEnabled:l=>pp[a].some(u=>!!l[u])};function A1(a){for(const l in a)ii[l]={...ii[l],...a[l]}}const E1=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Ws(a){return a.startsWith("while")||a.startsWith("drag")&&a!=="draggable"||a.startsWith("layout")||a.startsWith("onTap")||a.startsWith("onPan")||a.startsWith("onLayout")||E1.has(a)}let n0=a=>!Ws(a);function M1(a){typeof a=="function"&&(n0=l=>l.startsWith("on")?!Ws(l):a(l))}try{M1(require("@emotion/is-prop-valid").default)}catch{}function j1(a,l,u){const r={};for(const c in a)c==="values"&&typeof a.values=="object"||(n0(c)||u===!0&&Ws(c)||!l&&!Ws(c)||a.draggable&&c.startsWith("onDrag"))&&(r[c]=a[c]);return r}function D1(a){if(typeof Proxy>"u")return a;const l=new Map,u=(...r)=>a(...r);return new Proxy(u,{get:(r,c)=>c==="create"?a:(l.has(c)||l.set(c,a(c)),l.get(c))})}const eu=W.createContext({});function nu(a){return a!==null&&typeof a=="object"&&typeof a.start=="function"}function bl(a){return typeof a=="string"||Array.isArray(a)}const Nc=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],zc=["initial",...Nc];function au(a){return nu(a.animate)||zc.some(l=>bl(a[l]))}function a0(a){return!!(au(a)||a.variants)}function C1(a,l){if(au(a)){const{initial:u,animate:r}=a;return{initial:u===!1||bl(u)?u:void 0,animate:bl(r)?r:void 0}}return a.inherit!==!1?l:{}}function w1(a){const{initial:l,animate:u}=C1(a,W.useContext(eu));return W.useMemo(()=>({initial:l,animate:u}),[gp(l),gp(u)])}function gp(a){return Array.isArray(a)?a.join(" "):a}const R1=Symbol.for("motionComponentSymbol");function Ia(a){return a&&typeof a=="object"&&Object.prototype.hasOwnProperty.call(a,"current")}function O1(a,l,u){return W.useCallback(r=>{r&&a.onMount&&a.onMount(r),l&&(r?l.mount(r):l.unmount()),u&&(typeof u=="function"?u(r):Ia(u)&&(u.current=r))},[l])}const Vc=a=>a.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),N1="framerAppearId",i0="data-"+Vc(N1),l0=W.createContext({});function z1(a,l,u,r,c){const{visualElement:d}=W.useContext(eu),f=W.useContext(e0),m=W.useContext(tu),p=W.useContext(Rc).reducedMotion,g=W.useRef(null);r=r||f.renderer,!g.current&&r&&(g.current=r(a,{visualState:l,parent:d,props:u,presenceContext:m,blockInitialAnimation:m?m.initial===!1:!1,reducedMotionConfig:p}));const v=g.current,x=W.useContext(l0);v&&!v.projection&&c&&(v.type==="html"||v.type==="svg")&&V1(g.current,u,c,x);const S=W.useRef(!1);W.useInsertionEffect(()=>{v&&S.current&&v.update(u,m)});const V=u[i0],j=W.useRef(!!V&&!window.MotionHandoffIsComplete?.(V)&&window.MotionHasOptimisedAnimation?.(V));return cg(()=>{v&&(S.current=!0,window.MotionIsMounted=!0,v.updateFeatures(),Cc.render(v.render),j.current&&v.animationState&&v.animationState.animateChanges())}),W.useEffect(()=>{v&&(!j.current&&v.animationState&&v.animationState.animateChanges(),j.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(V)}),j.current=!1))}),v}function V1(a,l,u,r){const{layoutId:c,layout:d,drag:f,dragConstraints:m,layoutScroll:p,layoutRoot:g,layoutCrossfade:v}=l;a.projection=new u(a.latestValues,l["data-framer-portal-id"]?void 0:s0(a.parent)),a.projection.setOptions({layoutId:c,layout:d,alwaysMeasureLayout:!!f||m&&Ia(m),visualElement:a,animationType:typeof d=="string"?d:"both",initialPromotionConfig:r,crossfade:v,layoutScroll:p,layoutRoot:g})}function s0(a){if(a)return a.options.allowProjection!==!1?a.projection:s0(a.parent)}function _1({preloadedFeatures:a,createVisualElement:l,useRender:u,useVisualState:r,Component:c}){a&&A1(a);function d(m,p){let g;const v={...W.useContext(Rc),...m,layoutId:B1(m)},{isStatic:x}=v,S=w1(m),V=r(m,x);if(!x&&uc){U1();const j=L1(v);g=j.MeasureLayout,S.visualElement=z1(c,V,v,l,j.ProjectionNode)}return y.jsxs(eu.Provider,{value:S,children:[g&&S.visualElement?y.jsx(g,{visualElement:S.visualElement,...v}):null,u(c,m,O1(V,S.visualElement,p),V,x,S.visualElement)]})}d.displayName=`motion.${typeof c=="string"?c:`create(${c.displayName??c.name??""})`}`;const f=W.forwardRef(d);return f[R1]=c,f}function B1({layoutId:a}){const l=W.useContext(lc).id;return l&&a!==void 0?l+"-"+a:a}function U1(a,l){W.useContext(e0).strict}function L1(a){const{drag:l,layout:u}=ii;if(!l&&!u)return{};const r={...l,...u};return{MeasureLayout:l?.isEnabled(a)||u?.isEnabled(a)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}const xl={};function H1(a){for(const l in a)xl[l]=a[l],pc(l)&&(xl[l].isCSSVariable=!0)}function u0(a,{layout:l,layoutId:u}){return ri.has(a)||a.startsWith("origin")||(l||u!==void 0)&&(!!xl[a]||a==="opacity")}const q1={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Y1=ui.length;function G1(a,l,u){let r="",c=!0;for(let d=0;d<Y1;d++){const f=ui[d],m=a[f];if(m===void 0)continue;let p=!0;if(typeof m=="number"?p=m===(f.startsWith("scale")?1:0):p=parseFloat(m)===0,!p||u){const g=Pg(m,Dc[f]);if(!p){c=!1;const v=q1[f]||f;r+=`${v}(${g}) `}u&&(l[f]=g)}}return r=r.trim(),u?r=u(l,c?"":r):c&&(r="none"),r}function _c(a,l,u){const{style:r,vars:c,transformOrigin:d}=a;let f=!1,m=!1;for(const p in l){const g=l[p];if(ri.has(p)){f=!0;continue}else if(pc(p)){c[p]=g;continue}else{const v=Pg(g,Dc[p]);p.startsWith("origin")?(m=!0,d[p]=v):r[p]=v}}if(l.transform||(f||u?r.transform=G1(l,a.transform,u):r.transform&&(r.transform="none")),m){const{originX:p="50%",originY:g="50%",originZ:v=0}=d;r.transformOrigin=`${p} ${g} ${v}`}}const Bc=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function r0(a,l,u){for(const r in l)!ne(l[r])&&!u0(r,u)&&(a[r]=l[r])}function X1({transformTemplate:a},l){return W.useMemo(()=>{const u=Bc();return _c(u,l,a),Object.assign({},u.vars,u.style)},[l])}function Z1(a,l){const u=a.style||{},r={};return r0(r,u,a),Object.assign(r,X1(a,l)),r}function Q1(a,l){const u={},r=Z1(a,l);return a.drag&&a.dragListener!==!1&&(u.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=a.drag===!0?"none":`pan-${a.drag==="x"?"y":"x"}`),a.tabIndex===void 0&&(a.onTap||a.onTapStart||a.whileTap)&&(u.tabIndex=0),u.style=r,u}const K1={offset:"stroke-dashoffset",array:"stroke-dasharray"},k1={offset:"strokeDashoffset",array:"strokeDasharray"};function F1(a,l,u=1,r=0,c=!0){a.pathLength=1;const d=c?K1:k1;a[d.offset]=st.transform(-r);const f=st.transform(l),m=st.transform(u);a[d.array]=`${f} ${m}`}function o0(a,{attrX:l,attrY:u,attrScale:r,pathLength:c,pathSpacing:d=1,pathOffset:f=0,...m},p,g,v){if(_c(a,m,g),p){a.style.viewBox&&(a.attrs.viewBox=a.style.viewBox);return}a.attrs=a.style,a.style={};const{attrs:x,style:S}=a;x.transform&&(S.transform=x.transform,delete x.transform),(S.transform||x.transformOrigin)&&(S.transformOrigin=x.transformOrigin??"50% 50%",delete x.transformOrigin),S.transform&&(S.transformBox=v?.transformBox??"fill-box",delete x.transformBox),l!==void 0&&(x.x=l),u!==void 0&&(x.y=u),r!==void 0&&(x.scale=r),c!==void 0&&F1(x,c,d,f,!1)}const c0=()=>({...Bc(),attrs:{}}),f0=a=>typeof a=="string"&&a.toLowerCase()==="svg";function P1(a,l,u,r){const c=W.useMemo(()=>{const d=c0();return o0(d,l,f0(r),a.transformTemplate,a.style),{...d.attrs,style:{...d.style}}},[l]);if(a.style){const d={};r0(d,a.style,a),c.style={...d,...c.style}}return c}const J1=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Uc(a){return typeof a!="string"||a.includes("-")?!1:!!(J1.indexOf(a)>-1||/[A-Z]/u.test(a))}function $1(a=!1){return(u,r,c,{latestValues:d},f)=>{const p=(Uc(u)?P1:Q1)(r,d,f,u),g=j1(r,typeof u=="string",a),v=u!==W.Fragment?{...g,...p,ref:c}:{},{children:x}=r,S=W.useMemo(()=>ne(x)?x.get():x,[x]);return W.createElement(u,{...v,children:S})}}function yp(a){const l=[{},{}];return a?.values.forEach((u,r)=>{l[0][r]=u.get(),l[1][r]=u.getVelocity()}),l}function Lc(a,l,u,r){if(typeof l=="function"){const[c,d]=yp(r);l=l(u!==void 0?u:a.custom,c,d)}if(typeof l=="string"&&(l=a.variants&&a.variants[l]),typeof l=="function"){const[c,d]=yp(r);l=l(u!==void 0?u:a.custom,c,d)}return l}function ks(a){return ne(a)?a.get():a}function W1({scrapeMotionValuesFromProps:a,createRenderState:l},u,r,c){return{latestValues:I1(u,r,c,a),renderState:l()}}const d0=a=>(l,u)=>{const r=W.useContext(eu),c=W.useContext(tu),d=()=>W1(a,l,r,c);return u?d():sc(d)};function I1(a,l,u,r){const c={},d=r(a,{});for(const S in d)c[S]=ks(d[S]);let{initial:f,animate:m}=a;const p=au(a),g=a0(a);l&&g&&!p&&a.inherit!==!1&&(f===void 0&&(f=l.initial),m===void 0&&(m=l.animate));let v=u?u.initial===!1:!1;v=v||f===!1;const x=v?m:f;if(x&&typeof x!="boolean"&&!nu(x)){const S=Array.isArray(x)?x:[x];for(let V=0;V<S.length;V++){const j=Lc(a,S[V]);if(j){const{transitionEnd:Y,transition:X,...Q}=j;for(const C in Q){let M=Q[C];if(Array.isArray(M)){const _=v?M.length-1:0;M=M[_]}M!==null&&(c[C]=M)}for(const C in Y)c[C]=Y[C]}}}return c}function Hc(a,l,u){const{style:r}=a,c={};for(const d in r)(ne(r[d])||l.style&&ne(l.style[d])||u0(d,a)||u?.getValue(d)?.liveStyle!==void 0)&&(c[d]=r[d]);return c}const tS={useVisualState:d0({scrapeMotionValuesFromProps:Hc,createRenderState:Bc})};function h0(a,l,u){const r=Hc(a,l,u);for(const c in a)if(ne(a[c])||ne(l[c])){const d=ui.indexOf(c)!==-1?"attr"+c.charAt(0).toUpperCase()+c.substring(1):c;r[d]=a[c]}return r}const eS={useVisualState:d0({scrapeMotionValuesFromProps:h0,createRenderState:c0})};function nS(a,l){return function(r,{forwardMotionProps:c}={forwardMotionProps:!1}){const f={...Uc(r)?eS:tS,preloadedFeatures:a,useRender:$1(c),createVisualElement:l,Component:r};return _1(f)}}function Sl(a,l,u){const r=a.getProps();return Lc(r,l,u!==void 0?u:r.custom,a)}const Po=a=>Array.isArray(a);function aS(a,l,u){a.hasValue(l)?a.getValue(l).set(u):a.addValue(l,ai(u))}function iS(a){return Po(a)?a[a.length-1]||0:a}function lS(a,l){const u=Sl(a,l);let{transitionEnd:r={},transition:c={},...d}=u||{};d={...d,...r};for(const f in d){const m=iS(d[f]);aS(a,f,m)}}function sS(a){return!!(ne(a)&&a.add)}function Jo(a,l){const u=a.getValue("willChange");if(sS(u))return u.add(l);if(!u&&dn.WillChange){const r=new dn.WillChange("auto");a.addValue("willChange",r),r.add(l)}}function m0(a){return a.props[i0]}const uS=a=>a!==null;function rS(a,{repeat:l,repeatType:u="loop"},r){const c=a.filter(uS),d=l&&u!=="loop"&&l%2===1?0:c.length-1;return c[d]}const oS={type:"spring",stiffness:500,damping:25,restSpeed:10},cS=a=>({type:"spring",stiffness:550,damping:a===0?2*Math.sqrt(550):30,restSpeed:10}),fS={type:"keyframes",duration:.8},dS={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},hS=(a,{keyframes:l})=>l.length>2?fS:ri.has(a)?a.startsWith("scale")?cS(l[1]):oS:dS;function mS({when:a,delay:l,delayChildren:u,staggerChildren:r,staggerDirection:c,repeat:d,repeatType:f,repeatDelay:m,from:p,elapsed:g,...v}){return!!Object.keys(v).length}const qc=(a,l,u,r={},c,d)=>f=>{const m=jc(r,a)||{},p=m.delay||r.delay||0;let{elapsed:g=0}=r;g=g-Qe(p);const v={keyframes:Array.isArray(u)?u:[null,u],ease:"easeOut",velocity:l.getVelocity(),...m,delay:-g,onUpdate:S=>{l.set(S),m.onUpdate&&m.onUpdate(S)},onComplete:()=>{f(),m.onComplete&&m.onComplete()},name:a,motionValue:l,element:d?void 0:c};mS(m)||Object.assign(v,hS(a,v)),v.duration&&(v.duration=Qe(v.duration)),v.repeatDelay&&(v.repeatDelay=Qe(v.repeatDelay)),v.from!==void 0&&(v.keyframes[0]=v.from);let x=!1;if((v.type===!1||v.duration===0&&!v.repeatDelay)&&(v.duration=0,v.delay===0&&(x=!0)),(dn.instantAnimations||dn.skipAnimations)&&(x=!0,v.duration=0,v.delay=0),v.allowFlatten=!m.type&&!m.ease,x&&!d&&l.get()!==void 0){const S=rS(v.keyframes,m);if(S!==void 0){wt.update(()=>{v.onUpdate(S),v.onComplete()});return}}return m.isSync?new Ac(v):new Fx(v)};function pS({protectedKeys:a,needsAnimating:l},u){const r=a.hasOwnProperty(u)&&l[u]!==!0;return l[u]=!1,r}function p0(a,l,{delay:u=0,transitionOverride:r,type:c}={}){let{transition:d=a.getDefaultTransition(),transitionEnd:f,...m}=l;r&&(d=r);const p=[],g=c&&a.animationState&&a.animationState.getState()[c];for(const v in m){const x=a.getValue(v,a.latestValues[v]??null),S=m[v];if(S===void 0||g&&pS(g,v))continue;const V={delay:u,...jc(d||{},v)},j=x.get();if(j!==void 0&&!x.isAnimating&&!Array.isArray(S)&&S===j&&!V.velocity)continue;let Y=!1;if(window.MotionHandoffAnimation){const Q=m0(a);if(Q){const C=window.MotionHandoffAnimation(Q,v,wt);C!==null&&(V.startTime=C,Y=!0)}}Jo(a,v),x.start(qc(v,x,S,a.shouldReduceMotion&&Zg.has(v)?{type:!1}:V,a,Y));const X=x.animation;X&&p.push(X)}return f&&Promise.all(p).then(()=>{wt.update(()=>{f&&lS(a,f)})}),p}function $o(a,l,u={}){const r=Sl(a,l,u.type==="exit"?a.presenceContext?.custom:void 0);let{transition:c=a.getDefaultTransition()||{}}=r||{};u.transitionOverride&&(c=u.transitionOverride);const d=r?()=>Promise.all(p0(a,r,u)):()=>Promise.resolve(),f=a.variantChildren&&a.variantChildren.size?(p=0)=>{const{delayChildren:g=0,staggerChildren:v,staggerDirection:x}=c;return gS(a,l,p,g,v,x,u)}:()=>Promise.resolve(),{when:m}=c;if(m){const[p,g]=m==="beforeChildren"?[d,f]:[f,d];return p().then(()=>g())}else return Promise.all([d(),f(u.delay)])}function gS(a,l,u=0,r=0,c=0,d=1,f){const m=[],p=a.variantChildren.size,g=(p-1)*c,v=typeof r=="function",x=v?S=>r(S,p):d===1?(S=0)=>S*c:(S=0)=>g-S*c;return Array.from(a.variantChildren).sort(yS).forEach((S,V)=>{S.notify("AnimationStart",l),m.push($o(S,l,{...f,delay:u+(v?0:r)+x(V)}).then(()=>S.notify("AnimationComplete",l)))}),Promise.all(m)}function yS(a,l){return a.sortNodePosition(l)}function vS(a,l,u={}){a.notify("AnimationStart",l);let r;if(Array.isArray(l)){const c=l.map(d=>$o(a,d,u));r=Promise.all(c)}else if(typeof l=="string")r=$o(a,l,u);else{const c=typeof l=="function"?Sl(a,l,u.custom):l;r=Promise.all(p0(a,c,u))}return r.then(()=>{a.notify("AnimationComplete",l)})}function g0(a,l){if(!Array.isArray(l))return!1;const u=l.length;if(u!==a.length)return!1;for(let r=0;r<u;r++)if(l[r]!==a[r])return!1;return!0}const bS=zc.length;function y0(a){if(!a)return;if(!a.isControllingVariants){const u=a.parent?y0(a.parent)||{}:{};return a.props.initial!==void 0&&(u.initial=a.props.initial),u}const l={};for(let u=0;u<bS;u++){const r=zc[u],c=a.props[r];(bl(c)||c===!1)&&(l[r]=c)}return l}const xS=[...Nc].reverse(),SS=Nc.length;function TS(a){return l=>Promise.all(l.map(({animation:u,options:r})=>vS(a,u,r)))}function AS(a){let l=TS(a),u=vp(),r=!0;const c=p=>(g,v)=>{const x=Sl(a,v,p==="exit"?a.presenceContext?.custom:void 0);if(x){const{transition:S,transitionEnd:V,...j}=x;g={...g,...j,...V}}return g};function d(p){l=p(a)}function f(p){const{props:g}=a,v=y0(a.parent)||{},x=[],S=new Set;let V={},j=1/0;for(let X=0;X<SS;X++){const Q=xS[X],C=u[Q],M=g[Q]!==void 0?g[Q]:v[Q],_=bl(M),O=Q===p?C.isActive:null;O===!1&&(j=X);let B=M===v[Q]&&M!==g[Q]&&_;if(B&&r&&a.manuallyAnimateOnMount&&(B=!1),C.protectedKeys={...V},!C.isActive&&O===null||!M&&!C.prevProp||nu(M)||typeof M=="boolean")continue;const Z=ES(C.prevProp,M);let K=Z||Q===p&&C.isActive&&!B&&_||X>j&&_,F=!1;const ut=Array.isArray(M)?M:[M];let pt=ut.reduce(c(Q),{});O===!1&&(pt={});const{prevResolvedValues:St={}}=C,Se={...St,...pt},fe=k=>{K=!0,S.has(k)&&(F=!0,S.delete(k)),C.needsAnimating[k]=!0;const J=a.getValue(k);J&&(J.liveStyle=!1)};for(const k in Se){const J=pt[k],rt=St[k];if(V.hasOwnProperty(k))continue;let A=!1;Po(J)&&Po(rt)?A=!g0(J,rt):A=J!==rt,A?J!=null?fe(k):S.add(k):J!==void 0&&S.has(k)?fe(k):C.protectedKeys[k]=!0}C.prevProp=M,C.prevResolvedValues=pt,C.isActive&&(V={...V,...pt}),r&&a.blockInitialAnimation&&(K=!1),K&&(!(B&&Z)||F)&&x.push(...ut.map(k=>({animation:k,options:{type:Q}})))}if(S.size){const X={};if(typeof g.initial!="boolean"){const Q=Sl(a,Array.isArray(g.initial)?g.initial[0]:g.initial);Q&&Q.transition&&(X.transition=Q.transition)}S.forEach(Q=>{const C=a.getBaseTarget(Q),M=a.getValue(Q);M&&(M.liveStyle=!0),X[Q]=C??null}),x.push({animation:X})}let Y=!!x.length;return r&&(g.initial===!1||g.initial===g.animate)&&!a.manuallyAnimateOnMount&&(Y=!1),r=!1,Y?l(x):Promise.resolve()}function m(p,g){if(u[p].isActive===g)return Promise.resolve();a.variantChildren?.forEach(x=>x.animationState?.setActive(p,g)),u[p].isActive=g;const v=f(p);for(const x in u)u[x].protectedKeys={};return v}return{animateChanges:f,setActive:m,setAnimateFunction:d,getState:()=>u,reset:()=>{u=vp(),r=!0}}}function ES(a,l){return typeof l=="string"?l!==a:Array.isArray(l)?!g0(l,a):!1}function ua(a=!1){return{isActive:a,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function vp(){return{animate:ua(!0),whileInView:ua(),whileHover:ua(),whileTap:ua(),whileDrag:ua(),whileFocus:ua(),exit:ua()}}class qn{constructor(l){this.isMounted=!1,this.node=l}update(){}}class MS extends qn{constructor(l){super(l),l.animationState||(l.animationState=AS(l))}updateAnimationControlsSubscription(){const{animate:l}=this.node.getProps();nu(l)&&(this.unmountControls=l.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:l}=this.node.getProps(),{animate:u}=this.node.prevProps||{};l!==u&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let jS=0;class DS extends qn{constructor(){super(...arguments),this.id=jS++}update(){if(!this.node.presenceContext)return;const{isPresent:l,onExitComplete:u}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||l===r)return;const c=this.node.animationState.setActive("exit",!l);u&&!l&&c.then(()=>{u(this.id)})}mount(){const{register:l,onExitComplete:u}=this.node.presenceContext||{};u&&u(this.id),l&&(this.unmount=l(this.id))}unmount(){}}const CS={animation:{Feature:MS},exit:{Feature:DS}};function Tl(a,l,u,r={passive:!0}){return a.addEventListener(l,u,r),()=>a.removeEventListener(l,u)}function jl(a){return{point:{x:a.pageX,y:a.pageY}}}const wS=a=>l=>wc(l)&&a(l,jl(l));function hl(a,l,u,r){return Tl(a,l,wS(u),r)}function v0({top:a,left:l,right:u,bottom:r}){return{x:{min:l,max:u},y:{min:a,max:r}}}function RS({x:a,y:l}){return{top:l.min,right:a.max,bottom:l.max,left:a.min}}function OS(a,l){if(!l)return a;const u=l({x:a.left,y:a.top}),r=l({x:a.right,y:a.bottom});return{top:u.y,left:u.x,bottom:r.y,right:r.x}}const b0=1e-4,NS=1-b0,zS=1+b0,x0=.01,VS=0-x0,_S=0+x0;function ie(a){return a.max-a.min}function BS(a,l,u){return Math.abs(a-l)<=u}function bp(a,l,u,r=.5){a.origin=r,a.originPoint=Ot(l.min,l.max,a.origin),a.scale=ie(u)/ie(l),a.translate=Ot(u.min,u.max,a.origin)-a.originPoint,(a.scale>=NS&&a.scale<=zS||isNaN(a.scale))&&(a.scale=1),(a.translate>=VS&&a.translate<=_S||isNaN(a.translate))&&(a.translate=0)}function ml(a,l,u,r){bp(a.x,l.x,u.x,r?r.originX:void 0),bp(a.y,l.y,u.y,r?r.originY:void 0)}function xp(a,l,u){a.min=u.min+l.min,a.max=a.min+ie(l)}function US(a,l,u){xp(a.x,l.x,u.x),xp(a.y,l.y,u.y)}function Sp(a,l,u){a.min=l.min-u.min,a.max=a.min+ie(l)}function pl(a,l,u){Sp(a.x,l.x,u.x),Sp(a.y,l.y,u.y)}const Tp=()=>({translate:0,scale:1,origin:0,originPoint:0}),ti=()=>({x:Tp(),y:Tp()}),Ap=()=>({min:0,max:0}),Ut=()=>({x:Ap(),y:Ap()});function Ne(a){return[a("x"),a("y")]}function wo(a){return a===void 0||a===1}function Wo({scale:a,scaleX:l,scaleY:u}){return!wo(a)||!wo(l)||!wo(u)}function ra(a){return Wo(a)||S0(a)||a.z||a.rotate||a.rotateX||a.rotateY||a.skewX||a.skewY}function S0(a){return Ep(a.x)||Ep(a.y)}function Ep(a){return a&&a!=="0%"}function Is(a,l,u){const r=a-u,c=l*r;return u+c}function Mp(a,l,u,r,c){return c!==void 0&&(a=Is(a,c,r)),Is(a,u,r)+l}function Io(a,l=0,u=1,r,c){a.min=Mp(a.min,l,u,r,c),a.max=Mp(a.max,l,u,r,c)}function T0(a,{x:l,y:u}){Io(a.x,l.translate,l.scale,l.originPoint),Io(a.y,u.translate,u.scale,u.originPoint)}const jp=.999999999999,Dp=1.0000000000001;function LS(a,l,u,r=!1){const c=u.length;if(!c)return;l.x=l.y=1;let d,f;for(let m=0;m<c;m++){d=u[m],f=d.projectionDelta;const{visualElement:p}=d.options;p&&p.props.style&&p.props.style.display==="contents"||(r&&d.options.layoutScroll&&d.scroll&&d!==d.root&&ni(a,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),f&&(l.x*=f.x.scale,l.y*=f.y.scale,T0(a,f)),r&&ra(d.latestValues)&&ni(a,d.latestValues))}l.x<Dp&&l.x>jp&&(l.x=1),l.y<Dp&&l.y>jp&&(l.y=1)}function ei(a,l){a.min=a.min+l,a.max=a.max+l}function Cp(a,l,u,r,c=.5){const d=Ot(a.min,a.max,c);Io(a,l,u,d,r)}function ni(a,l){Cp(a.x,l.x,l.scaleX,l.scale,l.originX),Cp(a.y,l.y,l.scaleY,l.scale,l.originY)}function A0(a,l){return v0(OS(a.getBoundingClientRect(),l))}function HS(a,l,u){const r=A0(a,u),{scroll:c}=l;return c&&(ei(r.x,c.offset.x),ei(r.y,c.offset.y)),r}const E0=({current:a})=>a?a.ownerDocument.defaultView:null,wp=(a,l)=>Math.abs(a-l);function qS(a,l){const u=wp(a.x,l.x),r=wp(a.y,l.y);return Math.sqrt(u**2+r**2)}class M0{constructor(l,u,{transformPagePoint:r,contextWindow:c=window,dragSnapToOrigin:d=!1,distanceThreshold:f=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const S=Oo(this.lastMoveEventInfo,this.history),V=this.startEvent!==null,j=qS(S.offset,{x:0,y:0})>=this.distanceThreshold;if(!V&&!j)return;const{point:Y}=S,{timestamp:X}=Wt;this.history.push({...Y,timestamp:X});const{onStart:Q,onMove:C}=this.handlers;V||(Q&&Q(this.lastMoveEvent,S),this.startEvent=this.lastMoveEvent),C&&C(this.lastMoveEvent,S)},this.handlePointerMove=(S,V)=>{this.lastMoveEvent=S,this.lastMoveEventInfo=Ro(V,this.transformPagePoint),wt.update(this.updatePoint,!0)},this.handlePointerUp=(S,V)=>{this.end();const{onEnd:j,onSessionEnd:Y,resumeAnimation:X}=this.handlers;if(this.dragSnapToOrigin&&X&&X(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const Q=Oo(S.type==="pointercancel"?this.lastMoveEventInfo:Ro(V,this.transformPagePoint),this.history);this.startEvent&&j&&j(S,Q),Y&&Y(S,Q)},!wc(l))return;this.dragSnapToOrigin=d,this.handlers=u,this.transformPagePoint=r,this.distanceThreshold=f,this.contextWindow=c||window;const m=jl(l),p=Ro(m,this.transformPagePoint),{point:g}=p,{timestamp:v}=Wt;this.history=[{...g,timestamp:v}];const{onSessionStart:x}=u;x&&x(l,Oo(p,this.history)),this.removeListeners=Al(hl(this.contextWindow,"pointermove",this.handlePointerMove),hl(this.contextWindow,"pointerup",this.handlePointerUp),hl(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(l){this.handlers=l}end(){this.removeListeners&&this.removeListeners(),Ln(this.updatePoint)}}function Ro(a,l){return l?{point:l(a.point)}:a}function Rp(a,l){return{x:a.x-l.x,y:a.y-l.y}}function Oo({point:a},l){return{point:a,delta:Rp(a,j0(l)),offset:Rp(a,YS(l)),velocity:GS(l,.1)}}function YS(a){return a[0]}function j0(a){return a[a.length-1]}function GS(a,l){if(a.length<2)return{x:0,y:0};let u=a.length-1,r=null;const c=j0(a);for(;u>=0&&(r=a[u],!(c.timestamp-r.timestamp>Qe(l)));)u--;if(!r)return{x:0,y:0};const d=Ke(c.timestamp-r.timestamp);if(d===0)return{x:0,y:0};const f={x:(c.x-r.x)/d,y:(c.y-r.y)/d};return f.x===1/0&&(f.x=0),f.y===1/0&&(f.y=0),f}function XS(a,{min:l,max:u},r){return l!==void 0&&a<l?a=r?Ot(l,a,r.min):Math.max(a,l):u!==void 0&&a>u&&(a=r?Ot(u,a,r.max):Math.min(a,u)),a}function Op(a,l,u){return{min:l!==void 0?a.min+l:void 0,max:u!==void 0?a.max+u-(a.max-a.min):void 0}}function ZS(a,{top:l,left:u,bottom:r,right:c}){return{x:Op(a.x,u,c),y:Op(a.y,l,r)}}function Np(a,l){let u=l.min-a.min,r=l.max-a.max;return l.max-l.min<a.max-a.min&&([u,r]=[r,u]),{min:u,max:r}}function QS(a,l){return{x:Np(a.x,l.x),y:Np(a.y,l.y)}}function KS(a,l){let u=.5;const r=ie(a),c=ie(l);return c>r?u=gl(l.min,l.max-r,a.min):r>c&&(u=gl(a.min,a.max-c,l.min)),fn(0,1,u)}function kS(a,l){const u={};return l.min!==void 0&&(u.min=l.min-a.min),l.max!==void 0&&(u.max=l.max-a.min),u}const tc=.35;function FS(a=tc){return a===!1?a=0:a===!0&&(a=tc),{x:zp(a,"left","right"),y:zp(a,"top","bottom")}}function zp(a,l,u){return{min:Vp(a,l),max:Vp(a,u)}}function Vp(a,l){return typeof a=="number"?a:a[l]||0}const PS=new WeakMap;class JS{constructor(l){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=Ut(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=l}start(l,{snapToCursor:u=!1,distanceThreshold:r}={}){const{presenceContext:c}=this.visualElement;if(c&&c.isPresent===!1)return;const d=x=>{const{dragSnapToOrigin:S}=this.getProps();S?this.pauseAnimation():this.stopAnimation(),u&&this.snapToCursor(jl(x).point)},f=(x,S)=>{const{drag:V,dragPropagation:j,onDragStart:Y}=this.getProps();if(V&&!j&&(this.openDragLock&&this.openDragLock(),this.openDragLock=c1(V),!this.openDragLock))return;this.latestPointerEvent=x,this.latestPanInfo=S,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Ne(Q=>{let C=this.getAxisMotionValue(Q).get()||0;if(ke.test(C)){const{projection:M}=this.visualElement;if(M&&M.layout){const _=M.layout.layoutBox[Q];_&&(C=ie(_)*(parseFloat(C)/100))}}this.originPoint[Q]=C}),Y&&wt.postRender(()=>Y(x,S)),Jo(this.visualElement,"transform");const{animationState:X}=this.visualElement;X&&X.setActive("whileDrag",!0)},m=(x,S)=>{this.latestPointerEvent=x,this.latestPanInfo=S;const{dragPropagation:V,dragDirectionLock:j,onDirectionLock:Y,onDrag:X}=this.getProps();if(!V&&!this.openDragLock)return;const{offset:Q}=S;if(j&&this.currentDirection===null){this.currentDirection=$S(Q),this.currentDirection!==null&&Y&&Y(this.currentDirection);return}this.updateAxis("x",S.point,Q),this.updateAxis("y",S.point,Q),this.visualElement.render(),X&&X(x,S)},p=(x,S)=>{this.latestPointerEvent=x,this.latestPanInfo=S,this.stop(x,S),this.latestPointerEvent=null,this.latestPanInfo=null},g=()=>Ne(x=>this.getAnimationState(x)==="paused"&&this.getAxisMotionValue(x).animation?.play()),{dragSnapToOrigin:v}=this.getProps();this.panSession=new M0(l,{onSessionStart:d,onStart:f,onMove:m,onSessionEnd:p,resumeAnimation:g},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:v,distanceThreshold:r,contextWindow:E0(this.visualElement)})}stop(l,u){const r=l||this.latestPointerEvent,c=u||this.latestPanInfo,d=this.isDragging;if(this.cancel(),!d||!c||!r)return;const{velocity:f}=c;this.startAnimation(f);const{onDragEnd:m}=this.getProps();m&&wt.postRender(()=>m(r,c))}cancel(){this.isDragging=!1;const{projection:l,animationState:u}=this.visualElement;l&&(l.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),u&&u.setActive("whileDrag",!1)}updateAxis(l,u,r){const{drag:c}=this.getProps();if(!r||!Zs(l,c,this.currentDirection))return;const d=this.getAxisMotionValue(l);let f=this.originPoint[l]+r[l];this.constraints&&this.constraints[l]&&(f=XS(f,this.constraints[l],this.elastic[l])),d.set(f)}resolveConstraints(){const{dragConstraints:l,dragElastic:u}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,c=this.constraints;l&&Ia(l)?this.constraints||(this.constraints=this.resolveRefConstraints()):l&&r?this.constraints=ZS(r.layoutBox,l):this.constraints=!1,this.elastic=FS(u),c!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&Ne(d=>{this.constraints!==!1&&this.getAxisMotionValue(d)&&(this.constraints[d]=kS(r.layoutBox[d],this.constraints[d]))})}resolveRefConstraints(){const{dragConstraints:l,onMeasureDragConstraints:u}=this.getProps();if(!l||!Ia(l))return!1;const r=l.current,{projection:c}=this.visualElement;if(!c||!c.layout)return!1;const d=HS(r,c.root,this.visualElement.getTransformPagePoint());let f=QS(c.layout.layoutBox,d);if(u){const m=u(RS(f));this.hasMutatedConstraints=!!m,m&&(f=v0(m))}return f}startAnimation(l){const{drag:u,dragMomentum:r,dragElastic:c,dragTransition:d,dragSnapToOrigin:f,onDragTransitionEnd:m}=this.getProps(),p=this.constraints||{},g=Ne(v=>{if(!Zs(v,u,this.currentDirection))return;let x=p&&p[v]||{};f&&(x={min:0,max:0});const S=c?200:1e6,V=c?40:1e7,j={type:"inertia",velocity:r?l[v]:0,bounceStiffness:S,bounceDamping:V,timeConstant:750,restDelta:1,restSpeed:10,...d,...x};return this.startAxisValueAnimation(v,j)});return Promise.all(g).then(m)}startAxisValueAnimation(l,u){const r=this.getAxisMotionValue(l);return Jo(this.visualElement,l),r.start(qc(l,r,0,u,this.visualElement,!1))}stopAnimation(){Ne(l=>this.getAxisMotionValue(l).stop())}pauseAnimation(){Ne(l=>this.getAxisMotionValue(l).animation?.pause())}getAnimationState(l){return this.getAxisMotionValue(l).animation?.state}getAxisMotionValue(l){const u=`_drag${l.toUpperCase()}`,r=this.visualElement.getProps(),c=r[u];return c||this.visualElement.getValue(l,(r.initial?r.initial[l]:void 0)||0)}snapToCursor(l){Ne(u=>{const{drag:r}=this.getProps();if(!Zs(u,r,this.currentDirection))return;const{projection:c}=this.visualElement,d=this.getAxisMotionValue(u);if(c&&c.layout){const{min:f,max:m}=c.layout.layoutBox[u];d.set(l[u]-Ot(f,m,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:l,dragConstraints:u}=this.getProps(),{projection:r}=this.visualElement;if(!Ia(u)||!r||!this.constraints)return;this.stopAnimation();const c={x:0,y:0};Ne(f=>{const m=this.getAxisMotionValue(f);if(m&&this.constraints!==!1){const p=m.get();c[f]=KS({min:p,max:p},this.constraints[f])}});const{transformTemplate:d}=this.visualElement.getProps();this.visualElement.current.style.transform=d?d({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),Ne(f=>{if(!Zs(f,l,null))return;const m=this.getAxisMotionValue(f),{min:p,max:g}=this.constraints[f];m.set(Ot(p,g,c[f]))})}addListeners(){if(!this.visualElement.current)return;PS.set(this.visualElement,this);const l=this.visualElement.current,u=hl(l,"pointerdown",p=>{const{drag:g,dragListener:v=!0}=this.getProps();g&&v&&this.start(p)}),r=()=>{const{dragConstraints:p}=this.getProps();Ia(p)&&p.current&&(this.constraints=this.resolveRefConstraints())},{projection:c}=this.visualElement,d=c.addEventListener("measure",r);c&&!c.layout&&(c.root&&c.root.updateScroll(),c.updateLayout()),wt.read(r);const f=Tl(window,"resize",()=>this.scalePositionWithinConstraints()),m=c.addEventListener("didUpdate",({delta:p,hasLayoutChanged:g})=>{this.isDragging&&g&&(Ne(v=>{const x=this.getAxisMotionValue(v);x&&(this.originPoint[v]+=p[v].translate,x.set(x.get()+p[v].translate))}),this.visualElement.render())});return()=>{f(),u(),d(),m&&m()}}getProps(){const l=this.visualElement.getProps(),{drag:u=!1,dragDirectionLock:r=!1,dragPropagation:c=!1,dragConstraints:d=!1,dragElastic:f=tc,dragMomentum:m=!0}=l;return{...l,drag:u,dragDirectionLock:r,dragPropagation:c,dragConstraints:d,dragElastic:f,dragMomentum:m}}}function Zs(a,l,u){return(l===!0||l===a)&&(u===null||u===a)}function $S(a,l=10){let u=null;return Math.abs(a.y)>l?u="y":Math.abs(a.x)>l&&(u="x"),u}class WS extends qn{constructor(l){super(l),this.removeGroupControls=ze,this.removeListeners=ze,this.controls=new JS(l)}mount(){const{dragControls:l}=this.node.getProps();l&&(this.removeGroupControls=l.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ze}unmount(){this.removeGroupControls(),this.removeListeners()}}const _p=a=>(l,u)=>{a&&wt.postRender(()=>a(l,u))};class IS extends qn{constructor(){super(...arguments),this.removePointerDownListener=ze}onPointerDown(l){this.session=new M0(l,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:E0(this.node)})}createPanHandlers(){const{onPanSessionStart:l,onPanStart:u,onPan:r,onPanEnd:c}=this.node.getProps();return{onSessionStart:_p(l),onStart:_p(u),onMove:r,onEnd:(d,f)=>{delete this.session,c&&wt.postRender(()=>c(d,f))}}}mount(){this.removePointerDownListener=hl(this.node.current,"pointerdown",l=>this.onPointerDown(l))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const Fs={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Bp(a,l){return l.max===l.min?0:a/(l.max-l.min)*100}const ol={correct:(a,l)=>{if(!l.target)return a;if(typeof a=="string")if(st.test(a))a=parseFloat(a);else return a;const u=Bp(a,l.target.x),r=Bp(a,l.target.y);return`${u}% ${r}%`}},tT={correct:(a,{treeScale:l,projectionDelta:u})=>{const r=a,c=Hn.parse(a);if(c.length>5)return r;const d=Hn.createTransformer(a),f=typeof c[0]!="number"?1:0,m=u.x.scale*l.x,p=u.y.scale*l.y;c[0+f]/=m,c[1+f]/=p;const g=Ot(m,p,.5);return typeof c[2+f]=="number"&&(c[2+f]/=g),typeof c[3+f]=="number"&&(c[3+f]/=g),d(c)}};let Up=!1;class eT extends W.Component{componentDidMount(){const{visualElement:l,layoutGroup:u,switchLayoutGroup:r,layoutId:c}=this.props,{projection:d}=l;H1(nT),d&&(u.group&&u.group.add(d),r&&r.register&&c&&r.register(d),Up&&d.root.didUpdate(),d.addEventListener("animationComplete",()=>{this.safeToRemove()}),d.setOptions({...d.options,onExitComplete:()=>this.safeToRemove()})),Fs.hasEverUpdated=!0}getSnapshotBeforeUpdate(l){const{layoutDependency:u,visualElement:r,drag:c,isPresent:d}=this.props,{projection:f}=r;return f&&(f.isPresent=d,Up=!0,c||l.layoutDependency!==u||u===void 0||l.isPresent!==d?f.willUpdate():this.safeToRemove(),l.isPresent!==d&&(d?f.promote():f.relegate()||wt.postRender(()=>{const m=f.getStack();(!m||!m.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:l}=this.props.visualElement;l&&(l.root.didUpdate(),Cc.postRender(()=>{!l.currentAnimation&&l.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:l,layoutGroup:u,switchLayoutGroup:r}=this.props,{projection:c}=l;c&&(c.scheduleCheckAfterUnmount(),u&&u.group&&u.group.remove(c),r&&r.deregister&&r.deregister(c))}safeToRemove(){const{safeToRemove:l}=this.props;l&&l()}render(){return null}}function D0(a){const[l,u]=t0(),r=W.useContext(lc);return y.jsx(eT,{...a,layoutGroup:r,switchLayoutGroup:W.useContext(l0),isPresent:l,safeToRemove:u})}const nT={borderRadius:{...ol,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ol,borderTopRightRadius:ol,borderBottomLeftRadius:ol,borderBottomRightRadius:ol,boxShadow:tT};function aT(a,l,u){const r=ne(a)?a:ai(a);return r.start(qc("",r,l,u)),r.animation}const iT=(a,l)=>a.depth-l.depth;class lT{constructor(){this.children=[],this.isDirty=!1}add(l){rc(this.children,l),this.isDirty=!0}remove(l){oc(this.children,l),this.isDirty=!0}forEach(l){this.isDirty&&this.children.sort(iT),this.isDirty=!1,this.children.forEach(l)}}function sT(a,l){const u=ce.now(),r=({timestamp:c})=>{const d=c-u;d>=l&&(Ln(r),a(d-l))};return wt.setup(r,!0),()=>Ln(r)}const C0=["TopLeft","TopRight","BottomLeft","BottomRight"],uT=C0.length,Lp=a=>typeof a=="string"?parseFloat(a):a,Hp=a=>typeof a=="number"||st.test(a);function rT(a,l,u,r,c,d){c?(a.opacity=Ot(0,u.opacity??1,oT(r)),a.opacityExit=Ot(l.opacity??1,0,cT(r))):d&&(a.opacity=Ot(l.opacity??1,u.opacity??1,r));for(let f=0;f<uT;f++){const m=`border${C0[f]}Radius`;let p=qp(l,m),g=qp(u,m);if(p===void 0&&g===void 0)continue;p||(p=0),g||(g=0),p===0||g===0||Hp(p)===Hp(g)?(a[m]=Math.max(Ot(Lp(p),Lp(g),r),0),(ke.test(g)||ke.test(p))&&(a[m]+="%")):a[m]=g}(l.rotate||u.rotate)&&(a.rotate=Ot(l.rotate||0,u.rotate||0,r))}function qp(a,l){return a[l]!==void 0?a[l]:a.borderRadius}const oT=w0(0,.5,Sg),cT=w0(.5,.95,ze);function w0(a,l,u){return r=>r<a?0:r>l?1:u(gl(a,l,r))}function Yp(a,l){a.min=l.min,a.max=l.max}function Oe(a,l){Yp(a.x,l.x),Yp(a.y,l.y)}function Gp(a,l){a.translate=l.translate,a.scale=l.scale,a.originPoint=l.originPoint,a.origin=l.origin}function Xp(a,l,u,r,c){return a-=l,a=Is(a,1/u,r),c!==void 0&&(a=Is(a,1/c,r)),a}function fT(a,l=0,u=1,r=.5,c,d=a,f=a){if(ke.test(l)&&(l=parseFloat(l),l=Ot(f.min,f.max,l/100)-f.min),typeof l!="number")return;let m=Ot(d.min,d.max,r);a===d&&(m-=l),a.min=Xp(a.min,l,u,m,c),a.max=Xp(a.max,l,u,m,c)}function Zp(a,l,[u,r,c],d,f){fT(a,l[u],l[r],l[c],l.scale,d,f)}const dT=["x","scaleX","originX"],hT=["y","scaleY","originY"];function Qp(a,l,u,r){Zp(a.x,l,dT,u?u.x:void 0,r?r.x:void 0),Zp(a.y,l,hT,u?u.y:void 0,r?r.y:void 0)}function Kp(a){return a.translate===0&&a.scale===1}function R0(a){return Kp(a.x)&&Kp(a.y)}function kp(a,l){return a.min===l.min&&a.max===l.max}function mT(a,l){return kp(a.x,l.x)&&kp(a.y,l.y)}function Fp(a,l){return Math.round(a.min)===Math.round(l.min)&&Math.round(a.max)===Math.round(l.max)}function O0(a,l){return Fp(a.x,l.x)&&Fp(a.y,l.y)}function Pp(a){return ie(a.x)/ie(a.y)}function Jp(a,l){return a.translate===l.translate&&a.scale===l.scale&&a.originPoint===l.originPoint}class pT{constructor(){this.members=[]}add(l){rc(this.members,l),l.scheduleRender()}remove(l){if(oc(this.members,l),l===this.prevLead&&(this.prevLead=void 0),l===this.lead){const u=this.members[this.members.length-1];u&&this.promote(u)}}relegate(l){const u=this.members.findIndex(c=>l===c);if(u===0)return!1;let r;for(let c=u;c>=0;c--){const d=this.members[c];if(d.isPresent!==!1){r=d;break}}return r?(this.promote(r),!0):!1}promote(l,u){const r=this.lead;if(l!==r&&(this.prevLead=r,this.lead=l,l.show(),r)){r.instance&&r.scheduleRender(),l.scheduleRender(),l.resumeFrom=r,u&&(l.resumeFrom.preserveOpacity=!0),r.snapshot&&(l.snapshot=r.snapshot,l.snapshot.latestValues=r.animationValues||r.latestValues),l.root&&l.root.isUpdating&&(l.isLayoutDirty=!0);const{crossfade:c}=l.options;c===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(l=>{const{options:u,resumingFrom:r}=l;u.onExitComplete&&u.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(l=>{l.instance&&l.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function gT(a,l,u){let r="";const c=a.x.translate/l.x,d=a.y.translate/l.y,f=u?.z||0;if((c||d||f)&&(r=`translate3d(${c}px, ${d}px, ${f}px) `),(l.x!==1||l.y!==1)&&(r+=`scale(${1/l.x}, ${1/l.y}) `),u){const{transformPerspective:g,rotate:v,rotateX:x,rotateY:S,skewX:V,skewY:j}=u;g&&(r=`perspective(${g}px) ${r}`),v&&(r+=`rotate(${v}deg) `),x&&(r+=`rotateX(${x}deg) `),S&&(r+=`rotateY(${S}deg) `),V&&(r+=`skewX(${V}deg) `),j&&(r+=`skewY(${j}deg) `)}const m=a.x.scale*l.x,p=a.y.scale*l.y;return(m!==1||p!==1)&&(r+=`scale(${m}, ${p})`),r||"none"}const No=["","X","Y","Z"],yT=1e3;let vT=0;function zo(a,l,u,r){const{latestValues:c}=l;c[a]&&(u[a]=c[a],l.setStaticValue(a,0),r&&(r[a]=0))}function N0(a){if(a.hasCheckedOptimisedAppear=!0,a.root===a)return;const{visualElement:l}=a.options;if(!l)return;const u=m0(l);if(window.MotionHasOptimisedAnimation(u,"transform")){const{layout:c,layoutId:d}=a.options;window.MotionCancelOptimisedAnimation(u,"transform",wt,!(c||d))}const{parent:r}=a;r&&!r.hasCheckedOptimisedAppear&&N0(r)}function z0({attachResizeListener:a,defaultParent:l,measureScroll:u,checkIsScrollRoot:r,resetTransform:c}){return class{constructor(f={},m=l?.()){this.id=vT++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(ST),this.nodes.forEach(MT),this.nodes.forEach(jT),this.nodes.forEach(TT)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=f,this.root=m?m.root||m:this,this.path=m?[...m.path,m]:[],this.parent=m,this.depth=m?m.depth+1:0;for(let p=0;p<this.path.length;p++)this.path[p].shouldResetTransform=!0;this.root===this&&(this.nodes=new lT)}addEventListener(f,m){return this.eventHandlers.has(f)||this.eventHandlers.set(f,new dc),this.eventHandlers.get(f).add(m)}notifyListeners(f,...m){const p=this.eventHandlers.get(f);p&&p.notify(...m)}hasListeners(f){return this.eventHandlers.has(f)}mount(f){if(this.instance)return;this.isSVG=Ig(f)&&!g1(f),this.instance=f;const{layoutId:m,layout:p,visualElement:g}=this.options;if(g&&!g.current&&g.mount(f),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(p||m)&&(this.isLayoutDirty=!0),a){let v,x=0;const S=()=>this.root.updateBlockedByResize=!1;wt.read(()=>{x=window.innerWidth}),a(f,()=>{const V=window.innerWidth;V!==x&&(x=V,this.root.updateBlockedByResize=!0,v&&v(),v=sT(S,250),Fs.hasAnimatedSinceResize&&(Fs.hasAnimatedSinceResize=!1,this.nodes.forEach(Ip)))})}m&&this.root.registerSharedNode(m,this),this.options.animate!==!1&&g&&(m||p)&&this.addEventListener("didUpdate",({delta:v,hasLayoutChanged:x,hasRelativeLayoutChanged:S,layout:V})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const j=this.options.transition||g.getDefaultTransition()||OT,{onLayoutAnimationStart:Y,onLayoutAnimationComplete:X}=g.getProps(),Q=!this.targetLayout||!O0(this.targetLayout,V),C=!x&&S;if(this.options.layoutRoot||this.resumeFrom||C||x&&(Q||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const M={...jc(j,"layout"),onPlay:Y,onComplete:X};(g.shouldReduceMotion||this.options.layoutRoot)&&(M.delay=0,M.type=!1),this.startAnimation(M),this.setAnimationOrigin(v,C)}else x||Ip(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=V})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const f=this.getStack();f&&f.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),Ln(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(DT),this.animationId++)}getTransformTemplate(){const{visualElement:f}=this.options;return f&&f.getProps().transformTemplate}willUpdate(f=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&N0(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let v=0;v<this.path.length;v++){const x=this.path[v];x.shouldResetTransform=!0,x.updateScroll("snapshot"),x.options.layoutRoot&&x.willUpdate(!1)}const{layoutId:m,layout:p}=this.options;if(m===void 0&&!p)return;const g=this.getTransformTemplate();this.prevTransformTemplateValue=g?g(this.latestValues,""):void 0,this.updateSnapshot(),f&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach($p);return}if(this.animationId<=this.animationCommitId){this.nodes.forEach(Wp);return}this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(ET),this.nodes.forEach(bT),this.nodes.forEach(xT)):this.nodes.forEach(Wp),this.clearAllSnapshots();const m=ce.now();Wt.delta=fn(0,1e3/60,m-Wt.timestamp),Wt.timestamp=m,Wt.isProcessing=!0,Ao.update.process(Wt),Ao.preRender.process(Wt),Ao.render.process(Wt),Wt.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Cc.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(AT),this.sharedNodes.forEach(CT)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,wt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){wt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!ie(this.snapshot.measuredBox.x)&&!ie(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let p=0;p<this.path.length;p++)this.path[p].updateScroll();const f=this.layout;this.layout=this.measure(!1),this.layoutCorrected=Ut(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:m}=this.options;m&&m.notify("LayoutMeasure",this.layout.layoutBox,f?f.layoutBox:void 0)}updateScroll(f="measure"){let m=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===f&&(m=!1),m&&this.instance){const p=r(this.instance);this.scroll={animationId:this.root.animationId,phase:f,isRoot:p,offset:u(this.instance),wasRoot:this.scroll?this.scroll.isRoot:p}}}resetTransform(){if(!c)return;const f=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,m=this.projectionDelta&&!R0(this.projectionDelta),p=this.getTransformTemplate(),g=p?p(this.latestValues,""):void 0,v=g!==this.prevTransformTemplateValue;f&&this.instance&&(m||ra(this.latestValues)||v)&&(c(this.instance,g),this.shouldResetTransform=!1,this.scheduleRender())}measure(f=!0){const m=this.measurePageBox();let p=this.removeElementScroll(m);return f&&(p=this.removeTransform(p)),NT(p),{animationId:this.root.animationId,measuredBox:m,layoutBox:p,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:f}=this.options;if(!f)return Ut();const m=f.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(zT))){const{scroll:g}=this.root;g&&(ei(m.x,g.offset.x),ei(m.y,g.offset.y))}return m}removeElementScroll(f){const m=Ut();if(Oe(m,f),this.scroll?.wasRoot)return m;for(let p=0;p<this.path.length;p++){const g=this.path[p],{scroll:v,options:x}=g;g!==this.root&&v&&x.layoutScroll&&(v.wasRoot&&Oe(m,f),ei(m.x,v.offset.x),ei(m.y,v.offset.y))}return m}applyTransform(f,m=!1){const p=Ut();Oe(p,f);for(let g=0;g<this.path.length;g++){const v=this.path[g];!m&&v.options.layoutScroll&&v.scroll&&v!==v.root&&ni(p,{x:-v.scroll.offset.x,y:-v.scroll.offset.y}),ra(v.latestValues)&&ni(p,v.latestValues)}return ra(this.latestValues)&&ni(p,this.latestValues),p}removeTransform(f){const m=Ut();Oe(m,f);for(let p=0;p<this.path.length;p++){const g=this.path[p];if(!g.instance||!ra(g.latestValues))continue;Wo(g.latestValues)&&g.updateSnapshot();const v=Ut(),x=g.measurePageBox();Oe(v,x),Qp(m,g.latestValues,g.snapshot?g.snapshot.layoutBox:void 0,v)}return ra(this.latestValues)&&Qp(m,this.latestValues),m}setTargetDelta(f){this.targetDelta=f,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(f){this.options={...this.options,...f,crossfade:f.crossfade!==void 0?f.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Wt.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(f=!1){const m=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=m.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=m.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=m.isSharedProjectionDirty);const p=!!this.resumingFrom||this!==m;if(!(f||p&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:v,layoutId:x}=this.options;if(!(!this.layout||!(v||x))){if(this.resolvedRelativeTargetAt=Wt.timestamp,!this.targetDelta&&!this.relativeTarget){const S=this.getClosestProjectingParent();S&&S.layout&&this.animationProgress!==1?(this.relativeParent=S,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Ut(),this.relativeTargetOrigin=Ut(),pl(this.relativeTargetOrigin,this.layout.layoutBox,S.layout.layoutBox),Oe(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=Ut(),this.targetWithTransforms=Ut()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),US(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Oe(this.target,this.layout.layoutBox),T0(this.target,this.targetDelta)):Oe(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const S=this.getClosestProjectingParent();S&&!!S.resumingFrom==!!this.resumingFrom&&!S.options.layoutScroll&&S.target&&this.animationProgress!==1?(this.relativeParent=S,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Ut(),this.relativeTargetOrigin=Ut(),pl(this.relativeTargetOrigin,this.target,S.target),Oe(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||Wo(this.parent.latestValues)||S0(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){const f=this.getLead(),m=!!this.resumingFrom||this!==f;let p=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(p=!1),m&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(p=!1),this.resolvedRelativeTargetAt===Wt.timestamp&&(p=!1),p)return;const{layout:g,layoutId:v}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(g||v))return;Oe(this.layoutCorrected,this.layout.layoutBox);const x=this.treeScale.x,S=this.treeScale.y;LS(this.layoutCorrected,this.treeScale,this.path,m),f.layout&&!f.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(f.target=f.layout.layoutBox,f.targetWithTransforms=Ut());const{target:V}=f;if(!V){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(Gp(this.prevProjectionDelta.x,this.projectionDelta.x),Gp(this.prevProjectionDelta.y,this.projectionDelta.y)),ml(this.projectionDelta,this.layoutCorrected,V,this.latestValues),(this.treeScale.x!==x||this.treeScale.y!==S||!Jp(this.projectionDelta.x,this.prevProjectionDelta.x)||!Jp(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",V))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(f=!0){if(this.options.visualElement?.scheduleRender(),f){const m=this.getStack();m&&m.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=ti(),this.projectionDelta=ti(),this.projectionDeltaWithTransform=ti()}setAnimationOrigin(f,m=!1){const p=this.snapshot,g=p?p.latestValues:{},v={...this.latestValues},x=ti();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!m;const S=Ut(),V=p?p.source:void 0,j=this.layout?this.layout.source:void 0,Y=V!==j,X=this.getStack(),Q=!X||X.members.length<=1,C=!!(Y&&!Q&&this.options.crossfade===!0&&!this.path.some(RT));this.animationProgress=0;let M;this.mixTargetDelta=_=>{const O=_/1e3;tg(x.x,f.x,O),tg(x.y,f.y,O),this.setTargetDelta(x),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(pl(S,this.layout.layoutBox,this.relativeParent.layout.layoutBox),wT(this.relativeTarget,this.relativeTargetOrigin,S,O),M&&mT(this.relativeTarget,M)&&(this.isProjectionDirty=!1),M||(M=Ut()),Oe(M,this.relativeTarget)),Y&&(this.animationValues=v,rT(v,g,this.latestValues,O,C,Q)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=O},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(f){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(Ln(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=wt.update(()=>{Fs.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=ai(0)),this.currentAnimation=aT(this.motionValue,[0,1e3],{...f,velocity:0,isSync:!0,onUpdate:m=>{this.mixTargetDelta(m),f.onUpdate&&f.onUpdate(m)},onStop:()=>{},onComplete:()=>{f.onComplete&&f.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const f=this.getStack();f&&f.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(yT),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const f=this.getLead();let{targetWithTransforms:m,target:p,layout:g,latestValues:v}=f;if(!(!m||!p||!g)){if(this!==f&&this.layout&&g&&V0(this.options.animationType,this.layout.layoutBox,g.layoutBox)){p=this.target||Ut();const x=ie(this.layout.layoutBox.x);p.x.min=f.target.x.min,p.x.max=p.x.min+x;const S=ie(this.layout.layoutBox.y);p.y.min=f.target.y.min,p.y.max=p.y.min+S}Oe(m,p),ni(m,v),ml(this.projectionDeltaWithTransform,this.layoutCorrected,m,v)}}registerSharedNode(f,m){this.sharedNodes.has(f)||this.sharedNodes.set(f,new pT),this.sharedNodes.get(f).add(m);const g=m.options.initialPromotionConfig;m.promote({transition:g?g.transition:void 0,preserveFollowOpacity:g&&g.shouldPreserveFollowOpacity?g.shouldPreserveFollowOpacity(m):void 0})}isLead(){const f=this.getStack();return f?f.lead===this:!0}getLead(){const{layoutId:f}=this.options;return f?this.getStack()?.lead||this:this}getPrevLead(){const{layoutId:f}=this.options;return f?this.getStack()?.prevLead:void 0}getStack(){const{layoutId:f}=this.options;if(f)return this.root.sharedNodes.get(f)}promote({needsReset:f,transition:m,preserveFollowOpacity:p}={}){const g=this.getStack();g&&g.promote(this,p),f&&(this.projectionDelta=void 0,this.needsReset=!0),m&&this.setOptions({transition:m})}relegate(){const f=this.getStack();return f?f.relegate(this):!1}resetSkewAndRotation(){const{visualElement:f}=this.options;if(!f)return;let m=!1;const{latestValues:p}=f;if((p.z||p.rotate||p.rotateX||p.rotateY||p.rotateZ||p.skewX||p.skewY)&&(m=!0),!m)return;const g={};p.z&&zo("z",f,g,this.animationValues);for(let v=0;v<No.length;v++)zo(`rotate${No[v]}`,f,g,this.animationValues),zo(`skew${No[v]}`,f,g,this.animationValues);f.render();for(const v in g)f.setStaticValue(v,g[v]),this.animationValues&&(this.animationValues[v]=g[v]);f.scheduleRender()}applyProjectionStyles(f,m){if(!this.instance||this.isSVG)return;if(!this.isVisible){f.visibility="hidden";return}const p=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,f.visibility="",f.opacity="",f.pointerEvents=ks(m?.pointerEvents)||"",f.transform=p?p(this.latestValues,""):"none";return}const g=this.getLead();if(!this.projectionDelta||!this.layout||!g.target){this.options.layoutId&&(f.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,f.pointerEvents=ks(m?.pointerEvents)||""),this.hasProjected&&!ra(this.latestValues)&&(f.transform=p?p({},""):"none",this.hasProjected=!1);return}f.visibility="";const v=g.animationValues||g.latestValues;this.applyTransformsToTarget();let x=gT(this.projectionDeltaWithTransform,this.treeScale,v);p&&(x=p(v,x)),f.transform=x;const{x:S,y:V}=this.projectionDelta;f.transformOrigin=`${S.origin*100}% ${V.origin*100}% 0`,g.animationValues?f.opacity=g===this?v.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:v.opacityExit:f.opacity=g===this?v.opacity!==void 0?v.opacity:"":v.opacityExit!==void 0?v.opacityExit:0;for(const j in xl){if(v[j]===void 0)continue;const{correct:Y,applyTo:X,isCSSVariable:Q}=xl[j],C=x==="none"?v[j]:Y(v[j],g);if(X){const M=X.length;for(let _=0;_<M;_++)f[X[_]]=C}else Q?this.options.visualElement.renderState.vars[j]=C:f[j]=C}this.options.layoutId&&(f.pointerEvents=g===this?ks(m?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(f=>f.currentAnimation?.stop()),this.root.nodes.forEach($p),this.root.sharedNodes.clear()}}}function bT(a){a.updateLayout()}function xT(a){const l=a.resumeFrom?.snapshot||a.snapshot;if(a.isLead()&&a.layout&&l&&a.hasListeners("didUpdate")){const{layoutBox:u,measuredBox:r}=a.layout,{animationType:c}=a.options,d=l.source!==a.layout.source;c==="size"?Ne(v=>{const x=d?l.measuredBox[v]:l.layoutBox[v],S=ie(x);x.min=u[v].min,x.max=x.min+S}):V0(c,l.layoutBox,u)&&Ne(v=>{const x=d?l.measuredBox[v]:l.layoutBox[v],S=ie(u[v]);x.max=x.min+S,a.relativeTarget&&!a.currentAnimation&&(a.isProjectionDirty=!0,a.relativeTarget[v].max=a.relativeTarget[v].min+S)});const f=ti();ml(f,u,l.layoutBox);const m=ti();d?ml(m,a.applyTransform(r,!0),l.measuredBox):ml(m,u,l.layoutBox);const p=!R0(f);let g=!1;if(!a.resumeFrom){const v=a.getClosestProjectingParent();if(v&&!v.resumeFrom){const{snapshot:x,layout:S}=v;if(x&&S){const V=Ut();pl(V,l.layoutBox,x.layoutBox);const j=Ut();pl(j,u,S.layoutBox),O0(V,j)||(g=!0),v.options.layoutRoot&&(a.relativeTarget=j,a.relativeTargetOrigin=V,a.relativeParent=v)}}}a.notifyListeners("didUpdate",{layout:u,snapshot:l,delta:m,layoutDelta:f,hasLayoutChanged:p,hasRelativeLayoutChanged:g})}else if(a.isLead()){const{onExitComplete:u}=a.options;u&&u()}a.options.transition=void 0}function ST(a){a.parent&&(a.isProjecting()||(a.isProjectionDirty=a.parent.isProjectionDirty),a.isSharedProjectionDirty||(a.isSharedProjectionDirty=!!(a.isProjectionDirty||a.parent.isProjectionDirty||a.parent.isSharedProjectionDirty)),a.isTransformDirty||(a.isTransformDirty=a.parent.isTransformDirty))}function TT(a){a.isProjectionDirty=a.isSharedProjectionDirty=a.isTransformDirty=!1}function AT(a){a.clearSnapshot()}function $p(a){a.clearMeasurements()}function Wp(a){a.isLayoutDirty=!1}function ET(a){const{visualElement:l}=a.options;l&&l.getProps().onBeforeLayoutMeasure&&l.notify("BeforeLayoutMeasure"),a.resetTransform()}function Ip(a){a.finishAnimation(),a.targetDelta=a.relativeTarget=a.target=void 0,a.isProjectionDirty=!0}function MT(a){a.resolveTargetDelta()}function jT(a){a.calcProjection()}function DT(a){a.resetSkewAndRotation()}function CT(a){a.removeLeadSnapshot()}function tg(a,l,u){a.translate=Ot(l.translate,0,u),a.scale=Ot(l.scale,1,u),a.origin=l.origin,a.originPoint=l.originPoint}function eg(a,l,u,r){a.min=Ot(l.min,u.min,r),a.max=Ot(l.max,u.max,r)}function wT(a,l,u,r){eg(a.x,l.x,u.x,r),eg(a.y,l.y,u.y,r)}function RT(a){return a.animationValues&&a.animationValues.opacityExit!==void 0}const OT={duration:.45,ease:[.4,0,.1,1]},ng=a=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(a),ag=ng("applewebkit/")&&!ng("chrome/")?Math.round:ze;function ig(a){a.min=ag(a.min),a.max=ag(a.max)}function NT(a){ig(a.x),ig(a.y)}function V0(a,l,u){return a==="position"||a==="preserve-aspect"&&!BS(Pp(l),Pp(u),.2)}function zT(a){return a!==a.root&&a.scroll?.wasRoot}const VT=z0({attachResizeListener:(a,l)=>Tl(a,"resize",l),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Vo={current:void 0},_0=z0({measureScroll:a=>({x:a.scrollLeft,y:a.scrollTop}),defaultParent:()=>{if(!Vo.current){const a=new VT({});a.mount(window),a.setOptions({layoutScroll:!0}),Vo.current=a}return Vo.current},resetTransform:(a,l)=>{a.style.transform=l!==void 0?l:"none"},checkIsScrollRoot:a=>window.getComputedStyle(a).position==="fixed"}),_T={pan:{Feature:IS},drag:{Feature:WS,ProjectionNode:_0,MeasureLayout:D0}};function lg(a,l,u){const{props:r}=a;a.animationState&&r.whileHover&&a.animationState.setActive("whileHover",u==="Start");const c="onHover"+u,d=r[c];d&&wt.postRender(()=>d(l,jl(l)))}class BT extends qn{mount(){const{current:l}=this.node;l&&(this.unmount=f1(l,(u,r)=>(lg(this.node,r,"Start"),c=>lg(this.node,c,"End"))))}unmount(){}}class UT extends qn{constructor(){super(...arguments),this.isActive=!1}onFocus(){let l=!1;try{l=this.node.current.matches(":focus-visible")}catch{l=!0}!l||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Al(Tl(this.node.current,"focus",()=>this.onFocus()),Tl(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function sg(a,l,u){const{props:r}=a;if(a.current instanceof HTMLButtonElement&&a.current.disabled)return;a.animationState&&r.whileTap&&a.animationState.setActive("whileTap",u==="Start");const c="onTap"+(u==="End"?"":u),d=r[c];d&&wt.postRender(()=>d(l,jl(l)))}class LT extends qn{mount(){const{current:l}=this.node;l&&(this.unmount=p1(l,(u,r)=>(sg(this.node,r,"Start"),(c,{success:d})=>sg(this.node,c,d?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const ec=new WeakMap,_o=new WeakMap,HT=a=>{const l=ec.get(a.target);l&&l(a)},qT=a=>{a.forEach(HT)};function YT({root:a,...l}){const u=a||document;_o.has(u)||_o.set(u,{});const r=_o.get(u),c=JSON.stringify(l);return r[c]||(r[c]=new IntersectionObserver(qT,{root:a,...l})),r[c]}function GT(a,l,u){const r=YT(l);return ec.set(a,u),r.observe(a),()=>{ec.delete(a),r.unobserve(a)}}const XT={some:0,all:1};class ZT extends qn{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:l={}}=this.node.getProps(),{root:u,margin:r,amount:c="some",once:d}=l,f={root:u?u.current:void 0,rootMargin:r,threshold:typeof c=="number"?c:XT[c]},m=p=>{const{isIntersecting:g}=p;if(this.isInView===g||(this.isInView=g,d&&!g&&this.hasEnteredView))return;g&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",g);const{onViewportEnter:v,onViewportLeave:x}=this.node.getProps(),S=g?v:x;S&&S(p)};return GT(this.node.current,f,m)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:l,prevProps:u}=this.node;["amount","margin","root"].some(QT(l,u))&&this.startObserver()}unmount(){}}function QT({viewport:a={}},{viewport:l={}}={}){return u=>a[u]!==l[u]}const KT={inView:{Feature:ZT},tap:{Feature:LT},focus:{Feature:UT},hover:{Feature:BT}},kT={layout:{ProjectionNode:_0,MeasureLayout:D0}},nc={current:null},B0={current:!1};function FT(){if(B0.current=!0,!!uc)if(window.matchMedia){const a=window.matchMedia("(prefers-reduced-motion)"),l=()=>nc.current=a.matches;a.addEventListener("change",l),l()}else nc.current=!1}const PT=new WeakMap;function JT(a,l,u){for(const r in l){const c=l[r],d=u[r];if(ne(c))a.addValue(r,c);else if(ne(d))a.addValue(r,ai(c,{owner:a}));else if(d!==c)if(a.hasValue(r)){const f=a.getValue(r);f.liveStyle===!0?f.jump(c):f.hasAnimated||f.set(c)}else{const f=a.getStaticValue(r);a.addValue(r,ai(f!==void 0?f:c,{owner:a}))}}for(const r in u)l[r]===void 0&&a.removeValue(r);return l}const ug=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class $T{scrapeMotionValuesFromProps(l,u,r){return{}}constructor({parent:l,props:u,presenceContext:r,reducedMotionConfig:c,blockInitialAnimation:d,visualState:f},m={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Ec,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const S=ce.now();this.renderScheduledAt<S&&(this.renderScheduledAt=S,wt.render(this.render,!1,!0))};const{latestValues:p,renderState:g}=f;this.latestValues=p,this.baseTarget={...p},this.initialValues=u.initial?{...p}:{},this.renderState=g,this.parent=l,this.props=u,this.presenceContext=r,this.depth=l?l.depth+1:0,this.reducedMotionConfig=c,this.options=m,this.blockInitialAnimation=!!d,this.isControllingVariants=au(u),this.isVariantNode=a0(u),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(l&&l.current);const{willChange:v,...x}=this.scrapeMotionValuesFromProps(u,{},this);for(const S in x){const V=x[S];p[S]!==void 0&&ne(V)&&V.set(p[S],!1)}}mount(l){this.current=l,PT.set(l,this),this.projection&&!this.projection.instance&&this.projection.mount(l),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((u,r)=>this.bindToMotionValue(r,u)),B0.current||FT(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:nc.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),Ln(this.notifyUpdate),Ln(this.render),this.valueSubscriptions.forEach(l=>l()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const l in this.events)this.events[l].clear();for(const l in this.features){const u=this.features[l];u&&(u.unmount(),u.isMounted=!1)}this.current=null}bindToMotionValue(l,u){this.valueSubscriptions.has(l)&&this.valueSubscriptions.get(l)();const r=ri.has(l);r&&this.onBindTransform&&this.onBindTransform();const c=u.on("change",m=>{this.latestValues[l]=m,this.props.onUpdate&&wt.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),d=u.on("renderRequest",this.scheduleRender);let f;window.MotionCheckAppearSync&&(f=window.MotionCheckAppearSync(this,l,u)),this.valueSubscriptions.set(l,()=>{c(),d(),f&&f(),u.owner&&u.stop()})}sortNodePosition(l){return!this.current||!this.sortInstanceNodePosition||this.type!==l.type?0:this.sortInstanceNodePosition(this.current,l.current)}updateFeatures(){let l="animation";for(l in ii){const u=ii[l];if(!u)continue;const{isEnabled:r,Feature:c}=u;if(!this.features[l]&&c&&r(this.props)&&(this.features[l]=new c(this)),this.features[l]){const d=this.features[l];d.isMounted?d.update():(d.mount(),d.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):Ut()}getStaticValue(l){return this.latestValues[l]}setStaticValue(l,u){this.latestValues[l]=u}update(l,u){(l.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=l,this.prevPresenceContext=this.presenceContext,this.presenceContext=u;for(let r=0;r<ug.length;r++){const c=ug[r];this.propEventSubscriptions[c]&&(this.propEventSubscriptions[c](),delete this.propEventSubscriptions[c]);const d="on"+c,f=l[d];f&&(this.propEventSubscriptions[c]=this.on(c,f))}this.prevMotionValues=JT(this,this.scrapeMotionValuesFromProps(l,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(l){return this.props.variants?this.props.variants[l]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(l){const u=this.getClosestVariantNode();if(u)return u.variantChildren&&u.variantChildren.add(l),()=>u.variantChildren.delete(l)}addValue(l,u){const r=this.values.get(l);u!==r&&(r&&this.removeValue(l),this.bindToMotionValue(l,u),this.values.set(l,u),this.latestValues[l]=u.get())}removeValue(l){this.values.delete(l);const u=this.valueSubscriptions.get(l);u&&(u(),this.valueSubscriptions.delete(l)),delete this.latestValues[l],this.removeValueFromRenderState(l,this.renderState)}hasValue(l){return this.values.has(l)}getValue(l,u){if(this.props.values&&this.props.values[l])return this.props.values[l];let r=this.values.get(l);return r===void 0&&u!==void 0&&(r=ai(u===null?void 0:u,{owner:this}),this.addValue(l,r)),r}readValue(l,u){let r=this.latestValues[l]!==void 0||!this.current?this.latestValues[l]:this.getBaseTargetFromProps(this.props,l)??this.readValueFromInstance(this.current,l,this.options);return r!=null&&(typeof r=="string"&&(fg(r)||hg(r))?r=parseFloat(r):!v1(r)&&Hn.test(u)&&(r=Fg(l,u)),this.setBaseTarget(l,ne(r)?r.get():r)),ne(r)?r.get():r}setBaseTarget(l,u){this.baseTarget[l]=u}getBaseTarget(l){const{initial:u}=this.props;let r;if(typeof u=="string"||typeof u=="object"){const d=Lc(this.props,u,this.presenceContext?.custom);d&&(r=d[l])}if(u&&r!==void 0)return r;const c=this.getBaseTargetFromProps(this.props,l);return c!==void 0&&!ne(c)?c:this.initialValues[l]!==void 0&&r===void 0?void 0:this.baseTarget[l]}on(l,u){return this.events[l]||(this.events[l]=new dc),this.events[l].add(u)}notify(l,...u){this.events[l]&&this.events[l].notify(...u)}}class U0 extends $T{constructor(){super(...arguments),this.KeyframeResolver=s1}sortInstanceNodePosition(l,u){return l.compareDocumentPosition(u)&2?1:-1}getBaseTargetFromProps(l,u){return l.style?l.style[u]:void 0}removeValueFromRenderState(l,{vars:u,style:r}){delete u[l],delete r[l]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:l}=this.props;ne(l)&&(this.childSubscription=l.on("change",u=>{this.current&&(this.current.textContent=`${u}`)}))}}function L0(a,{style:l,vars:u},r,c){const d=a.style;let f;for(f in l)d[f]=l[f];c?.applyProjectionStyles(d,r);for(f in u)d.setProperty(f,u[f])}function WT(a){return window.getComputedStyle(a)}class IT extends U0{constructor(){super(...arguments),this.type="html",this.renderInstance=L0}readValueFromInstance(l,u){if(ri.has(u))return this.projection?.isProjecting?Xo(u):Mx(l,u);{const r=WT(l),c=(pc(u)?r.getPropertyValue(u):r[u])||0;return typeof c=="string"?c.trim():c}}measureInstanceViewportBox(l,{transformPagePoint:u}){return A0(l,u)}build(l,u,r){_c(l,u,r.transformTemplate)}scrapeMotionValuesFromProps(l,u,r){return Hc(l,u,r)}}const H0=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function t2(a,l,u,r){L0(a,l,void 0,r);for(const c in l.attrs)a.setAttribute(H0.has(c)?c:Vc(c),l.attrs[c])}class e2 extends U0{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Ut}getBaseTargetFromProps(l,u){return l[u]}readValueFromInstance(l,u){if(ri.has(u)){const r=kg(u);return r&&r.default||0}return u=H0.has(u)?u:Vc(u),l.getAttribute(u)}scrapeMotionValuesFromProps(l,u,r){return h0(l,u,r)}build(l,u,r){o0(l,u,this.isSVGTag,r.transformTemplate,r.style)}renderInstance(l,u,r,c){t2(l,u,r,c)}mount(l){this.isSVGTag=f0(l.tagName),super.mount(l)}}const n2=(a,l)=>Uc(a)?new e2(l):new IT(l,{allowProjection:a!==W.Fragment}),a2=nS({...CS,...KT,..._T,...kT},n2),li=D1(a2),i2=({variant:a="primary",size:l="md",animation:u="fade-in",disabled:r=!1,text:c="Click me",onClick:d,className:f="",...m})=>{const p={primary:"bg-blue-600 hover:bg-blue-700 text-white border-blue-600",secondary:"bg-gray-600 hover:bg-gray-700 text-white border-gray-600",success:"bg-green-600 hover:bg-green-700 text-white border-green-600",danger:"bg-red-600 hover:bg-red-700 text-white border-red-600",warning:"bg-yellow-600 hover:bg-yellow-700 text-white border-yellow-600",outline:"bg-transparent hover:bg-blue-50 text-blue-600 border-blue-600",ghost:"bg-transparent hover:bg-gray-100 text-gray-700 border-transparent"},g={sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-base",lg:"px-6 py-3 text-lg",xl:"px-8 py-4 text-xl"},v={"fade-in":{initial:{opacity:0},animate:{opacity:1},transition:{duration:.5}},"slide-in":{initial:{y:-10,opacity:0},animate:{y:0,opacity:1},transition:{duration:.3}},"bounce-in":{initial:{scale:.3,opacity:0},animate:{scale:1,opacity:1},transition:{duration:.6,type:"spring",stiffness:260,damping:20}},"scale-in":{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},transition:{duration:.3}},none:{}},x=`
    inline-flex items-center justify-center
    font-medium rounded-lg border
    transition-all duration-200 ease-in-out
    focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
    disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-current
    ${p[a]}
    ${g[l]}
    ${f}
  `,S=v[u]||v["fade-in"];return y.jsx(li.button,{className:x,disabled:r,onClick:d,initial:S.initial,animate:S.animate,transition:S.transition,whileHover:r?{}:{scale:1.02},whileTap:r?{}:{scale:.98},...m,children:c})},l2=({size:a="md",animation:l="scale-in",backdrop:u=!0,closable:r=!0,title:c="Modal Title",content:d="This is modal content",className:f="",...m})=>{const[p,g]=W.useState(!1),v={sm:"max-w-sm",md:"max-w-md",lg:"max-w-lg",xl:"max-w-xl","2xl":"max-w-2xl",full:"max-w-full mx-4"},x={"fade-in":{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.3}},"scale-in":{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.8,opacity:0},transition:{duration:.3}},"slide-in":{initial:{y:-50,opacity:0},animate:{y:0,opacity:1},exit:{y:-50,opacity:0},transition:{duration:.3}},"bounce-in":{initial:{scale:.3,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.3,opacity:0},transition:{duration:.6,type:"spring",stiffness:260,damping:20}}},S=x[l]||x["scale-in"],V=j=>{j.target===j.currentTarget&&r&&g(!1)};return y.jsxs("div",{className:"space-y-4",children:[y.jsx("button",{onClick:()=>g(!0),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Open Modal"}),y.jsx(Oc,{children:p&&y.jsx(li.div,{className:`fixed inset-0 z-50 flex items-center justify-center p-4 ${u?"bg-black bg-opacity-50":""}`,onClick:V,initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2},children:y.jsxs(li.div,{className:`
                bg-white rounded-lg shadow-xl w-full
                ${v[a]}
                ${f}
              `,onClick:j=>j.stopPropagation(),initial:S.initial,animate:S.animate,exit:S.exit,transition:S.transition,...m,children:[y.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[y.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:c}),r&&y.jsx("button",{onClick:()=>g(!1),className:"text-gray-400 hover:text-gray-600 transition-colors",children:y.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:y.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),y.jsx("div",{className:"p-6",children:y.jsx("p",{className:"text-gray-600",children:d})}),y.jsxs("div",{className:"flex justify-end space-x-3 p-6 border-t border-gray-200",children:[y.jsx("button",{onClick:()=>g(!1),className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:"Cancel"}),y.jsx("button",{onClick:()=>g(!1),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Confirm"})]})]})})})]})},s2=({autoplay:a=!0,interval:l=3e3,showDots:u=!0,showArrows:r=!0,animation:c="slide",items:d=[],className:f="",...m})=>{const[p,g]=W.useState(0);W.useEffect(()=>{if(!a||d.length<=1)return;const Y=setInterval(()=>{g(X=>X===d.length-1?0:X+1)},l);return()=>clearInterval(Y)},[a,l,d.length]);const v={slide:{initial:Y=>({x:Y>0?300:-300,opacity:0}),animate:{x:0,opacity:1},exit:Y=>({x:Y<0?300:-300,opacity:0}),transition:{duration:.3,ease:"easeInOut"}},fade:{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.5}},scale:{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},exit:{scale:1.2,opacity:0},transition:{duration:.4}}},x=v[c]||v.slide,S=Y=>{g(Y)},V=()=>{g(p===0?d.length-1:p-1)},j=()=>{g(p===d.length-1?0:p+1)};return!d||d.length===0?y.jsx("div",{className:"w-full h-64 bg-gray-200 rounded-lg flex items-center justify-center",children:y.jsx("p",{className:"text-gray-500",children:"No items to display"})}):y.jsxs("div",{className:`relative w-full max-w-2xl mx-auto ${f}`,...m,children:[y.jsxs("div",{className:"relative h-64 overflow-hidden rounded-lg bg-gray-100",children:[y.jsx(Oc,{mode:"wait",custom:p,children:y.jsx(li.div,{custom:p,className:"absolute inset-0 flex items-center justify-center",initial:x.initial,animate:x.animate,exit:x.exit,transition:x.transition,children:d[p]?.image?y.jsx("img",{src:d[p].image,alt:d[p].content||`Slide ${p+1}`,className:"w-full h-full object-cover"}):y.jsx("div",{className:"w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-400 to-purple-500 text-white",children:y.jsxs("div",{className:"text-center",children:[y.jsx("h3",{className:"text-2xl font-bold mb-2",children:d[p]?.content||`Slide ${p+1}`}),y.jsx("p",{className:"text-blue-100",children:d[p]?.description||"Carousel slide content"})]})})},p)}),r&&d.length>1&&y.jsxs(y.Fragment,{children:[y.jsx("button",{onClick:V,className:"absolute left-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-2 shadow-lg transition-all duration-200",children:y.jsx("svg",{className:"w-5 h-5 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:y.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),y.jsx("button",{onClick:j,className:"absolute right-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-2 shadow-lg transition-all duration-200",children:y.jsx("svg",{className:"w-5 h-5 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:y.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]})]}),u&&d.length>1&&y.jsx("div",{className:"flex justify-center space-x-2 mt-4",children:d.map((Y,X)=>y.jsx("button",{onClick:()=>S(X),className:`w-3 h-3 rounded-full transition-all duration-200 ${X===p?"bg-blue-600 scale-110":"bg-gray-300 hover:bg-gray-400"}`},X))})]})},u2=({variant:a="default",shadow:l="md",animation:u="fade-in",title:r="Card Title",content:c="This is card content with some description text.",showImage:d=!0,imageUrl:f="https://via.placeholder.com/300x150/6366F1/white?text=Card+Image",className:m="",...p})=>{const g={default:"bg-white border border-gray-200",elevated:"bg-white border-0",outlined:"bg-white border-2 border-gray-300",filled:"bg-gray-50 border border-gray-200",gradient:"bg-gradient-to-br from-blue-50 to-indigo-100 border border-blue-200"},v={none:"shadow-none",sm:"shadow-sm",md:"shadow-md",lg:"shadow-lg",xl:"shadow-xl","2xl":"shadow-2xl"},x={"fade-in":{initial:{opacity:0},animate:{opacity:1},transition:{duration:.5}},"slide-in":{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{duration:.4}},"scale-in":{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},transition:{duration:.3}},"bounce-in":{initial:{scale:.3,opacity:0},animate:{scale:1,opacity:1},transition:{duration:.6,type:"spring",stiffness:260,damping:20}},none:{}},S=x[u]||x["fade-in"],V=`
    rounded-lg overflow-hidden transition-all duration-200
    hover:shadow-lg hover:scale-105
    ${g[a]}
    ${v[l]}
    ${m}
  `;return y.jsxs(li.div,{className:V,initial:S.initial,animate:S.animate,transition:S.transition,...p,children:[d&&f&&y.jsx("div",{className:"aspect-video w-full overflow-hidden",children:y.jsx("img",{src:f,alt:r,className:"w-full h-full object-cover transition-transform duration-300 hover:scale-110"})}),y.jsxs("div",{className:"p-6",children:[y.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:r}),y.jsx("p",{className:"text-gray-600 mb-4 leading-relaxed",children:c}),y.jsxs("div",{className:"flex space-x-3",children:[y.jsx("button",{className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium",children:"Learn More"}),y.jsx("button",{className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-sm font-medium",children:"Share"})]})]})]})},r2=({type:a="success",position:l="top-right",animation:u="slide-in",autoClose:r=!0,duration:c=3e3,message:d="This is a toast message!",className:f="",...m})=>{const[p,g]=W.useState(!1),v={success:{bg:"bg-green-500",text:"text-white",icon:y.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:y.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})},error:{bg:"bg-red-500",text:"text-white",icon:y.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:y.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})},warning:{bg:"bg-yellow-500",text:"text-white",icon:y.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:y.jsx("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})},info:{bg:"bg-blue-500",text:"text-white",icon:y.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:y.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}},x={"top-left":"top-4 left-4","top-center":"top-4 left-1/2 transform -translate-x-1/2","top-right":"top-4 right-4","bottom-left":"bottom-4 left-4","bottom-center":"bottom-4 left-1/2 transform -translate-x-1/2","bottom-right":"bottom-4 right-4"},S={"slide-in":{initial:{x:l.includes("right")?300:l.includes("left")?-300:0,opacity:0},animate:{x:0,opacity:1},exit:{x:l.includes("right")?300:l.includes("left")?-300:0,opacity:0},transition:{duration:.3}},"fade-in":{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.3}},"scale-in":{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.8,opacity:0},transition:{duration:.3}},"bounce-in":{initial:{scale:.3,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.3,opacity:0},transition:{duration:.6,type:"spring",stiffness:260,damping:20}}},V=S[u]||S["slide-in"],j=v[a]||v.success;W.useEffect(()=>{if(!r||!p)return;const Q=setTimeout(()=>{g(!1)},c);return()=>clearTimeout(Q)},[r,c,p]);const Y=()=>{g(!0)},X=()=>{g(!1)};return y.jsxs("div",{className:"space-y-4",children:[y.jsx("button",{onClick:Y,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Show Toast"}),y.jsx(Oc,{children:p&&y.jsx(li.div,{className:`
              fixed z-50 max-w-sm w-full
              ${x[l]}
            `,initial:V.initial,animate:V.animate,exit:V.exit,transition:V.transition,children:y.jsxs("div",{className:`
                flex items-center p-4 rounded-lg shadow-lg
                ${j.bg} ${j.text}
                ${f}
              `,...m,children:[y.jsx("div",{className:"flex-shrink-0 mr-3",children:j.icon}),y.jsx("div",{className:"flex-1 text-sm font-medium",children:d}),y.jsx("button",{onClick:X,className:"flex-shrink-0 ml-3 text-current hover:text-opacity-75 transition-opacity",children:y.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:y.jsx("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]})})})]})},o2=()=>{const{selectedComponent:a,componentConfigs:l}=ic(),u=l[a],r=()=>{switch(a){case"Button":return y.jsx(i2,{variant:u.variant,size:u.size,animation:u.animation,disabled:u.disabled,text:u.text,onClick:()=>alert("Button clicked!")});case"Modal":return y.jsx(l2,{size:u.size,animation:u.animation,backdrop:u.backdrop,closable:u.closable,title:u.title,content:u.content});case"Carousel":return y.jsx(s2,{autoplay:u.autoplay,interval:u.interval,showDots:u.showDots,showArrows:u.showArrows,animation:u.animation,items:u.items});case"Card":return y.jsx(u2,{variant:u.variant,shadow:u.shadow,animation:u.animation,title:u.title,content:u.content,showImage:u.showImage,imageUrl:u.imageUrl});case"Toast":return y.jsx(r2,{type:u.type,position:u.position,animation:u.animation,autoClose:u.autoClose,duration:u.duration,message:u.message});default:return y.jsx("div",{children:"Component not found"})}};return y.jsx("div",{className:"flex-1 bg-gray-50 p-8 overflow-y-auto",children:y.jsxs("div",{className:"max-w-4xl mx-auto",children:[y.jsxs("div",{className:"mb-6",children:[y.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Live Preview"}),y.jsxs("p",{className:"text-gray-600",children:["See your ",a," component in action. Changes are reflected in real-time."]})]}),y.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8 min-h-96 flex items-center justify-center",children:y.jsx("div",{className:"w-full max-w-lg",children:r()})}),y.jsxs("div",{className:"mt-6 bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[y.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Component Configuration"}),y.jsx("div",{className:"bg-gray-50 rounded-md p-4",children:y.jsx("pre",{className:"text-sm text-gray-700 overflow-x-auto",children:JSON.stringify(u,null,2)})})]})]})})};var Bo={exports:{}},rg;function c2(){return rg||(rg=1,function(a){var l=typeof window<"u"?window:typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope?self:{};/**
 * Prism: Lightweight, robust, elegant syntax highlighting
 *
 * @license MIT <https://opensource.org/licenses/MIT>
 * <AUTHOR> Verou <https://lea.verou.me>
 * @namespace
 * @public
 */var u=function(r){var c=/(?:^|\s)lang(?:uage)?-([\w-]+)(?=\s|$)/i,d=0,f={},m={manual:r.Prism&&r.Prism.manual,disableWorkerMessageHandler:r.Prism&&r.Prism.disableWorkerMessageHandler,util:{encode:function C(M){return M instanceof p?new p(M.type,C(M.content),M.alias):Array.isArray(M)?M.map(C):M.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(C){return Object.prototype.toString.call(C).slice(8,-1)},objId:function(C){return C.__id||Object.defineProperty(C,"__id",{value:++d}),C.__id},clone:function C(M,_){_=_||{};var O,B;switch(m.util.type(M)){case"Object":if(B=m.util.objId(M),_[B])return _[B];O={},_[B]=O;for(var Z in M)M.hasOwnProperty(Z)&&(O[Z]=C(M[Z],_));return O;case"Array":return B=m.util.objId(M),_[B]?_[B]:(O=[],_[B]=O,M.forEach(function(K,F){O[F]=C(K,_)}),O);default:return M}},getLanguage:function(C){for(;C;){var M=c.exec(C.className);if(M)return M[1].toLowerCase();C=C.parentElement}return"none"},setLanguage:function(C,M){C.className=C.className.replace(RegExp(c,"gi"),""),C.classList.add("language-"+M)},currentScript:function(){if(typeof document>"u")return null;if(document.currentScript&&document.currentScript.tagName==="SCRIPT")return document.currentScript;try{throw new Error}catch(O){var C=(/at [^(\r\n]*\((.*):[^:]+:[^:]+\)$/i.exec(O.stack)||[])[1];if(C){var M=document.getElementsByTagName("script");for(var _ in M)if(M[_].src==C)return M[_]}return null}},isActive:function(C,M,_){for(var O="no-"+M;C;){var B=C.classList;if(B.contains(M))return!0;if(B.contains(O))return!1;C=C.parentElement}return!!_}},languages:{plain:f,plaintext:f,text:f,txt:f,extend:function(C,M){var _=m.util.clone(m.languages[C]);for(var O in M)_[O]=M[O];return _},insertBefore:function(C,M,_,O){O=O||m.languages;var B=O[C],Z={};for(var K in B)if(B.hasOwnProperty(K)){if(K==M)for(var F in _)_.hasOwnProperty(F)&&(Z[F]=_[F]);_.hasOwnProperty(K)||(Z[K]=B[K])}var ut=O[C];return O[C]=Z,m.languages.DFS(m.languages,function(pt,St){St===ut&&pt!=C&&(this[pt]=Z)}),Z},DFS:function C(M,_,O,B){B=B||{};var Z=m.util.objId;for(var K in M)if(M.hasOwnProperty(K)){_.call(M,K,M[K],O||K);var F=M[K],ut=m.util.type(F);ut==="Object"&&!B[Z(F)]?(B[Z(F)]=!0,C(F,_,null,B)):ut==="Array"&&!B[Z(F)]&&(B[Z(F)]=!0,C(F,_,K,B))}}},plugins:{},highlightAll:function(C,M){m.highlightAllUnder(document,C,M)},highlightAllUnder:function(C,M,_){var O={callback:_,container:C,selector:'code[class*="language-"], [class*="language-"] code, code[class*="lang-"], [class*="lang-"] code'};m.hooks.run("before-highlightall",O),O.elements=Array.prototype.slice.apply(O.container.querySelectorAll(O.selector)),m.hooks.run("before-all-elements-highlight",O);for(var B=0,Z;Z=O.elements[B++];)m.highlightElement(Z,M===!0,O.callback)},highlightElement:function(C,M,_){var O=m.util.getLanguage(C),B=m.languages[O];m.util.setLanguage(C,O);var Z=C.parentElement;Z&&Z.nodeName.toLowerCase()==="pre"&&m.util.setLanguage(Z,O);var K=C.textContent,F={element:C,language:O,grammar:B,code:K};function ut(St){F.highlightedCode=St,m.hooks.run("before-insert",F),F.element.innerHTML=F.highlightedCode,m.hooks.run("after-highlight",F),m.hooks.run("complete",F),_&&_.call(F.element)}if(m.hooks.run("before-sanity-check",F),Z=F.element.parentElement,Z&&Z.nodeName.toLowerCase()==="pre"&&!Z.hasAttribute("tabindex")&&Z.setAttribute("tabindex","0"),!F.code){m.hooks.run("complete",F),_&&_.call(F.element);return}if(m.hooks.run("before-highlight",F),!F.grammar){ut(m.util.encode(F.code));return}if(M&&r.Worker){var pt=new Worker(m.filename);pt.onmessage=function(St){ut(St.data)},pt.postMessage(JSON.stringify({language:F.language,code:F.code,immediateClose:!0}))}else ut(m.highlight(F.code,F.grammar,F.language))},highlight:function(C,M,_){var O={code:C,grammar:M,language:_};if(m.hooks.run("before-tokenize",O),!O.grammar)throw new Error('The language "'+O.language+'" has no grammar.');return O.tokens=m.tokenize(O.code,O.grammar),m.hooks.run("after-tokenize",O),p.stringify(m.util.encode(O.tokens),O.language)},tokenize:function(C,M){var _=M.rest;if(_){for(var O in _)M[O]=_[O];delete M.rest}var B=new x;return S(B,B.head,C),v(C,B,M,B.head,0),j(B)},hooks:{all:{},add:function(C,M){var _=m.hooks.all;_[C]=_[C]||[],_[C].push(M)},run:function(C,M){var _=m.hooks.all[C];if(!(!_||!_.length))for(var O=0,B;B=_[O++];)B(M)}},Token:p};r.Prism=m;function p(C,M,_,O){this.type=C,this.content=M,this.alias=_,this.length=(O||"").length|0}p.stringify=function C(M,_){if(typeof M=="string")return M;if(Array.isArray(M)){var O="";return M.forEach(function(ut){O+=C(ut,_)}),O}var B={type:M.type,content:C(M.content,_),tag:"span",classes:["token",M.type],attributes:{},language:_},Z=M.alias;Z&&(Array.isArray(Z)?Array.prototype.push.apply(B.classes,Z):B.classes.push(Z)),m.hooks.run("wrap",B);var K="";for(var F in B.attributes)K+=" "+F+'="'+(B.attributes[F]||"").replace(/"/g,"&quot;")+'"';return"<"+B.tag+' class="'+B.classes.join(" ")+'"'+K+">"+B.content+"</"+B.tag+">"};function g(C,M,_,O){C.lastIndex=M;var B=C.exec(_);if(B&&O&&B[1]){var Z=B[1].length;B.index+=Z,B[0]=B[0].slice(Z)}return B}function v(C,M,_,O,B,Z){for(var K in _)if(!(!_.hasOwnProperty(K)||!_[K])){var F=_[K];F=Array.isArray(F)?F:[F];for(var ut=0;ut<F.length;++ut){if(Z&&Z.cause==K+","+ut)return;var pt=F[ut],St=pt.inside,Se=!!pt.lookbehind,fe=!!pt.greedy,Yt=pt.alias;if(fe&&!pt.pattern.global){var U=pt.pattern.toString().match(/[imsuy]*$/)[0];pt.pattern=RegExp(pt.pattern.source,U+"g")}for(var k=pt.pattern||pt,J=O.next,rt=B;J!==M.tail&&!(Z&&rt>=Z.reach);rt+=J.value.length,J=J.next){var A=J.value;if(M.length>C.length)return;if(!(A instanceof p)){var G=1,P;if(fe){if(P=g(k,rt,C,Se),!P||P.index>=C.length)break;var it=P.index,$=P.index+P[0].length,tt=rt;for(tt+=J.value.length;it>=tt;)J=J.next,tt+=J.value.length;if(tt-=J.value.length,rt=tt,J.value instanceof p)continue;for(var ft=J;ft!==M.tail&&(tt<$||typeof ft.value=="string");ft=ft.next)G++,tt+=ft.value.length;G--,A=C.slice(rt,tt),P.index-=rt}else if(P=g(k,0,A,Se),!P)continue;var it=P.index,Zt=P[0],Tt=A.slice(0,it),Ve=A.slice(it+Zt.length),Yn=rt+A.length;Z&&Yn>Z.reach&&(Z.reach=Yn);var Fe=J.prev;Tt&&(Fe=S(M,Fe,Tt),rt+=Tt.length),V(M,Fe,G);var oi=new p(K,St?m.tokenize(Zt,St):Zt,Yt,Zt);if(J=S(M,Fe,oi),Ve&&S(M,J,Ve),G>1){var Gn={cause:K+","+ut,reach:Yn};v(C,M,_,J.prev,rt,Gn),Z&&Gn.reach>Z.reach&&(Z.reach=Gn.reach)}}}}}}function x(){var C={value:null,prev:null,next:null},M={value:null,prev:C,next:null};C.next=M,this.head=C,this.tail=M,this.length=0}function S(C,M,_){var O=M.next,B={value:_,prev:M,next:O};return M.next=B,O.prev=B,C.length++,B}function V(C,M,_){for(var O=M.next,B=0;B<_&&O!==C.tail;B++)O=O.next;M.next=O,O.prev=M,C.length-=B}function j(C){for(var M=[],_=C.head.next;_!==C.tail;)M.push(_.value),_=_.next;return M}if(!r.document)return r.addEventListener&&(m.disableWorkerMessageHandler||r.addEventListener("message",function(C){var M=JSON.parse(C.data),_=M.language,O=M.code,B=M.immediateClose;r.postMessage(m.highlight(O,m.languages[_],_)),B&&r.close()},!1)),m;var Y=m.util.currentScript();Y&&(m.filename=Y.src,Y.hasAttribute("data-manual")&&(m.manual=!0));function X(){m.manual||m.highlightAll()}if(!m.manual){var Q=document.readyState;Q==="loading"||Q==="interactive"&&Y&&Y.defer?document.addEventListener("DOMContentLoaded",X):window.requestAnimationFrame?window.requestAnimationFrame(X):window.setTimeout(X,16)}return m}(l);a.exports&&(a.exports=u),typeof Vm<"u"&&(Vm.Prism=u),u.languages.markup={comment:{pattern:/<!--(?:(?!<!--)[\s\S])*?-->/,greedy:!0},prolog:{pattern:/<\?[\s\S]+?\?>/,greedy:!0},doctype:{pattern:/<!DOCTYPE(?:[^>"'[\]]|"[^"]*"|'[^']*')+(?:\[(?:[^<"'\]]|"[^"]*"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\]\s*)?>/i,greedy:!0,inside:{"internal-subset":{pattern:/(^[^\[]*\[)[\s\S]+(?=\]>$)/,lookbehind:!0,greedy:!0,inside:null},string:{pattern:/"[^"]*"|'[^']*'/,greedy:!0},punctuation:/^<!|>$|[[\]]/,"doctype-tag":/^DOCTYPE/i,name:/[^\s<>'"]+/}},cdata:{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,greedy:!0},tag:{pattern:/<\/?(?!\d)[^\s>\/=$<%]+(?:\s(?:\s*[^\s>\/=]+(?:\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))|(?=[\s/>])))+)?\s*\/?>/,greedy:!0,inside:{tag:{pattern:/^<\/?[^\s>\/]+/,inside:{punctuation:/^<\/?/,namespace:/^[^\s>\/:]+:/}},"special-attr":[],"attr-value":{pattern:/=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+)/,inside:{punctuation:[{pattern:/^=/,alias:"attr-equals"},{pattern:/^(\s*)["']|["']$/,lookbehind:!0}]}},punctuation:/\/?>/,"attr-name":{pattern:/[^\s>\/]+/,inside:{namespace:/^[^\s>\/:]+:/}}}},entity:[{pattern:/&[\da-z]{1,8};/i,alias:"named-entity"},/&#x?[\da-f]{1,8};/i]},u.languages.markup.tag.inside["attr-value"].inside.entity=u.languages.markup.entity,u.languages.markup.doctype.inside["internal-subset"].inside=u.languages.markup,u.hooks.add("wrap",function(r){r.type==="entity"&&(r.attributes.title=r.content.replace(/&amp;/,"&"))}),Object.defineProperty(u.languages.markup.tag,"addInlined",{value:function(c,d){var f={};f["language-"+d]={pattern:/(^<!\[CDATA\[)[\s\S]+?(?=\]\]>$)/i,lookbehind:!0,inside:u.languages[d]},f.cdata=/^<!\[CDATA\[|\]\]>$/i;var m={"included-cdata":{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,inside:f}};m["language-"+d]={pattern:/[\s\S]+/,inside:u.languages[d]};var p={};p[c]={pattern:RegExp(/(<__[^>]*>)(?:<!\[CDATA\[(?:[^\]]|\](?!\]>))*\]\]>|(?!<!\[CDATA\[)[\s\S])*?(?=<\/__>)/.source.replace(/__/g,function(){return c}),"i"),lookbehind:!0,greedy:!0,inside:m},u.languages.insertBefore("markup","cdata",p)}}),Object.defineProperty(u.languages.markup.tag,"addAttribute",{value:function(r,c){u.languages.markup.tag.inside["special-attr"].push({pattern:RegExp(/(^|["'\s])/.source+"(?:"+r+")"+/\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))/.source,"i"),lookbehind:!0,inside:{"attr-name":/^[^\s=]+/,"attr-value":{pattern:/=[\s\S]+/,inside:{value:{pattern:/(^=\s*(["']|(?!["'])))\S[\s\S]*(?=\2$)/,lookbehind:!0,alias:[c,"language-"+c],inside:u.languages[c]},punctuation:[{pattern:/^=/,alias:"attr-equals"},/"|'/]}}}})}}),u.languages.html=u.languages.markup,u.languages.mathml=u.languages.markup,u.languages.svg=u.languages.markup,u.languages.xml=u.languages.extend("markup",{}),u.languages.ssml=u.languages.xml,u.languages.atom=u.languages.xml,u.languages.rss=u.languages.xml,function(r){var c=/(?:"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"|'(?:\\(?:\r\n|[\s\S])|[^'\\\r\n])*')/;r.languages.css={comment:/\/\*[\s\S]*?\*\//,atrule:{pattern:RegExp("@[\\w-](?:"+/[^;{\s"']|\s+(?!\s)/.source+"|"+c.source+")*?"+/(?:;|(?=\s*\{))/.source),inside:{rule:/^@[\w-]+/,"selector-function-argument":{pattern:/(\bselector\s*\(\s*(?![\s)]))(?:[^()\s]|\s+(?![\s)])|\((?:[^()]|\([^()]*\))*\))+(?=\s*\))/,lookbehind:!0,alias:"selector"},keyword:{pattern:/(^|[^\w-])(?:and|not|only|or)(?![\w-])/,lookbehind:!0}}},url:{pattern:RegExp("\\burl\\((?:"+c.source+"|"+/(?:[^\\\r\n()"']|\\[\s\S])*/.source+")\\)","i"),greedy:!0,inside:{function:/^url/i,punctuation:/^\(|\)$/,string:{pattern:RegExp("^"+c.source+"$"),alias:"url"}}},selector:{pattern:RegExp(`(^|[{}\\s])[^{}\\s](?:[^{};"'\\s]|\\s+(?![\\s{])|`+c.source+")*(?=\\s*\\{)"),lookbehind:!0},string:{pattern:c,greedy:!0},property:{pattern:/(^|[^-\w\xA0-\uFFFF])(?!\s)[-_a-z\xA0-\uFFFF](?:(?!\s)[-\w\xA0-\uFFFF])*(?=\s*:)/i,lookbehind:!0},important:/!important\b/i,function:{pattern:/(^|[^-a-z0-9])[-a-z0-9]+(?=\()/i,lookbehind:!0},punctuation:/[(){};:,]/},r.languages.css.atrule.inside.rest=r.languages.css;var d=r.languages.markup;d&&(d.tag.addInlined("style","css"),d.tag.addAttribute("style","css"))}(u),u.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0,greedy:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/(\b(?:class|extends|implements|instanceof|interface|new|trait)\s+|\bcatch\s+\()[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:break|catch|continue|do|else|finally|for|function|if|in|instanceof|new|null|return|throw|try|while)\b/,boolean:/\b(?:false|true)\b/,function:/\b\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/[<>]=?|[!=]=?=?|--?|\+\+?|&&?|\|\|?|[?*/~^%]/,punctuation:/[{}[\];(),.:]/},u.languages.javascript=u.languages.extend("clike",{"class-name":[u.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$A-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\.(?:constructor|prototype))/,lookbehind:!0}],keyword:[{pattern:/((?:^|\})\s*)catch\b/,lookbehind:!0},{pattern:/(^|[^.]|\.\.\.\s*)\b(?:as|assert(?=\s*\{)|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\s*(?:\{|$))|for|from(?=\s*(?:['"]|$))|function|(?:get|set)(?=\s*(?:[#\[$\w\xA0-\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],function:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,number:{pattern:RegExp(/(^|[^\w$])/.source+"(?:"+(/NaN|Infinity/.source+"|"+/0[bB][01]+(?:_[01]+)*n?/.source+"|"+/0[oO][0-7]+(?:_[0-7]+)*n?/.source+"|"+/0[xX][\dA-Fa-f]+(?:_[\dA-Fa-f]+)*n?/.source+"|"+/\d+(?:_\d+)*n/.source+"|"+/(?:\d+(?:_\d+)*(?:\.(?:\d+(?:_\d+)*)?)?|\.\d+(?:_\d+)*)(?:[Ee][+-]?\d+(?:_\d+)*)?/.source)+")"+/(?![\w$])/.source),lookbehind:!0},operator:/--|\+\+|\*\*=?|=>|&&=?|\|\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\.{3}|\?\?=?|\?\.?|[~:]/}),u.languages.javascript["class-name"][0].pattern=/(\b(?:class|extends|implements|instanceof|interface|new)\s+)[\w.\\]+/,u.languages.insertBefore("javascript","keyword",{regex:{pattern:RegExp(/((?:^|[^$\w\xA0-\uFFFF."'\])\s]|\b(?:return|yield))\s*)/.source+/\//.source+"(?:"+/(?:\[(?:[^\]\\\r\n]|\\.)*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}/.source+"|"+/(?:\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.)*\])*\])*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}v[dgimyus]{0,7}/.source+")"+/(?=(?:\s|\/\*(?:[^*]|\*(?!\/))*\*\/)*(?:$|[\r\n,.;:})\]]|\/\/))/.source),lookbehind:!0,greedy:!0,inside:{"regex-source":{pattern:/^(\/)[\s\S]+(?=\/[a-z]*$)/,lookbehind:!0,alias:"language-regex",inside:u.languages.regex},"regex-delimiter":/^\/|\/$/,"regex-flags":/^[a-z]+$/}},"function-variable":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)?\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\))/,lookbehind:!0,inside:u.languages.javascript},{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$a-z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*=>)/i,lookbehind:!0,inside:u.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*=>)/,lookbehind:!0,inside:u.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*)\(\s*|\]\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*\{)/,lookbehind:!0,inside:u.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/}),u.languages.insertBefore("javascript","string",{hashbang:{pattern:/^#!.*/,greedy:!0,alias:"comment"},"template-string":{pattern:/`(?:\\[\s\S]|\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}|(?!\$\{)[^\\`])*`/,greedy:!0,inside:{"template-punctuation":{pattern:/^`|`$/,alias:"string"},interpolation:{pattern:/((?:^|[^\\])(?:\\{2})*)\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}/,lookbehind:!0,inside:{"interpolation-punctuation":{pattern:/^\$\{|\}$/,alias:"punctuation"},rest:u.languages.javascript}},string:/[\s\S]+/}},"string-property":{pattern:/((?:^|[,{])[ \t]*)(["'])(?:\\(?:\r\n|[\s\S])|(?!\2)[^\\\r\n])*\2(?=\s*:)/m,lookbehind:!0,greedy:!0,alias:"property"}}),u.languages.insertBefore("javascript","operator",{"literal-property":{pattern:/((?:^|[,{])[ \t]*)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*:)/m,lookbehind:!0,alias:"property"}}),u.languages.markup&&(u.languages.markup.tag.addInlined("script","javascript"),u.languages.markup.tag.addAttribute(/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source,"javascript")),u.languages.js=u.languages.javascript,function(){if(typeof u>"u"||typeof document>"u")return;Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector);var r="Loading…",c=function(Y,X){return"✖ Error "+Y+" while fetching file: "+X},d="✖ Error: File does not exist or is empty",f={js:"javascript",py:"python",rb:"ruby",ps1:"powershell",psm1:"powershell",sh:"bash",bat:"batch",h:"c",tex:"latex"},m="data-src-status",p="loading",g="loaded",v="failed",x="pre[data-src]:not(["+m+'="'+g+'"]):not(['+m+'="'+p+'"])';function S(Y,X,Q){var C=new XMLHttpRequest;C.open("GET",Y,!0),C.onreadystatechange=function(){C.readyState==4&&(C.status<400&&C.responseText?X(C.responseText):C.status>=400?Q(c(C.status,C.statusText)):Q(d))},C.send(null)}function V(Y){var X=/^\s*(\d+)\s*(?:(,)\s*(?:(\d+)\s*)?)?$/.exec(Y||"");if(X){var Q=Number(X[1]),C=X[2],M=X[3];return C?M?[Q,Number(M)]:[Q,void 0]:[Q,Q]}}u.hooks.add("before-highlightall",function(Y){Y.selector+=", "+x}),u.hooks.add("before-sanity-check",function(Y){var X=Y.element;if(X.matches(x)){Y.code="",X.setAttribute(m,p);var Q=X.appendChild(document.createElement("CODE"));Q.textContent=r;var C=X.getAttribute("data-src"),M=Y.language;if(M==="none"){var _=(/\.(\w+)$/.exec(C)||[,"none"])[1];M=f[_]||_}u.util.setLanguage(Q,M),u.util.setLanguage(X,M);var O=u.plugins.autoloader;O&&O.loadLanguages(M),S(C,function(B){X.setAttribute(m,g);var Z=V(X.getAttribute("data-range"));if(Z){var K=B.split(/\r\n?|\n/g),F=Z[0],ut=Z[1]==null?K.length:Z[1];F<0&&(F+=K.length),F=Math.max(0,Math.min(F-1,K.length)),ut<0&&(ut+=K.length),ut=Math.max(0,Math.min(ut,K.length)),B=K.slice(F,ut).join(`
`),X.hasAttribute("data-start")||X.setAttribute("data-start",String(F+1))}Q.textContent=B,u.highlightElement(Q)},function(B){X.setAttribute(m,v),Q.textContent=B})}}),u.plugins.fileHighlight={highlight:function(X){for(var Q=(X||document).querySelectorAll(x),C=0,M;M=Q[C++];)u.highlightElement(M)}};var j=!1;u.fileHighlight=function(){j||(console.warn("Prism.fileHighlight is deprecated. Use `Prism.plugins.fileHighlight.highlight` instead."),j=!0),u.plugins.fileHighlight.highlight.apply(this,arguments)}}()}(Bo)),Bo.exports}var f2=c2();const d2=og(f2);(function(a){var l=a.util.clone(a.languages.javascript),u=/(?:\s|\/\/.*(?!.)|\/\*(?:[^*]|\*(?!\/))\*\/)/.source,r=/(?:\{(?:\{(?:\{[^{}]*\}|[^{}])*\}|[^{}])*\})/.source,c=/(?:\{<S>*\.{3}(?:[^{}]|<BRACES>)*\})/.source;function d(p,g){return p=p.replace(/<S>/g,function(){return u}).replace(/<BRACES>/g,function(){return r}).replace(/<SPREAD>/g,function(){return c}),RegExp(p,g)}c=d(c).source,a.languages.jsx=a.languages.extend("markup",l),a.languages.jsx.tag.pattern=d(/<\/?(?:[\w.:-]+(?:<S>+(?:[\w.:$-]+(?:=(?:"(?:\\[\s\S]|[^\\"])*"|'(?:\\[\s\S]|[^\\'])*'|[^\s{'"/>=]+|<BRACES>))?|<SPREAD>))*<S>*\/?)?>/.source),a.languages.jsx.tag.inside.tag.pattern=/^<\/?[^\s>\/]*/,a.languages.jsx.tag.inside["attr-value"].pattern=/=(?!\{)(?:"(?:\\[\s\S]|[^\\"])*"|'(?:\\[\s\S]|[^\\'])*'|[^\s'">]+)/,a.languages.jsx.tag.inside.tag.inside["class-name"]=/^[A-Z]\w*(?:\.[A-Z]\w*)*$/,a.languages.jsx.tag.inside.comment=l.comment,a.languages.insertBefore("inside","attr-name",{spread:{pattern:d(/<SPREAD>/.source),inside:a.languages.jsx}},a.languages.jsx.tag),a.languages.insertBefore("inside","special-attr",{script:{pattern:d(/=<BRACES>/.source),alias:"language-javascript",inside:{"script-punctuation":{pattern:/^=(?=\{)/,alias:"punctuation"},rest:a.languages.jsx}}},a.languages.jsx.tag);var f=function(p){return p?typeof p=="string"?p:typeof p.content=="string"?p.content:p.content.map(f).join(""):""},m=function(p){for(var g=[],v=0;v<p.length;v++){var x=p[v],S=!1;if(typeof x!="string"&&(x.type==="tag"&&x.content[0]&&x.content[0].type==="tag"?x.content[0].content[0].content==="</"?g.length>0&&g[g.length-1].tagName===f(x.content[0].content[1])&&g.pop():x.content[x.content.length-1].content==="/>"||g.push({tagName:f(x.content[0].content[1]),openedBraces:0}):g.length>0&&x.type==="punctuation"&&x.content==="{"?g[g.length-1].openedBraces++:g.length>0&&g[g.length-1].openedBraces>0&&x.type==="punctuation"&&x.content==="}"?g[g.length-1].openedBraces--:S=!0),(S||typeof x=="string")&&g.length>0&&g[g.length-1].openedBraces===0){var V=f(x);v<p.length-1&&(typeof p[v+1]=="string"||p[v+1].type==="plain-text")&&(V+=f(p[v+1]),p.splice(v+1,1)),v>0&&(typeof p[v-1]=="string"||p[v-1].type==="plain-text")&&(V=f(p[v-1])+V,p.splice(v-1,1),v--),p[v]=new a.Token("plain-text",V,null,V)}x.content&&typeof x.content!="string"&&m(x.content)}};a.hooks.add("after-tokenize",function(p){p.language!=="jsx"&&p.language!=="tsx"||m(p.tokens)})})(Prism);const h2=()=>{const{selectedComponent:a,componentConfigs:l}=ic(),u=l[a],r=W.useRef(null),c=()=>{const f=v=>typeof v=="string"?`"${v}"`:typeof v=="boolean"?v?"{true}":"{false}":typeof v=="number"?`{${v}}`:Array.isArray(v)?`{${JSON.stringify(v,null,2)}}`:`{${JSON.stringify(v)}}`,p=(v=>Object.entries(v).filter(([x,S])=>({Button:{variant:"primary",size:"md",animation:"fade-in",disabled:!1,text:"Click me"},Modal:{size:"md",animation:"scale-in",backdrop:!0,closable:!0,title:"Modal Title",content:"This is modal content"},Carousel:{autoplay:!0,interval:3e3,showDots:!0,showArrows:!0,animation:"slide"},Card:{variant:"default",shadow:"md",animation:"fade-in",title:"Card Title",content:"This is card content with some description text.",showImage:!0},Toast:{type:"success",position:"top-right",animation:"slide-in",autoClose:!0,duration:3e3,message:"This is a toast message!"}}[a]||{})[x]!==S).map(([x,S])=>`  ${x}=${f(S)}`).join(`
`))(u),g=a;return p?`import { ${g} } from './components';

const MyComponent = () => {
  return (
    <${g}
${p}
    />
  );
};

export default MyComponent;`:`import { ${g} } from './components';

const MyComponent = () => {
  return <${g} />;
};

export default MyComponent;`},d=async()=>{try{await navigator.clipboard.writeText(c()),alert("Code copied to clipboard!")}catch(f){console.error("Failed to copy code: ",f)}};return W.useEffect(()=>{r.current&&d2.highlightElement(r.current)},[a,u]),y.jsx("div",{className:"w-96 bg-white border-l border-gray-200 p-6 overflow-y-auto",children:y.jsxs("div",{className:"mb-6",children:[y.jsxs("div",{className:"flex items-center justify-between mb-4",children:[y.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Generated Code"}),y.jsxs("button",{onClick:d,className:"px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors flex items-center space-x-1",children:[y.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:y.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})}),y.jsx("span",{children:"Copy"})]})]}),y.jsxs("div",{className:"bg-gray-900 rounded-lg overflow-hidden",children:[y.jsxs("div",{className:"bg-gray-800 px-4 py-2 text-sm text-gray-300 border-b border-gray-700",children:[a,".jsx"]}),y.jsx("pre",{className:"p-4 overflow-x-auto",children:y.jsx("code",{ref:r,className:"language-jsx text-sm",children:c()})})]}),y.jsxs("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[y.jsx("h3",{className:"text-sm font-semibold text-blue-900 mb-2",children:"Usage Instructions"}),y.jsxs("div",{className:"text-sm text-blue-800 space-y-1",children:[y.jsx("p",{children:"1. Copy the generated code above"}),y.jsx("p",{children:"2. Create a new component file in your project"}),y.jsx("p",{children:"3. Paste the code and customize as needed"}),y.jsx("p",{children:"4. Make sure to install the required dependencies:"}),y.jsx("code",{className:"block mt-2 p-2 bg-blue-100 rounded text-xs",children:"npm install framer-motion tailwindcss"})]})]}),y.jsxs("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg",children:[y.jsx("h3",{className:"text-sm font-semibold text-gray-900 mb-2",children:"Available Props"}),y.jsxs("div",{className:"text-xs text-gray-600 space-y-1",children:[a==="Button"&&y.jsxs(y.Fragment,{children:[y.jsxs("p",{children:[y.jsx("code",{children:"variant"}),": primary | secondary | success | danger | warning | outline | ghost"]}),y.jsxs("p",{children:[y.jsx("code",{children:"size"}),": sm | md | lg | xl"]}),y.jsxs("p",{children:[y.jsx("code",{children:"animation"}),": fade-in | slide-in | bounce-in | scale-in | none"]}),y.jsxs("p",{children:[y.jsx("code",{children:"disabled"}),": boolean"]}),y.jsxs("p",{children:[y.jsx("code",{children:"text"}),": string"]})]}),a==="Modal"&&y.jsxs(y.Fragment,{children:[y.jsxs("p",{children:[y.jsx("code",{children:"size"}),": sm | md | lg | xl | 2xl | full"]}),y.jsxs("p",{children:[y.jsx("code",{children:"animation"}),": fade-in | slide-in | bounce-in | scale-in"]}),y.jsxs("p",{children:[y.jsx("code",{children:"backdrop"}),": boolean"]}),y.jsxs("p",{children:[y.jsx("code",{children:"closable"}),": boolean"]}),y.jsxs("p",{children:[y.jsx("code",{children:"title"}),": string"]}),y.jsxs("p",{children:[y.jsx("code",{children:"content"}),": string"]})]}),a==="Carousel"&&y.jsxs(y.Fragment,{children:[y.jsxs("p",{children:[y.jsx("code",{children:"autoplay"}),": boolean"]}),y.jsxs("p",{children:[y.jsx("code",{children:"interval"}),": number (milliseconds)"]}),y.jsxs("p",{children:[y.jsx("code",{children:"showDots"}),": boolean"]}),y.jsxs("p",{children:[y.jsx("code",{children:"showArrows"}),": boolean"]}),y.jsxs("p",{children:[y.jsx("code",{children:"animation"}),": slide | fade | scale"]}),y.jsxs("p",{children:[y.jsx("code",{children:"items"}),": array of slide objects"]})]}),a==="Card"&&y.jsxs(y.Fragment,{children:[y.jsxs("p",{children:[y.jsx("code",{children:"variant"}),": default | elevated | outlined | filled | gradient"]}),y.jsxs("p",{children:[y.jsx("code",{children:"shadow"}),": none | sm | md | lg | xl | 2xl"]}),y.jsxs("p",{children:[y.jsx("code",{children:"animation"}),": fade-in | slide-in | bounce-in | scale-in | none"]}),y.jsxs("p",{children:[y.jsx("code",{children:"title"}),": string"]}),y.jsxs("p",{children:[y.jsx("code",{children:"content"}),": string"]}),y.jsxs("p",{children:[y.jsx("code",{children:"showImage"}),": boolean"]}),y.jsxs("p",{children:[y.jsx("code",{children:"imageUrl"}),": string"]})]}),a==="Toast"&&y.jsxs(y.Fragment,{children:[y.jsxs("p",{children:[y.jsx("code",{children:"type"}),": success | error | warning | info"]}),y.jsxs("p",{children:[y.jsx("code",{children:"position"}),": top-left | top-center | top-right | bottom-left | bottom-center | bottom-right"]}),y.jsxs("p",{children:[y.jsx("code",{children:"animation"}),": slide-in | fade-in | bounce-in | scale-in"]}),y.jsxs("p",{children:[y.jsx("code",{children:"autoClose"}),": boolean"]}),y.jsxs("p",{children:[y.jsx("code",{children:"duration"}),": number (milliseconds)"]}),y.jsxs("p",{children:[y.jsx("code",{children:"message"}),": string"]})]})]})]})]})})};function m2(){return y.jsxs("div",{className:"h-screen flex bg-gray-100",children:[y.jsx(Tb,{}),y.jsx(o2,{}),y.jsx(h2,{})]})}yb.createRoot(document.getElementById("root")).render(y.jsx(W.StrictMode,{children:y.jsx(m2,{})}));
