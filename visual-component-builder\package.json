{"name": "@visual-kit/react-components", "version": "1.0.0", "description": "A modern, customizable React component library with visual builder, dark mode, and advanced animations", "type": "module", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist", "README.md"], "scripts": {"dev": "vite", "build": "vite build", "build:lib": "vite build --mode lib", "lint": "eslint .", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "keywords": ["react", "components", "ui", "tailwind", "framer-motion", "visual-builder", "dark-mode", "typescript"], "author": "Visual Kit Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/visual-kit/react-components"}, "homepage": "https://visual-kit.dev", "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/postcss": "^4.1.11", "framer-motion": "^12.23.3", "prismjs": "^1.30.0", "react": "^19.1.0", "react-dom": "^19.1.0", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "vite": "^7.0.4"}}