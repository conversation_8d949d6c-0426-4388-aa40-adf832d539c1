{"key": "lastEvents", "content": {"boot": {"body": {"eventType": "boot", "eventId": "vs9EJkebaQVeKUI2nQSOq", "sessionId": "SOcHxCLkg-gmXiJGUIbXe", "payload": {"eventType": "init"}, "context": {"inCI": false, "isTTY": true, "platform": "Windows", "nodeVersion": "22.14.0", "storybookVersion": "9.0.16"}}, "timestamp": 1752456264592}, "init-step": {"body": {"eventType": "init-step", "eventId": "J4dvA3Mn3GuyW9dQ263qJ", "sessionId": "SOcHxCLkg-gmXiJGUIbXe", "metadata": {"generatedAt": 1752456353161, "userSince": 1752456267248, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": false, "refCount": 0, "testPackages": {}, "hasRouterPackage": false, "packageManager": {"type": "npm", "agent": "npm"}, "storybookVersionSpecifier": "9.0.16", "language": "javascript"}, "payload": {"step": "new-user-check", "newUser": true}, "context": {"inCI": false, "isTTY": true, "platform": "Windows", "nodeVersion": "22.14.0", "storybookVersion": "9.0.16", "cliVersion": "9.0.16"}}, "timestamp": 1752456354303}}}