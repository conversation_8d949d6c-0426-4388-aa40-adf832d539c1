import { motion } from 'framer-motion';
import { getAnimationConfig, getInteractionConfig } from '../utils/animations';

const Button = ({
  variant = 'primary',
  size = 'md',
  animation = 'fade-in',
  interaction = 'gentle',
  disabled = false,
  text = 'Click me',
  onClick,
  className = '',
  ...props
}) => {
  // Variant styles
  const variantStyles = {
    primary: 'bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white border-blue-600 dark:border-blue-500',
    secondary: 'bg-gray-600 hover:bg-gray-700 dark:bg-gray-500 dark:hover:bg-gray-600 text-white border-gray-600 dark:border-gray-500',
    success: 'bg-green-600 hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600 text-white border-green-600 dark:border-green-500',
    danger: 'bg-red-600 hover:bg-red-700 dark:bg-red-500 dark:hover:bg-red-600 text-white border-red-600 dark:border-red-500',
    warning: 'bg-yellow-600 hover:bg-yellow-700 dark:bg-yellow-500 dark:hover:bg-yellow-600 text-white border-yellow-600 dark:border-yellow-500',
    outline: 'bg-transparent hover:bg-blue-50 dark:hover:bg-blue-900/20 text-blue-600 dark:text-blue-400 border-blue-600 dark:border-blue-400',
    ghost: 'bg-transparent hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300 border-transparent',
  };

  // Size styles
  const sizeStyles = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg',
    xl: 'px-8 py-4 text-xl',
  };

  const baseStyles = `
    inline-flex items-center justify-center
    font-medium rounded-lg border
    transition-all duration-200 ease-in-out
    focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-blue-400 dark:focus:ring-offset-gray-800
    disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-current
    ${variantStyles[variant]}
    ${sizeStyles[size]}
    ${className}
  `;

  const animationConfig = getAnimationConfig(animation);
  const interactionConfig = getInteractionConfig(interaction, disabled);

  return (
    <motion.button
      className={baseStyles}
      disabled={disabled}
      onClick={onClick}
      initial={animationConfig.initial}
      animate={animationConfig.animate}
      exit={animationConfig.exit}
      transition={animationConfig.transition}
      whileHover={interactionConfig.whileHover}
      whileTap={interactionConfig.whileTap}
      {...props}
    >
      {text}
    </motion.button>
  );
};

export default Button;
