import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';
import { getAnimationConfig } from '../utils/animations';

const Accordion = ({
  items = [
    { id: 1, title: 'What is this component?', content: 'This is a customizable accordion component with smooth animations.' },
    { id: 2, title: 'How do I use it?', content: 'Simply pass an array of items with title and content properties.' },
    { id: 3, title: 'Can I customize it?', content: 'Yes! You can control animations, allow multiple open items, and more.' }
  ],
  allowMultiple = false,
  animation = 'slide-up',
  animationSpeed = 0.3,
  direction = 'top-down',
  className = '',
  ...props
}) => {
  const [openItems, setOpenItems] = useState(new Set());

  const toggleItem = (itemId) => {
    const newOpenItems = new Set(openItems);
    
    if (newOpenItems.has(itemId)) {
      newOpenItems.delete(itemId);
    } else {
      if (!allowMultiple) {
        newOpenItems.clear();
      }
      newOpenItems.add(itemId);
    }
    
    setOpenItems(newOpenItems);
  };

  const animationConfig = getAnimationConfig(animation, { duration: animationSpeed });

  const contentVariants = {
    closed: {
      height: 0,
      opacity: 0,
      transition: { duration: animationSpeed, ease: "easeInOut" }
    },
    open: {
      height: "auto",
      opacity: 1,
      transition: { duration: animationSpeed, ease: "easeInOut" }
    }
  };

  const iconVariants = {
    closed: { rotate: 0 },
    open: { rotate: 180 }
  };

  const displayItems = direction === 'bottom-up' ? [...items].reverse() : items;

  return (
    <div className={`space-y-2 ${className}`} {...props}>
      {displayItems.map((item, index) => {
        const isOpen = openItems.has(item.id);
        
        return (
          <motion.div
            key={item.id}
            className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden"
            initial={animationConfig.initial}
            animate={animationConfig.animate}
            transition={{ ...animationConfig.transition, delay: index * 0.1 }}
          >
            {/* Header */}
            <button
              onClick={() => toggleItem(item.id)}
              className="w-full px-4 py-3 text-left bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors flex items-center justify-between"
            >
              <span className="font-medium text-gray-900 dark:text-white">
                {item.title}
              </span>
              <motion.svg
                className="w-5 h-5 text-gray-500 dark:text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                variants={iconVariants}
                animate={isOpen ? "open" : "closed"}
                transition={{ duration: 0.2 }}
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </motion.svg>
            </button>

            {/* Content */}
            <AnimatePresence>
              {isOpen && (
                <motion.div
                  variants={contentVariants}
                  initial="closed"
                  animate="open"
                  exit="closed"
                  className="overflow-hidden"
                >
                  <div className="px-4 py-3 bg-gray-50 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
                    <p className="text-gray-600 dark:text-gray-300">
                      {item.content}
                    </p>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        );
      })}
    </div>
  );
};

export default Accordion;
