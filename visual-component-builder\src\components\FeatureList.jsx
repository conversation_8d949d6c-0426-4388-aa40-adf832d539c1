import { motion } from 'framer-motion';
import { getAnimationConfig, staggerConfigs } from '../utils/animations';

const FeatureList = ({
  animation = 'stagger-fade',
  staggerSpeed = 'medium',
  features = [
    { icon: '🚀', title: 'Fast Performance', description: 'Lightning-fast rendering and smooth animations' },
    { icon: '🎨', title: 'Beautiful Design', description: 'Modern and clean user interface components' },
    { icon: '📱', title: 'Responsive', description: 'Works perfectly on all devices and screen sizes' },
    { icon: '🔧', title: 'Customizable', description: 'Easily customize colors, sizes, and animations' }
  ],
  className = '',
  ...props
}) => {
  const animationConfig = getAnimationConfig(animation);
  const staggerConfig = staggerConfigs[staggerSpeed] || staggerConfigs.medium;

  const containerVariants = {
    initial: {},
    animate: {
      transition: staggerConfig
    }
  };

  return (
    <motion.div
      className={`space-y-4 ${className}`}
      variants={containerVariants}
      initial="initial"
      animate="animate"
      {...props}
    >
      {features.map((feature, index) => (
        <motion.div
          key={index}
          variants={animationConfig}
          className="flex items-start space-x-4 p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"
        >
          <div className="flex-shrink-0 text-2xl">
            {feature.icon}
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">
              {feature.title}
            </h3>
            <p className="text-gray-600 dark:text-gray-300 text-sm">
              {feature.description}
            </p>
          </div>
        </motion.div>
      ))}
    </motion.div>
  );
};

export default FeatureList;
