import useComponentStore from '../store/componentStore';
import { Button, Modal, Carousel, Card, Toast, FeatureList, Accordion, Tabs, Select } from './index';
import { motion } from 'framer-motion';
import { useTheme } from '../contexts/ThemeContext';

const Preview = () => {
  const { selectedComponent, componentConfigs } = useComponentStore();
  const { isDark } = useTheme();
  const currentConfig = componentConfigs[selectedComponent];

  const renderComponent = () => {
    switch (selectedComponent) {
      case 'Button':
        return (
          <Button
            variant={currentConfig.variant}
            size={currentConfig.size}
            animation={currentConfig.animation}
            interaction={currentConfig.interaction}
            disabled={currentConfig.disabled}
            text={currentConfig.text}
            onClick={() => alert('Button clicked!')}
          />
        );
      
      case 'Modal':
        return (
          <Modal
            size={currentConfig.size}
            animation={currentConfig.animation}
            backdrop={currentConfig.backdrop}
            closable={currentConfig.closable}
            title={currentConfig.title}
            content={currentConfig.content}
          />
        );
      
      case 'Carousel':
        return (
          <Carousel
            autoplay={currentConfig.autoplay}
            interval={currentConfig.interval}
            showDots={currentConfig.showDots}
            showArrows={currentConfig.showArrows}
            animation={currentConfig.animation}
            items={currentConfig.items}
          />
        );
      
      case 'Card':
        return (
          <Card
            variant={currentConfig.variant}
            shadow={currentConfig.shadow}
            animation={currentConfig.animation}
            title={currentConfig.title}
            content={currentConfig.content}
            showImage={currentConfig.showImage}
            imageUrl={currentConfig.imageUrl}
          />
        );
      
      case 'Toast':
        return (
          <Toast
            type={currentConfig.type}
            position={currentConfig.position}
            animation={currentConfig.animation}
            autoClose={currentConfig.autoClose}
            duration={currentConfig.duration}
            message={currentConfig.message}
          />
        );

      case 'FeatureList':
        return (
          <FeatureList
            animation={currentConfig.animation}
            staggerSpeed={currentConfig.staggerSpeed}
            features={currentConfig.features}
          />
        );

      case 'Accordion':
        return (
          <Accordion
            allowMultiple={currentConfig.allowMultiple}
            animation={currentConfig.animation}
            animationSpeed={currentConfig.animationSpeed}
            direction={currentConfig.direction}
            items={currentConfig.items}
          />
        );

      case 'Tabs':
        return (
          <Tabs
            variant={currentConfig.variant}
            underlineAnimation={currentConfig.underlineAnimation}
            animation={currentConfig.animation}
            tabs={currentConfig.tabs}
          />
        );

      case 'Select':
        return (
          <Select
            multiple={currentConfig.multiple}
            searchable={currentConfig.searchable}
            creatable={currentConfig.creatable}
            animation={currentConfig.animation}
            placeholder={currentConfig.placeholder}
            options={currentConfig.options}
          />
        );

      default:
        return <div>Component not found</div>;
    }
  };

  return (
    <div className="flex-1 bg-gray-50 dark:bg-gray-900 p-8 overflow-y-auto">
      <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Live Preview</h2>
          <p className="text-gray-600 dark:text-gray-300">
            See your {selectedComponent} component in action. Changes are reflected in real-time.
          </p>
        </div>

        {/* Enhanced Preview Area with Frosted Glass Design */}
        <motion.div
          className={`
            min-w-[720px] w-3/4 min-h-96 p-8 rounded-3xl
            flex items-center justify-center
            transition-all duration-500 ease-in-out
            ${isDark
              ? 'bg-white/10 backdrop-blur-lg border border-white/30 shadow-white/10 shadow-2xl'
              : 'bg-white shadow-xl border border-gray-200'
            }
          `}
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, ease: "easeOut" }}
        >
          <div className="w-full max-w-2xl">
            {renderComponent()}
          </div>
        </motion.div>

        {/* Component Info */}
        <div className="mt-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">Component Configuration</h3>
          <div className="bg-gray-50 dark:bg-gray-900 rounded-md p-4">
            <pre className="text-sm text-gray-700 dark:text-gray-300 overflow-x-auto">
              {JSON.stringify(currentConfig, null, 2)}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Preview;
