import useComponentStore from '../store/componentStore';
import { Button, Modal, Carousel, Card, Toast, FeatureList } from './index';

const Preview = () => {
  const { selectedComponent, componentConfigs } = useComponentStore();
  const currentConfig = componentConfigs[selectedComponent];

  const renderComponent = () => {
    switch (selectedComponent) {
      case 'Button':
        return (
          <Button
            variant={currentConfig.variant}
            size={currentConfig.size}
            animation={currentConfig.animation}
            interaction={currentConfig.interaction}
            disabled={currentConfig.disabled}
            text={currentConfig.text}
            onClick={() => alert('Button clicked!')}
          />
        );
      
      case 'Modal':
        return (
          <Modal
            size={currentConfig.size}
            animation={currentConfig.animation}
            backdrop={currentConfig.backdrop}
            closable={currentConfig.closable}
            title={currentConfig.title}
            content={currentConfig.content}
          />
        );
      
      case 'Carousel':
        return (
          <Carousel
            autoplay={currentConfig.autoplay}
            interval={currentConfig.interval}
            showDots={currentConfig.showDots}
            showArrows={currentConfig.showArrows}
            animation={currentConfig.animation}
            items={currentConfig.items}
          />
        );
      
      case 'Card':
        return (
          <Card
            variant={currentConfig.variant}
            shadow={currentConfig.shadow}
            animation={currentConfig.animation}
            title={currentConfig.title}
            content={currentConfig.content}
            showImage={currentConfig.showImage}
            imageUrl={currentConfig.imageUrl}
          />
        );
      
      case 'Toast':
        return (
          <Toast
            type={currentConfig.type}
            position={currentConfig.position}
            animation={currentConfig.animation}
            autoClose={currentConfig.autoClose}
            duration={currentConfig.duration}
            message={currentConfig.message}
          />
        );

      case 'FeatureList':
        return (
          <FeatureList
            animation={currentConfig.animation}
            staggerSpeed={currentConfig.staggerSpeed}
            features={currentConfig.features}
          />
        );

      default:
        return <div>Component not found</div>;
    }
  };

  return (
    <div className="flex-1 bg-gray-50 dark:bg-gray-900 p-8 overflow-y-auto">
      <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Live Preview</h2>
          <p className="text-gray-600 dark:text-gray-300">
            See your {selectedComponent} component in action. Changes are reflected in real-time.
          </p>
        </div>

        {/* Preview Area */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8 min-h-96 flex items-center justify-center">
          <div className="w-full max-w-lg">
            {renderComponent()}
          </div>
        </div>

        {/* Component Info */}
        <div className="mt-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">Component Configuration</h3>
          <div className="bg-gray-50 dark:bg-gray-900 rounded-md p-4">
            <pre className="text-sm text-gray-700 dark:text-gray-300 overflow-x-auto">
              {JSON.stringify(currentConfig, null, 2)}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Preview;
