import useComponentStore from '../store/componentStore';

const ControlPanel = () => {
  // Common input styles for dark mode
  const inputStyles = "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400";
  const labelStyles = "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2";
  const checkboxStyles = "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700";
  const sliderStyles = "w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer";
  const { 
    selectedComponent, 
    componentConfigs, 
    setSelectedComponent, 
    updateComponentConfig,
    resetComponentConfig 
  } = useComponentStore();

  const currentConfig = componentConfigs[selectedComponent];

  const handleConfigChange = (key, value) => {
    updateComponentConfig(selectedComponent, { [key]: value });
  };

  const handleReset = () => {
    resetComponentConfig(selectedComponent);
  };

  const renderButtonControls = () => (
    <div className="space-y-4">
      <div>
        <label className={labelStyles}>Text</label>
        <input
          type="text"
          value={currentConfig.text}
          onChange={(e) => handleConfigChange('text', e.target.value)}
          className={inputStyles}
        />
      </div>

      <div>
        <label className={labelStyles}>Variant</label>
        <select
          value={currentConfig.variant}
          onChange={(e) => handleConfigChange('variant', e.target.value)}
          className={inputStyles}
        >
          <option value="primary">Primary</option>
          <option value="secondary">Secondary</option>
          <option value="success">Success</option>
          <option value="danger">Danger</option>
          <option value="warning">Warning</option>
          <option value="outline">Outline</option>
          <option value="ghost">Ghost</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Size</label>
        <select
          value={currentConfig.size}
          onChange={(e) => handleConfigChange('size', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="sm">Small</option>
          <option value="md">Medium</option>
          <option value="lg">Large</option>
          <option value="xl">Extra Large</option>
        </select>
      </div>

      <div>
        <label className={labelStyles}>Animation</label>
        <select
          value={currentConfig.animation}
          onChange={(e) => handleConfigChange('animation', e.target.value)}
          className={inputStyles}
        >
          <option value="fade-in">Fade In</option>
          <option value="slide-in">Slide In</option>
          <option value="slide-up">Slide Up</option>
          <option value="slide-left">Slide Left</option>
          <option value="slide-right">Slide Right</option>
          <option value="scale-in">Scale In</option>
          <option value="bounce-in">Bounce In</option>
          <option value="spring-gentle">Spring Gentle</option>
          <option value="spring-bouncy">Spring Bouncy</option>
          <option value="spring-wobbly">Spring Wobbly</option>
          <option value="rotate-in">Rotate In</option>
          <option value="flip-in">Flip In</option>
          <option value="zoom-bounce">Zoom Bounce</option>
          <option value="slide-bounce">Slide Bounce</option>
          <option value="none">None</option>
        </select>
      </div>

      <div>
        <label className={labelStyles}>Interaction</label>
        <select
          value={currentConfig.interaction}
          onChange={(e) => handleConfigChange('interaction', e.target.value)}
          className={inputStyles}
        >
          <option value="gentle">Gentle</option>
          <option value="bouncy">Bouncy</option>
          <option value="lift">Lift</option>
          <option value="rotate">Rotate</option>
          <option value="none">None</option>
        </select>
      </div>

      <div className="flex items-center">
        <input
          type="checkbox"
          id="disabled"
          checked={currentConfig.disabled}
          onChange={(e) => handleConfigChange('disabled', e.target.checked)}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label htmlFor="disabled" className="ml-2 block text-sm text-gray-700">
          Disabled
        </label>
      </div>
    </div>
  );

  const renderModalControls = () => (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Title</label>
        <input
          type="text"
          value={currentConfig.title}
          onChange={(e) => handleConfigChange('title', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Content</label>
        <textarea
          value={currentConfig.content}
          onChange={(e) => handleConfigChange('content', e.target.value)}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Size</label>
        <select
          value={currentConfig.size}
          onChange={(e) => handleConfigChange('size', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="sm">Small</option>
          <option value="md">Medium</option>
          <option value="lg">Large</option>
          <option value="xl">Extra Large</option>
          <option value="2xl">2X Large</option>
          <option value="full">Full Width</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Animation</label>
        <select
          value={currentConfig.animation}
          onChange={(e) => handleConfigChange('animation', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="fade-in">Fade In</option>
          <option value="slide-in">Slide In</option>
          <option value="bounce-in">Bounce In</option>
          <option value="scale-in">Scale In</option>
        </select>
      </div>

      <div className="flex items-center">
        <input
          type="checkbox"
          id="backdrop"
          checked={currentConfig.backdrop}
          onChange={(e) => handleConfigChange('backdrop', e.target.checked)}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label htmlFor="backdrop" className="ml-2 block text-sm text-gray-700">
          Show Backdrop
        </label>
      </div>

      <div className="flex items-center">
        <input
          type="checkbox"
          id="closable"
          checked={currentConfig.closable}
          onChange={(e) => handleConfigChange('closable', e.target.checked)}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label htmlFor="closable" className="ml-2 block text-sm text-gray-700">
          Closable
        </label>
      </div>
    </div>
  );

  const renderCarouselControls = () => (
    <div className="space-y-4">
      <div className="flex items-center">
        <input
          type="checkbox"
          id="autoplay"
          checked={currentConfig.autoplay}
          onChange={(e) => handleConfigChange('autoplay', e.target.checked)}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label htmlFor="autoplay" className="ml-2 block text-sm text-gray-700">
          Autoplay
        </label>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Interval (ms): {currentConfig.interval}
        </label>
        <input
          type="range"
          min="1000"
          max="10000"
          step="500"
          value={currentConfig.interval}
          onChange={(e) => handleConfigChange('interval', parseInt(e.target.value))}
          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Animation</label>
        <select
          value={currentConfig.animation}
          onChange={(e) => handleConfigChange('animation', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="slide">Slide</option>
          <option value="fade">Fade</option>
          <option value="scale">Scale</option>
        </select>
      </div>

      <div className="flex items-center">
        <input
          type="checkbox"
          id="showDots"
          checked={currentConfig.showDots}
          onChange={(e) => handleConfigChange('showDots', e.target.checked)}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label htmlFor="showDots" className="ml-2 block text-sm text-gray-700">
          Show Dots
        </label>
      </div>

      <div className="flex items-center">
        <input
          type="checkbox"
          id="showArrows"
          checked={currentConfig.showArrows}
          onChange={(e) => handleConfigChange('showArrows', e.target.checked)}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label htmlFor="showArrows" className="ml-2 block text-sm text-gray-700">
          Show Arrows
        </label>
      </div>
    </div>
  );

  const renderCardControls = () => (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Title</label>
        <input
          type="text"
          value={currentConfig.title}
          onChange={(e) => handleConfigChange('title', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Content</label>
        <textarea
          value={currentConfig.content}
          onChange={(e) => handleConfigChange('content', e.target.value)}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Variant</label>
        <select
          value={currentConfig.variant}
          onChange={(e) => handleConfigChange('variant', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="default">Default</option>
          <option value="elevated">Elevated</option>
          <option value="outlined">Outlined</option>
          <option value="filled">Filled</option>
          <option value="gradient">Gradient</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Shadow</label>
        <select
          value={currentConfig.shadow}
          onChange={(e) => handleConfigChange('shadow', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="none">None</option>
          <option value="sm">Small</option>
          <option value="md">Medium</option>
          <option value="lg">Large</option>
          <option value="xl">Extra Large</option>
          <option value="2xl">2X Large</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Animation</label>
        <select
          value={currentConfig.animation}
          onChange={(e) => handleConfigChange('animation', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="fade-in">Fade In</option>
          <option value="slide-in">Slide In</option>
          <option value="bounce-in">Bounce In</option>
          <option value="scale-in">Scale In</option>
          <option value="none">None</option>
        </select>
      </div>

      <div className="flex items-center">
        <input
          type="checkbox"
          id="showImage"
          checked={currentConfig.showImage}
          onChange={(e) => handleConfigChange('showImage', e.target.checked)}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label htmlFor="showImage" className="ml-2 block text-sm text-gray-700">
          Show Image
        </label>
      </div>

      {currentConfig.showImage && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Image URL</label>
          <input
            type="url"
            value={currentConfig.imageUrl}
            onChange={(e) => handleConfigChange('imageUrl', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      )}
    </div>
  );

  const renderToastControls = () => (
    <div className="space-y-4">
      <div>
        <label className={labelStyles}>Message</label>
        <input
          type="text"
          value={currentConfig.message}
          onChange={(e) => handleConfigChange('message', e.target.value)}
          className={inputStyles}
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Type</label>
        <select
          value={currentConfig.type}
          onChange={(e) => handleConfigChange('type', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="success">Success</option>
          <option value="error">Error</option>
          <option value="warning">Warning</option>
          <option value="info">Info</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Position</label>
        <select
          value={currentConfig.position}
          onChange={(e) => handleConfigChange('position', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="top-left">Top Left</option>
          <option value="top-center">Top Center</option>
          <option value="top-right">Top Right</option>
          <option value="bottom-left">Bottom Left</option>
          <option value="bottom-center">Bottom Center</option>
          <option value="bottom-right">Bottom Right</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Animation</label>
        <select
          value={currentConfig.animation}
          onChange={(e) => handleConfigChange('animation', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="slide-in">Slide In</option>
          <option value="fade-in">Fade In</option>
          <option value="bounce-in">Bounce In</option>
          <option value="scale-in">Scale In</option>
        </select>
      </div>

      <div className="flex items-center">
        <input
          type="checkbox"
          id="autoClose"
          checked={currentConfig.autoClose}
          onChange={(e) => handleConfigChange('autoClose', e.target.checked)}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label htmlFor="autoClose" className="ml-2 block text-sm text-gray-700">
          Auto Close
        </label>
      </div>

      {currentConfig.autoClose && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Duration (ms): {currentConfig.duration}
          </label>
          <input
            type="range"
            min="1000"
            max="10000"
            step="500"
            value={currentConfig.duration}
            onChange={(e) => handleConfigChange('duration', parseInt(e.target.value))}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
          />
        </div>
      )}
    </div>
  );

  const renderControls = () => {
    switch (selectedComponent) {
      case 'Button':
        return renderButtonControls();
      case 'Modal':
        return renderModalControls();
      case 'Carousel':
        return renderCarouselControls();
      case 'Card':
        return renderCardControls();
      case 'Toast':
        return renderToastControls();
      default:
        return <div>No controls available</div>;
    }
  };

  return (
    <div className="w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 p-6 overflow-y-auto">
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Component Builder</h2>
        
        {/* Component Selector */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Component</label>
          <select
            value={selectedComponent}
            onChange={(e) => setSelectedComponent(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
          >
            <option value="Button">Button</option>
            <option value="Modal">Modal</option>
            <option value="Carousel">Carousel</option>
            <option value="Card">Card</option>
            <option value="Toast">Toast</option>
            <option value="FeatureList">Feature List</option>
          </select>
        </div>

        {/* Component Controls */}
        <div className="mb-6">
          <h3 className="text-md font-medium text-gray-800 dark:text-gray-200 mb-3">Properties</h3>
          {renderControls()}
        </div>

        {/* Reset Button */}
        <button
          onClick={handleReset}
          className="w-full px-4 py-2 bg-gray-600 hover:bg-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 text-white rounded-lg transition-colors"
        >
          Reset to Default
        </button>
      </div>
    </div>
  );
};

export default ControlPanel;
