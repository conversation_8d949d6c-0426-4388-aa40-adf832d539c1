import { Card } from '../components';

export default {
  title: 'Components/Card',
  component: Card,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['default', 'elevated', 'outlined', 'filled', 'gradient'],
    },
    shadow: {
      control: { type: 'select' },
      options: ['none', 'sm', 'md', 'lg', 'xl', '2xl'],
    },
    animation: {
      control: { type: 'select' },
      options: ['fade-in', 'slide-in', 'bounce-in', 'scale-in', 'none'],
    },
    showImage: {
      control: { type: 'boolean' },
    },
    title: {
      control: { type: 'text' },
    },
    content: {
      control: { type: 'text' },
    },
    imageUrl: {
      control: { type: 'text' },
    },
  },
};

export const Default = {
  args: {
    variant: 'default',
    title: 'Default Card',
    content: 'This is a default card with standard styling and shadow.',
  },
};

export const Elevated = {
  args: {
    variant: 'elevated',
    shadow: 'lg',
    title: 'Elevated Card',
    content: 'This card has an elevated appearance with larger shadow.',
  },
};

export const Outlined = {
  args: {
    variant: 'outlined',
    title: 'Outlined Card',
    content: 'This card has a prominent border outline.',
  },
};

export const Filled = {
  args: {
    variant: 'filled',
    title: 'Filled Card',
    content: 'This card has a filled background color.',
  },
};

export const Gradient = {
  args: {
    variant: 'gradient',
    title: 'Gradient Card',
    content: 'This card features a beautiful gradient background.',
  },
};

export const WithoutImage = {
  args: {
    showImage: false,
    title: 'Text Only Card',
    content: 'This card displays only text content without an image.',
  },
};

export const CustomImage = {
  args: {
    title: 'Custom Image Card',
    content: 'This card features a custom image.',
    imageUrl: 'https://via.placeholder.com/300x150/FF6B6B/white?text=Custom+Image',
  },
};

export const WithAnimation = {
  args: {
    animation: 'scale-in',
    title: 'Animated Card',
    content: 'This card has a scale-in animation effect.',
  },
};
