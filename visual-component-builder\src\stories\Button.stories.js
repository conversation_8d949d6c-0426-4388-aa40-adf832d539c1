import { Button } from '../components';

export default {
  title: 'Components/Button',
  component: Button,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['primary', 'secondary', 'success', 'danger', 'warning', 'outline', 'ghost'],
    },
    size: {
      control: { type: 'select' },
      options: ['sm', 'md', 'lg', 'xl'],
    },
    animation: {
      control: { type: 'select' },
      options: ['fade-in', 'slide-in', 'bounce-in', 'scale-in', 'none'],
    },
    disabled: {
      control: { type: 'boolean' },
    },
    text: {
      control: { type: 'text' },
    },
  },
};

export const Primary = {
  args: {
    variant: 'primary',
    text: 'Primary Button',
  },
};

export const Secondary = {
  args: {
    variant: 'secondary',
    text: 'Secondary Button',
  },
};

export const Success = {
  args: {
    variant: 'success',
    text: 'Success Button',
  },
};

export const Danger = {
  args: {
    variant: 'danger',
    text: 'Danger Button',
  },
};

export const Warning = {
  args: {
    variant: 'warning',
    text: 'Warning Button',
  },
};

export const Outline = {
  args: {
    variant: 'outline',
    text: 'Outline Button',
  },
};

export const Ghost = {
  args: {
    variant: 'ghost',
    text: 'Ghost Button',
  },
};

export const Small = {
  args: {
    size: 'sm',
    text: 'Small Button',
  },
};

export const Large = {
  args: {
    size: 'lg',
    text: 'Large Button',
  },
};

export const ExtraLarge = {
  args: {
    size: 'xl',
    text: 'Extra Large Button',
  },
};

export const Disabled = {
  args: {
    disabled: true,
    text: 'Disabled Button',
  },
};

export const WithAnimation = {
  args: {
    animation: 'bounce-in',
    text: 'Animated Button',
  },
};
