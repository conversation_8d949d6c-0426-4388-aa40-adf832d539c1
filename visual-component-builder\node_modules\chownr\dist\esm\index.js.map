{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAmB,MAAM,SAAS,CAAA;AACzC,OAAO,IAAI,MAAM,WAAW,CAAA;AAE5B,MAAM,UAAU,GAAG,CAAC,IAAY,EAAE,GAAW,EAAE,GAAW,EAAE,EAAE;IAC5D,IAAI,CAAC;QACH,OAAO,EAAE,CAAC,UAAU,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;IACtC,CAAC;IAAC,OAAO,EAAE,EAAE,CAAC;QACZ,IAAK,EAA4B,EAAE,IAAI,KAAK,QAAQ;YAAE,MAAM,EAAE,CAAA;IAChE,CAAC;AACH,CAAC,CAAA;AAED,MAAM,KAAK,GAAG,CACZ,KAAa,EACb,GAAW,EACX,GAAW,EACX,EAAyB,EACzB,EAAE;IACF,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE;QAC9B,oBAAoB;QACpB,EAAE,CAAC,EAAE,IAAK,EAA4B,EAAE,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;IACxE,CAAC,CAAC,CAAA;AACJ,CAAC,CAAA;AAED,MAAM,SAAS,GAAG,CAChB,CAAS,EACT,KAAa,EACb,GAAW,EACX,GAAW,EACX,EAAyB,EACzB,EAAE;IACF,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;QACxB,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAW,EAAE,EAAE;YAC5D,IAAI,EAAE;gBAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAA;YACrB,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAA;YACzC,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,CAAA;QAC5B,CAAC,CAAC,CAAA;IACJ,CAAC;SAAM,CAAC;QACN,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAA;QACzC,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,CAAA;IAC5B,CAAC;AACH,CAAC,CAAA;AAED,MAAM,CAAC,MAAM,MAAM,GAAG,CACpB,CAAS,EACT,GAAW,EACX,GAAW,EACX,EAAyB,EACzB,EAAE;IACF,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE,QAAQ,EAAE,EAAE;QACtD,mEAAmE;QACnE,8BAA8B;QAC9B,IAAI,EAAE,EAAE,CAAC;YACP,IAAI,EAAE,CAAC,IAAI,KAAK,QAAQ;gBAAE,OAAO,EAAE,EAAE,CAAA;iBAChC,IAAI,EAAE,CAAC,IAAI,KAAK,SAAS,IAAI,EAAE,CAAC,IAAI,KAAK,SAAS;gBACrD,OAAO,EAAE,CAAC,EAAE,CAAC,CAAA;QACjB,CAAC;QACD,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM;YAAE,OAAO,KAAK,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,CAAA;QAEzD,IAAI,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAA;QACzB,IAAI,QAAQ,GAAiC,IAAI,CAAA;QACjD,MAAM,IAAI,GAAG,CAAC,EAAY,EAAE,EAAE;YAC5B,qBAAqB;YACrB,IAAI,QAAQ;gBAAE,OAAM;YACpB,oBAAoB;YACpB,IAAI,EAAE;gBAAE,OAAO,EAAE,CAAC,CAAC,QAAQ,GAAG,EAA2B,CAAC,CAAC,CAAA;YAC3D,IAAI,EAAE,GAAG,KAAK,CAAC;gBAAE,OAAO,KAAK,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,CAAA;QAChD,CAAC,CAAA;QAED,KAAK,MAAM,KAAK,IAAI,QAAQ,EAAE,CAAC;YAC7B,SAAS,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAA;QACrC,CAAC;IACH,CAAC,CAAC,CAAA;AACJ,CAAC,CAAA;AAED,MAAM,aAAa,GAAG,CACpB,CAAS,EACT,KAAa,EACb,GAAW,EACX,GAAW,EACX,EAAE;IACF,IAAI,KAAK,CAAC,WAAW,EAAE;QACrB,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;IAEnD,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;AACnD,CAAC,CAAA;AAED,MAAM,CAAC,MAAM,UAAU,GAAG,CAAC,CAAS,EAAE,GAAW,EAAE,GAAW,EAAE,EAAE;IAChE,IAAI,QAAkB,CAAA;IACtB,IAAI,CAAC;QACH,QAAQ,GAAG,EAAE,CAAC,WAAW,CAAC,CAAC,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAA;IACvD,CAAC;IAAC,OAAO,EAAE,EAAE,CAAC;QACZ,MAAM,CAAC,GAAG,EAA2B,CAAA;QACrC,IAAI,CAAC,EAAE,IAAI,KAAK,QAAQ;YAAE,OAAM;aAC3B,IAAI,CAAC,EAAE,IAAI,KAAK,SAAS,IAAI,CAAC,EAAE,IAAI,KAAK,SAAS;YACrD,OAAO,UAAU,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;;YAC3B,MAAM,CAAC,CAAA;IACd,CAAC;IAED,KAAK,MAAM,KAAK,IAAI,QAAQ,EAAE,CAAC;QAC7B,aAAa,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;IACnC,CAAC;IAED,OAAO,UAAU,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;AAChC,CAAC,CAAA", "sourcesContent": ["import fs, { type Dirent } from 'node:fs'\nimport path from 'node:path'\n\nconst lchownSync = (path: string, uid: number, gid: number) => {\n  try {\n    return fs.lchownSync(path, uid, gid)\n  } catch (er) {\n    if ((er as NodeJS.ErrnoException)?.code !== 'ENOENT') throw er\n  }\n}\n\nconst chown = (\n  cpath: string,\n  uid: number,\n  gid: number,\n  cb: (er?: unknown) => any,\n) => {\n  fs.lchown(cpath, uid, gid, er => {\n    // Skip ENOENT error\n    cb(er && (er as NodeJS.ErrnoException)?.code !== 'ENOENT' ? er : null)\n  })\n}\n\nconst chownrKid = (\n  p: string,\n  child: Dirent,\n  uid: number,\n  gid: number,\n  cb: (er?: unknown) => any,\n) => {\n  if (child.isDirectory()) {\n    chownr(path.resolve(p, child.name), uid, gid, (er: unknown) => {\n      if (er) return cb(er)\n      const cpath = path.resolve(p, child.name)\n      chown(cpath, uid, gid, cb)\n    })\n  } else {\n    const cpath = path.resolve(p, child.name)\n    chown(cpath, uid, gid, cb)\n  }\n}\n\nexport const chownr = (\n  p: string,\n  uid: number,\n  gid: number,\n  cb: (er?: unknown) => any,\n) => {\n  fs.readdir(p, { withFileTypes: true }, (er, children) => {\n    // any error other than ENOTDIR or ENOTSUP means it's not readable,\n    // or doesn't exist.  give up.\n    if (er) {\n      if (er.code === 'ENOENT') return cb()\n      else if (er.code !== 'ENOTDIR' && er.code !== 'ENOTSUP')\n        return cb(er)\n    }\n    if (er || !children.length) return chown(p, uid, gid, cb)\n\n    let len = children.length\n    let errState: null | NodeJS.ErrnoException = null\n    const then = (er?: unknown) => {\n      /* c8 ignore start */\n      if (errState) return\n      /* c8 ignore stop */\n      if (er) return cb((errState = er as NodeJS.ErrnoException))\n      if (--len === 0) return chown(p, uid, gid, cb)\n    }\n\n    for (const child of children) {\n      chownrKid(p, child, uid, gid, then)\n    }\n  })\n}\n\nconst chownrKidSync = (\n  p: string,\n  child: Dirent,\n  uid: number,\n  gid: number,\n) => {\n  if (child.isDirectory())\n    chownrSync(path.resolve(p, child.name), uid, gid)\n\n  lchownSync(path.resolve(p, child.name), uid, gid)\n}\n\nexport const chownrSync = (p: string, uid: number, gid: number) => {\n  let children: Dirent[]\n  try {\n    children = fs.readdirSync(p, { withFileTypes: true })\n  } catch (er) {\n    const e = er as NodeJS.ErrnoException\n    if (e?.code === 'ENOENT') return\n    else if (e?.code === 'ENOTDIR' || e?.code === 'ENOTSUP')\n      return lchownSync(p, uid, gid)\n    else throw e\n  }\n\n  for (const child of children) {\n    chownrKidSync(p, child, uid, gid)\n  }\n\n  return lchownSync(p, uid, gid)\n}\n"]}