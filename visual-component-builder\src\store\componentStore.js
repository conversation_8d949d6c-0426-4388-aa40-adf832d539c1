import { create } from 'zustand';

const useComponentStore = create((set) => ({
  // Current selected component
  selectedComponent: 'Button',
  
  // Component configurations
  componentConfigs: {
    Button: {
      variant: 'primary',
      size: 'md',
      animation: 'fade-in',
      interaction: 'gentle',
      disabled: false,
      text: 'Click me',
    },
    Modal: {
      size: 'md',
      animation: 'scale-in',
      backdrop: true,
      closable: true,
      title: 'Modal Title',
      content: 'This is modal content',
    },
    Carousel: {
      autoplay: true,
      interval: 3000,
      showDots: true,
      showArrows: true,
      animation: 'slide',
      items: [
        { id: 1, content: 'Slide 1', image: 'https://via.placeholder.com/400x200/3B82F6/white?text=Slide+1' },
        { id: 2, content: 'Slide 2', image: 'https://via.placeholder.com/400x200/10B981/white?text=Slide+2' },
        { id: 3, content: 'Slide 3', image: 'https://via.placeholder.com/400x200/F59E0B/white?text=Slide+3' },
      ],
    },
    Card: {
      variant: 'default',
      shadow: 'md',
      animation: 'fade-in',
      title: 'Card Title',
      content: 'This is card content with some description text.',
      showImage: true,
      imageUrl: 'https://via.placeholder.com/300x150/6366F1/white?text=Card+Image',
    },
    Toast: {
      type: 'success',
      position: 'top-right',
      animation: 'slide-in',
      autoClose: true,
      duration: 3000,
      message: 'This is a toast message!',
    },
    FeatureList: {
      animation: 'stagger-fade',
      staggerSpeed: 'medium',
      features: [
        { icon: '🚀', title: 'Fast Performance', description: 'Lightning-fast rendering and smooth animations' },
        { icon: '🎨', title: 'Beautiful Design', description: 'Modern and clean user interface components' },
        { icon: '📱', title: 'Responsive', description: 'Works perfectly on all devices and screen sizes' },
        { icon: '🔧', title: 'Customizable', description: 'Easily customize colors, sizes, and animations' }
      ],
    },
    Accordion: {
      allowMultiple: false,
      animation: 'slide-up',
      animationSpeed: 0.3,
      direction: 'top-down',
      items: [
        { id: 1, title: 'What is this component?', content: 'This is a customizable accordion component with smooth animations.' },
        { id: 2, title: 'How do I use it?', content: 'Simply pass an array of items with title and content properties.' },
        { id: 3, title: 'Can I customize it?', content: 'Yes! You can control animations, allow multiple open items, and more.' }
      ],
    },
    Tabs: {
      variant: 'underline',
      underlineAnimation: true,
      animation: 'fade-in',
      tabs: [
        { id: 'tab1', label: 'Overview', content: 'This is the overview tab content with general information.' },
        { id: 'tab2', label: 'Features', content: 'Here are the key features and capabilities of this component.' },
        { id: 'tab3', label: 'Examples', content: 'Check out these practical examples and use cases.' }
      ],
    },
    Select: {
      multiple: false,
      searchable: false,
      creatable: false,
      animation: 'slide-up',
      placeholder: 'Select an option...',
      options: [
        { value: 'option1', label: 'Option 1' },
        { value: 'option2', label: 'Option 2' },
        { value: 'option3', label: 'Option 3' },
        { value: 'option4', label: 'Option 4' }
      ],
    },
  },

  // Actions
  setSelectedComponent: (component) => set({ selectedComponent: component }),
  
  updateComponentConfig: (component, config) =>
    set((state) => ({
      componentConfigs: {
        ...state.componentConfigs,
        [component]: {
          ...state.componentConfigs[component],
          ...config,
        },
      },
    })),

  resetComponentConfig: (component) =>
    set((state) => {
      const defaultConfigs = {
        Button: {
          variant: 'primary',
          size: 'md',
          animation: 'fade-in',
          interaction: 'gentle',
          disabled: false,
          text: 'Click me',
        },
        Modal: {
          size: 'md',
          animation: 'scale-in',
          backdrop: true,
          closable: true,
          title: 'Modal Title',
          content: 'This is modal content',
        },
        Carousel: {
          autoplay: true,
          interval: 3000,
          showDots: true,
          showArrows: true,
          animation: 'slide',
          items: [
            { id: 1, content: 'Slide 1', image: 'https://via.placeholder.com/400x200/3B82F6/white?text=Slide+1' },
            { id: 2, content: 'Slide 2', image: 'https://via.placeholder.com/400x200/10B981/white?text=Slide+2' },
            { id: 3, content: 'Slide 3', image: 'https://via.placeholder.com/400x200/F59E0B/white?text=Slide+3' },
          ],
        },
        Card: {
          variant: 'default',
          shadow: 'md',
          animation: 'fade-in',
          title: 'Card Title',
          content: 'This is card content with some description text.',
          showImage: true,
          imageUrl: 'https://via.placeholder.com/300x150/6366F1/white?text=Card+Image',
        },
        Toast: {
          type: 'success',
          position: 'top-right',
          animation: 'slide-in',
          autoClose: true,
          duration: 3000,
          message: 'This is a toast message!',
        },
      };

      return {
        componentConfigs: {
          ...state.componentConfigs,
          [component]: defaultConfigs[component],
        },
      };
    }),
}));

export default useComponentStore;
