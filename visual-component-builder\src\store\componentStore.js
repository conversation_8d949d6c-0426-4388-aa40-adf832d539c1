import { create } from 'zustand';

const useComponentStore = create((set) => ({
  // Current selected component
  selectedComponent: 'Button',
  
  // Component configurations
  componentConfigs: {
    Button: {
      variant: 'primary',
      size: 'md',
      animation: 'fade-in',
      interaction: 'gentle',
      disabled: false,
      text: 'Click me',
    },
    Modal: {
      size: 'md',
      animation: 'scale-in',
      backdrop: true,
      closable: true,
      title: 'Modal Title',
      content: 'This is modal content',
    },
    Carousel: {
      autoplay: true,
      interval: 3000,
      showDots: true,
      showArrows: true,
      animation: 'slide',
      items: [
        { id: 1, content: 'Slide 1', image: 'https://via.placeholder.com/400x200/3B82F6/white?text=Slide+1' },
        { id: 2, content: 'Slide 2', image: 'https://via.placeholder.com/400x200/10B981/white?text=Slide+2' },
        { id: 3, content: 'Slide 3', image: 'https://via.placeholder.com/400x200/F59E0B/white?text=Slide+3' },
      ],
    },
    Card: {
      variant: 'default',
      shadow: 'md',
      animation: 'fade-in',
      title: 'Card Title',
      content: 'This is card content with some description text.',
      showImage: true,
      imageUrl: 'https://via.placeholder.com/300x150/6366F1/white?text=Card+Image',
    },
    Toast: {
      type: 'success',
      position: 'top-right',
      animation: 'slide-in',
      autoClose: true,
      duration: 3000,
      message: 'This is a toast message!',
    },
    FeatureList: {
      animation: 'stagger-fade',
      staggerSpeed: 'medium',
      features: [
        { icon: '🚀', title: 'Fast Performance', description: 'Lightning-fast rendering and smooth animations' },
        { icon: '🎨', title: 'Beautiful Design', description: 'Modern and clean user interface components' },
        { icon: '📱', title: 'Responsive', description: 'Works perfectly on all devices and screen sizes' },
        { icon: '🔧', title: 'Customizable', description: 'Easily customize colors, sizes, and animations' }
      ],
    },
  },

  // Actions
  setSelectedComponent: (component) => set({ selectedComponent: component }),
  
  updateComponentConfig: (component, config) =>
    set((state) => ({
      componentConfigs: {
        ...state.componentConfigs,
        [component]: {
          ...state.componentConfigs[component],
          ...config,
        },
      },
    })),

  resetComponentConfig: (component) =>
    set((state) => {
      const defaultConfigs = {
        Button: {
          variant: 'primary',
          size: 'md',
          animation: 'fade-in',
          interaction: 'gentle',
          disabled: false,
          text: 'Click me',
        },
        Modal: {
          size: 'md',
          animation: 'scale-in',
          backdrop: true,
          closable: true,
          title: 'Modal Title',
          content: 'This is modal content',
        },
        Carousel: {
          autoplay: true,
          interval: 3000,
          showDots: true,
          showArrows: true,
          animation: 'slide',
          items: [
            { id: 1, content: 'Slide 1', image: 'https://via.placeholder.com/400x200/3B82F6/white?text=Slide+1' },
            { id: 2, content: 'Slide 2', image: 'https://via.placeholder.com/400x200/10B981/white?text=Slide+2' },
            { id: 3, content: 'Slide 3', image: 'https://via.placeholder.com/400x200/F59E0B/white?text=Slide+3' },
          ],
        },
        Card: {
          variant: 'default',
          shadow: 'md',
          animation: 'fade-in',
          title: 'Card Title',
          content: 'This is card content with some description text.',
          showImage: true,
          imageUrl: 'https://via.placeholder.com/300x150/6366F1/white?text=Card+Image',
        },
        Toast: {
          type: 'success',
          position: 'top-right',
          animation: 'slide-in',
          autoClose: true,
          duration: 3000,
          message: 'This is a toast message!',
        },
      };

      return {
        componentConfigs: {
          ...state.componentConfigs,
          [component]: defaultConfigs[component],
        },
      };
    }),
}));

export default useComponentStore;
