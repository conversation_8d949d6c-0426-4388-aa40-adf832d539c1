import { useEffect, useRef } from 'react';
import Prism from 'prismjs';
import 'prismjs/themes/prism-tomorrow.css';
import 'prismjs/components/prism-jsx';
import useComponentStore from '../store/componentStore';

const CodeGenerator = () => {
  const { selectedComponent, componentConfigs } = useComponentStore();
  const currentConfig = componentConfigs[selectedComponent];
  const codeRef = useRef(null);

  const generateCode = () => {
    const formatValue = (value) => {
      if (typeof value === 'string') {
        return `"${value}"`;
      }
      if (typeof value === 'boolean') {
        return value ? '{true}' : '{false}';
      }
      if (typeof value === 'number') {
        return `{${value}}`;
      }
      if (Array.isArray(value)) {
        return `{${JSON.stringify(value, null, 2)}}`;
      }
      return `{${JSON.stringify(value)}}`;
    };

    const generateProps = (config) => {
      return Object.entries(config)
        .filter(([key, value]) => {
          // Filter out default values to keep code clean
          const defaults = {
            Button: { variant: 'primary', size: 'md', animation: 'fade-in', disabled: false, text: 'Click me' },
            Modal: { size: 'md', animation: 'scale-in', backdrop: true, closable: true, title: 'Modal Title', content: 'This is modal content' },
            Carousel: { autoplay: true, interval: 3000, showDots: true, showArrows: true, animation: 'slide' },
            Card: { variant: 'default', shadow: 'md', animation: 'fade-in', title: 'Card Title', content: 'This is card content with some description text.', showImage: true },
            Toast: { type: 'success', position: 'top-right', animation: 'slide-in', autoClose: true, duration: 3000, message: 'This is a toast message!' }
          };
          
          const componentDefaults = defaults[selectedComponent] || {};
          return componentDefaults[key] !== value;
        })
        .map(([key, value]) => `  ${key}=${formatValue(value)}`)
        .join('\n');
    };

    const props = generateProps(currentConfig);
    const componentName = selectedComponent;

    if (props) {
      return `import { ${componentName} } from './components';

const MyComponent = () => {
  return (
    <${componentName}
${props}
    />
  );
};

export default MyComponent;`;
    } else {
      return `import { ${componentName} } from './components';

const MyComponent = () => {
  return <${componentName} />;
};

export default MyComponent;`;
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(generateCode());
      // You could add a toast notification here
      alert('Code copied to clipboard!');
    } catch (err) {
      console.error('Failed to copy code: ', err);
    }
  };

  useEffect(() => {
    if (codeRef.current) {
      Prism.highlightElement(codeRef.current);
    }
  }, [selectedComponent, currentConfig]);

  return (
    <div className="w-96 bg-white border-l border-gray-200 p-6 overflow-y-auto">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Generated Code</h2>
          <button
            onClick={copyToClipboard}
            className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors flex items-center space-x-1"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
            <span>Copy</span>
          </button>
        </div>

        <div className="bg-gray-900 rounded-lg overflow-hidden">
          <div className="bg-gray-800 px-4 py-2 text-sm text-gray-300 border-b border-gray-700">
            {selectedComponent}.jsx
          </div>
          <pre className="p-4 overflow-x-auto">
            <code
              ref={codeRef}
              className="language-jsx text-sm"
            >
              {generateCode()}
            </code>
          </pre>
        </div>

        {/* Usage Instructions */}
        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h3 className="text-sm font-semibold text-blue-900 mb-2">Usage Instructions</h3>
          <div className="text-sm text-blue-800 space-y-1">
            <p>1. Copy the generated code above</p>
            <p>2. Create a new component file in your project</p>
            <p>3. Paste the code and customize as needed</p>
            <p>4. Make sure to install the required dependencies:</p>
            <code className="block mt-2 p-2 bg-blue-100 rounded text-xs">
              npm install framer-motion tailwindcss
            </code>
          </div>
        </div>

        {/* Component Props Documentation */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h3 className="text-sm font-semibold text-gray-900 mb-2">Available Props</h3>
          <div className="text-xs text-gray-600 space-y-1">
            {selectedComponent === 'Button' && (
              <>
                <p><code>variant</code>: primary | secondary | success | danger | warning | outline | ghost</p>
                <p><code>size</code>: sm | md | lg | xl</p>
                <p><code>animation</code>: fade-in | slide-in | bounce-in | scale-in | none</p>
                <p><code>disabled</code>: boolean</p>
                <p><code>text</code>: string</p>
              </>
            )}
            {selectedComponent === 'Modal' && (
              <>
                <p><code>size</code>: sm | md | lg | xl | 2xl | full</p>
                <p><code>animation</code>: fade-in | slide-in | bounce-in | scale-in</p>
                <p><code>backdrop</code>: boolean</p>
                <p><code>closable</code>: boolean</p>
                <p><code>title</code>: string</p>
                <p><code>content</code>: string</p>
              </>
            )}
            {selectedComponent === 'Carousel' && (
              <>
                <p><code>autoplay</code>: boolean</p>
                <p><code>interval</code>: number (milliseconds)</p>
                <p><code>showDots</code>: boolean</p>
                <p><code>showArrows</code>: boolean</p>
                <p><code>animation</code>: slide | fade | scale</p>
                <p><code>items</code>: array of slide objects</p>
              </>
            )}
            {selectedComponent === 'Card' && (
              <>
                <p><code>variant</code>: default | elevated | outlined | filled | gradient</p>
                <p><code>shadow</code>: none | sm | md | lg | xl | 2xl</p>
                <p><code>animation</code>: fade-in | slide-in | bounce-in | scale-in | none</p>
                <p><code>title</code>: string</p>
                <p><code>content</code>: string</p>
                <p><code>showImage</code>: boolean</p>
                <p><code>imageUrl</code>: string</p>
              </>
            )}
            {selectedComponent === 'Toast' && (
              <>
                <p><code>type</code>: success | error | warning | info</p>
                <p><code>position</code>: top-left | top-center | top-right | bottom-left | bottom-center | bottom-right</p>
                <p><code>animation</code>: slide-in | fade-in | bounce-in | scale-in</p>
                <p><code>autoClose</code>: boolean</p>
                <p><code>duration</code>: number (milliseconds)</p>
                <p><code>message</code>: string</p>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CodeGenerator;
