import { motion } from 'framer-motion';

const Card = ({
  variant = 'default',
  shadow = 'md',
  animation = 'fade-in',
  title = 'Card Title',
  content = 'This is card content with some description text.',
  showImage = true,
  imageUrl = 'https://via.placeholder.com/300x150/6366F1/white?text=Card+Image',
  className = '',
  ...props
}) => {
  // Variant styles
  const variantStyles = {
    default: 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700',
    elevated: 'bg-white dark:bg-gray-800 border-0',
    outlined: 'bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600',
    filled: 'bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-700',
    gradient: 'bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-700',
  };

  // Shadow styles
  const shadowStyles = {
    none: 'shadow-none',
    sm: 'shadow-sm',
    md: 'shadow-md',
    lg: 'shadow-lg',
    xl: 'shadow-xl',
    '2xl': 'shadow-2xl',
  };

  // Animation variants
  const animationVariants = {
    'fade-in': {
      initial: { opacity: 0 },
      animate: { opacity: 1 },
      transition: { duration: 0.5 }
    },
    'slide-in': {
      initial: { y: 20, opacity: 0 },
      animate: { y: 0, opacity: 1 },
      transition: { duration: 0.4 }
    },
    'scale-in': {
      initial: { scale: 0.9, opacity: 0 },
      animate: { scale: 1, opacity: 1 },
      transition: { duration: 0.3 }
    },
    'bounce-in': {
      initial: { scale: 0.3, opacity: 0 },
      animate: { scale: 1, opacity: 1 },
      transition: { 
        duration: 0.6,
        type: "spring",
        stiffness: 260,
        damping: 20
      }
    },
    none: {}
  };

  const currentAnimation = animationVariants[animation] || animationVariants['fade-in'];

  const baseStyles = `
    rounded-lg overflow-hidden transition-all duration-200
    hover:shadow-lg hover:scale-105
    ${variantStyles[variant]}
    ${shadowStyles[shadow]}
    ${className}
  `;

  return (
    <motion.div
      className={baseStyles}
      initial={currentAnimation.initial}
      animate={currentAnimation.animate}
      transition={currentAnimation.transition}
      {...props}
    >
      {/* Image */}
      {showImage && imageUrl && (
        <div className="aspect-video w-full overflow-hidden">
          <img
            src={imageUrl}
            alt={title}
            className="w-full h-full object-cover transition-transform duration-300 hover:scale-110"
          />
        </div>
      )}

      {/* Content */}
      <div className="p-6">
        {/* Title */}
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          {title}
        </h3>

        {/* Description */}
        <p className="text-gray-600 dark:text-gray-300 mb-4 leading-relaxed">
          {content}
        </p>

        {/* Action buttons */}
        <div className="flex space-x-3">
          <button className="px-4 py-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white rounded-lg transition-colors text-sm font-medium">
            Learn More
          </button>
          <button className="px-4 py-2 text-gray-600 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-sm font-medium">
            Share
          </button>
        </div>
      </div>
    </motion.div>
  );
};

export default Card;
