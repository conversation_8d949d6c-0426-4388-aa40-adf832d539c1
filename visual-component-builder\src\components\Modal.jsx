import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';

const Modal = ({
  size = 'md',
  animation = 'scale-in',
  backdrop = true,
  closable = true,
  title = 'Modal Title',
  content = 'This is modal content',
  className = '',
  ...props
}) => {
  const [isOpen, setIsOpen] = useState(false);

  // Size styles
  const sizeStyles = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    full: 'max-w-full mx-4',
  };

  // Animation variants
  const animationVariants = {
    'fade-in': {
      initial: { opacity: 0 },
      animate: { opacity: 1 },
      exit: { opacity: 0 },
      transition: { duration: 0.3 }
    },
    'scale-in': {
      initial: { scale: 0.8, opacity: 0 },
      animate: { scale: 1, opacity: 1 },
      exit: { scale: 0.8, opacity: 0 },
      transition: { duration: 0.3 }
    },
    'slide-in': {
      initial: { y: -50, opacity: 0 },
      animate: { y: 0, opacity: 1 },
      exit: { y: -50, opacity: 0 },
      transition: { duration: 0.3 }
    },
    'bounce-in': {
      initial: { scale: 0.3, opacity: 0 },
      animate: { scale: 1, opacity: 1 },
      exit: { scale: 0.3, opacity: 0 },
      transition: { 
        duration: 0.6,
        type: "spring",
        stiffness: 260,
        damping: 20
      }
    },
  };

  const currentAnimation = animationVariants[animation] || animationVariants['scale-in'];

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget && closable) {
      setIsOpen(false);
    }
  };

  return (
    <div className="space-y-4">
      {/* Trigger Button */}
      <button
        onClick={() => setIsOpen(true)}
        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
      >
        Open Modal
      </button>

      {/* Modal */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className={`fixed inset-0 z-50 flex items-center justify-center p-4 ${
              backdrop ? 'bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70' : ''
            }`}
            onClick={handleBackdropClick}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
          >
            <motion.div
              className={`
                bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full
                ${sizeStyles[size]}
                ${className}
              `}
              onClick={(e) => e.stopPropagation()}
              initial={currentAnimation.initial}
              animate={currentAnimation.animate}
              exit={currentAnimation.exit}
              transition={currentAnimation.transition}
              {...props}
            >
              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{title}</h3>
                {closable && (
                  <button
                    onClick={() => setIsOpen(false)}
                    className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                )}
              </div>

              {/* Content */}
              <div className="p-6">
                <p className="text-gray-600 dark:text-gray-300">{content}</p>
              </div>

              {/* Footer */}
              <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
                <button
                  onClick={() => setIsOpen(false)}
                  className="px-4 py-2 text-gray-600 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => setIsOpen(false)}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white rounded-lg transition-colors"
                >
                  Confirm
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default Modal;
