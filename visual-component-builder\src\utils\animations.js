// Enhanced animation configurations for Framer Motion

export const animationPresets = {
  // Basic animations
  'fade-in': {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
    transition: { duration: 0.5, ease: "easeOut" }
  },
  
  'slide-in': {
    initial: { y: -20, opacity: 0 },
    animate: { y: 0, opacity: 1 },
    exit: { y: -20, opacity: 0 },
    transition: { duration: 0.3, ease: "easeOut" }
  },
  
  'slide-up': {
    initial: { y: 20, opacity: 0 },
    animate: { y: 0, opacity: 1 },
    exit: { y: 20, opacity: 0 },
    transition: { duration: 0.3, ease: "easeOut" }
  },
  
  'slide-left': {
    initial: { x: 20, opacity: 0 },
    animate: { x: 0, opacity: 1 },
    exit: { x: 20, opacity: 0 },
    transition: { duration: 0.3, ease: "easeOut" }
  },
  
  'slide-right': {
    initial: { x: -20, opacity: 0 },
    animate: { x: 0, opacity: 1 },
    exit: { x: -20, opacity: 0 },
    transition: { duration: 0.3, ease: "easeOut" }
  },
  
  'scale-in': {
    initial: { scale: 0.8, opacity: 0 },
    animate: { scale: 1, opacity: 1 },
    exit: { scale: 0.8, opacity: 0 },
    transition: { duration: 0.3, ease: "easeOut" }
  },
  
  'bounce-in': {
    initial: { scale: 0.3, opacity: 0 },
    animate: { scale: 1, opacity: 1 },
    exit: { scale: 0.3, opacity: 0 },
    transition: { 
      duration: 0.6,
      type: "spring",
      stiffness: 260,
      damping: 20
    }
  },
  
  // Advanced spring animations
  'spring-gentle': {
    initial: { scale: 0.9, opacity: 0 },
    animate: { scale: 1, opacity: 1 },
    exit: { scale: 0.9, opacity: 0 },
    transition: {
      type: "spring",
      stiffness: 100,
      damping: 15,
      mass: 1
    }
  },
  
  'spring-bouncy': {
    initial: { scale: 0.5, opacity: 0 },
    animate: { scale: 1, opacity: 1 },
    exit: { scale: 0.5, opacity: 0 },
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 10,
      mass: 0.8
    }
  },
  
  'spring-wobbly': {
    initial: { scale: 0.8, rotate: -5, opacity: 0 },
    animate: { scale: 1, rotate: 0, opacity: 1 },
    exit: { scale: 0.8, rotate: 5, opacity: 0 },
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 8,
      mass: 1.2
    }
  },
  
  // Rotation animations
  'rotate-in': {
    initial: { rotate: -180, scale: 0.8, opacity: 0 },
    animate: { rotate: 0, scale: 1, opacity: 1 },
    exit: { rotate: 180, scale: 0.8, opacity: 0 },
    transition: { duration: 0.5, ease: "easeOut" }
  },
  
  'flip-in': {
    initial: { rotateY: -90, opacity: 0 },
    animate: { rotateY: 0, opacity: 1 },
    exit: { rotateY: 90, opacity: 0 },
    transition: { duration: 0.4, ease: "easeOut" }
  },
  
  // Complex animations
  'zoom-bounce': {
    initial: { scale: 0, opacity: 0 },
    animate: { 
      scale: [0, 1.1, 1], 
      opacity: [0, 1, 1] 
    },
    exit: { scale: 0, opacity: 0 },
    transition: { 
      duration: 0.6,
      times: [0, 0.7, 1],
      ease: "easeOut"
    }
  },
  
  'slide-bounce': {
    initial: { x: -100, opacity: 0 },
    animate: { 
      x: [0, 10, 0], 
      opacity: 1 
    },
    exit: { x: 100, opacity: 0 },
    transition: { 
      duration: 0.5,
      times: [0, 0.8, 1],
      ease: "easeOut"
    }
  },
  
  // Stagger-ready animations
  'stagger-fade': {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
    transition: { duration: 0.4, ease: "easeOut" }
  },
  
  'stagger-scale': {
    initial: { scale: 0, opacity: 0 },
    animate: { scale: 1, opacity: 1 },
    exit: { scale: 0, opacity: 0 },
    transition: { duration: 0.3, ease: "easeOut" }
  },
  
  // No animation
  'none': {
    initial: {},
    animate: {},
    exit: {},
    transition: {}
  }
};

// Stagger configurations
export const staggerConfigs = {
  'fast': { delayChildren: 0.1, staggerChildren: 0.05 },
  'medium': { delayChildren: 0.2, staggerChildren: 0.1 },
  'slow': { delayChildren: 0.3, staggerChildren: 0.15 },
  'very-slow': { delayChildren: 0.5, staggerChildren: 0.2 }
};

// Hover and tap animations
export const interactionAnimations = {
  'gentle': {
    whileHover: { scale: 1.02, transition: { duration: 0.2 } },
    whileTap: { scale: 0.98, transition: { duration: 0.1 } }
  },
  'bouncy': {
    whileHover: { scale: 1.05, transition: { type: "spring", stiffness: 400 } },
    whileTap: { scale: 0.95, transition: { duration: 0.1 } }
  },
  'lift': {
    whileHover: { y: -2, scale: 1.02, transition: { duration: 0.2 } },
    whileTap: { y: 0, scale: 0.98, transition: { duration: 0.1 } }
  },
  'rotate': {
    whileHover: { rotate: 5, scale: 1.02, transition: { duration: 0.2 } },
    whileTap: { rotate: -5, scale: 0.98, transition: { duration: 0.1 } }
  },
  'none': {
    whileHover: {},
    whileTap: {}
  }
};

// Utility function to get animation config
export const getAnimationConfig = (animationType, options = {}) => {
  const baseConfig = animationPresets[animationType] || animationPresets['fade-in'];
  
  // Apply custom duration if provided
  if (options.duration) {
    return {
      ...baseConfig,
      transition: {
        ...baseConfig.transition,
        duration: options.duration
      }
    };
  }
  
  // Apply custom delay if provided
  if (options.delay) {
    return {
      ...baseConfig,
      transition: {
        ...baseConfig.transition,
        delay: options.delay
      }
    };
  }
  
  return baseConfig;
};

// Utility function to get interaction animations
export const getInteractionConfig = (interactionType = 'gentle', disabled = false) => {
  if (disabled) {
    return { whileHover: {}, whileTap: {} };
  }
  
  return interactionAnimations[interactionType] || interactionAnimations['gentle'];
};
