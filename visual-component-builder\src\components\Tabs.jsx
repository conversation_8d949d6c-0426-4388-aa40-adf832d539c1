import { motion } from 'framer-motion';
import { useState } from 'react';
import { getAnimationConfig } from '../utils/animations';

const Tabs = ({
  tabs = [
    { id: 'tab1', label: 'Overview', content: 'This is the overview tab content with general information.' },
    { id: 'tab2', label: 'Features', content: 'Here are the key features and capabilities of this component.' },
    { id: 'tab3', label: 'Examples', content: 'Check out these practical examples and use cases.' }
  ],
  defaultTab = null,
  underlineAnimation = true,
  variant = 'underline',
  animation = 'fade-in',
  className = '',
  ...props
}) => {
  const [activeTab, setActiveTab] = useState(defaultTab || tabs[0]?.id);

  const animationConfig = getAnimationConfig(animation);

  const activeTabContent = tabs.find(tab => tab.id === activeTab)?.content || '';

  const tabVariants = {
    underline: {
      base: 'px-4 py-2 text-sm font-medium transition-colors relative',
      active: 'text-blue-600 dark:text-blue-400',
      inactive: 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
    },
    pills: {
      base: 'px-4 py-2 text-sm font-medium rounded-lg transition-colors',
      active: 'bg-blue-600 text-white',
      inactive: 'text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800'
    },
    buttons: {
      base: 'px-4 py-2 text-sm font-medium border transition-colors',
      active: 'bg-blue-600 text-white border-blue-600',
      inactive: 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'
    }
  };

  const currentVariant = tabVariants[variant] || tabVariants.underline;

  return (
    <div className={`w-full ${className}`} {...props}>
      {/* Tab Headers */}
      <div className={`flex space-x-1 ${variant === 'underline' ? 'border-b border-gray-200 dark:border-gray-700' : ''}`}>
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`
              ${currentVariant.base}
              ${activeTab === tab.id ? currentVariant.active : currentVariant.inactive}
            `}
          >
            {tab.label}
            
            {/* Underline Animation */}
            {variant === 'underline' && underlineAnimation && activeTab === tab.id && (
              <motion.div
                className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-600 dark:bg-blue-400"
                layoutId="activeTab"
                initial={false}
                transition={{ type: "spring", stiffness: 500, damping: 30 }}
              />
            )}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="mt-4">
        <motion.div
          key={activeTab}
          initial={animationConfig.initial}
          animate={animationConfig.animate}
          transition={animationConfig.transition}
          className="p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700"
        >
          <p className="text-gray-700 dark:text-gray-300">
            {activeTabContent}
          </p>
        </motion.div>
      </div>
    </div>
  );
};

export default Tabs;
