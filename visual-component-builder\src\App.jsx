import ControlPanel from './components/ControlPanel';
import Preview from './components/Preview';
import CodeGenerator from './components/CodeGenerator';
import ThemeToggle from './components/ThemeToggle';
import { ThemeProvider } from './contexts/ThemeContext';

function App() {
  return (
    <ThemeProvider>
      <div className="h-screen flex bg-gray-100 dark:bg-gray-900">
        {/* Control Panel */}
        <ControlPanel />

        {/* Preview Area */}
        <Preview />

        {/* Code Generator */}
        <CodeGenerator />

        {/* Theme Toggle - Fixed position */}
        <div className="fixed top-4 right-4 z-50">
          <ThemeToggle />
        </div>
      </div>
    </ThemeProvider>
  );
}

export default App;
